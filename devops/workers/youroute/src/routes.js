/**
 * YouRoute Routing Configuration
 *
 * deploymentStrategy
 *  - worker-routes: 路由目标是 Cloudflare Worker
 *  - origin-rules: 路由目标是其他系统或 Cloudflare Pages
 */

export const ROUTES = [
  {
    deploymentStrategy: 'origin-rules',
    name: 'youapi',
    paths: ['/api', '/webhook', '/innerapi', '/checkout-success', '/auth', '/files'],
    target: {
      type: 'custom',
      domains: {
        production: 'youapi.youmind.com',
        preview: 'youapi-preview.youmind.com',
      },
    },
    description: 'API Services',
    headers: {
      'X-Forwarded-Host': 'http.request.headers["host"][0]',
    },
  },

  {
    deploymentStrategy: 'worker-routes',
    name: 'youadmin',
    paths: ['/admin'],
    description: 'Admin Dashboard',
  },

  {
    deploymentStrategy: 'origin-rules',
    name: 'youweb',
    paths: [
      '/boards',
      '/snips',
      '/thoughts',
      '/static',
      '/settings',
      '/500',
      '/mobile',
      '/print-view',
    ],
    target: {
      type: 'custom',
      domains: {
        production: 'youweb.youmind.com',
        preview: 'youweb-preview.youmind.com',
      },
    },
    description: 'Boards, Snips, Thoughts',
  },

  {
    deploymentStrategy: 'worker-routes',
    name: 'youhome',
    paths: [
      '/_next',
      '/404',
      '/use-cases',
      '/overview',
      '/blog',
      '/sign-in',
      '/privacy',
      '/terms',
      '/download-extension',
      '/email',
      '/pricing',
      '/video-tools',
      '/campaign',
      '/updates',

      // SEO
      '/robots.txt',
      '/sitemap.xml',
      '/blog-sitemap.xml',
      '/global-sitemap.xml',
      '/update-sitemap.xml',
      '/use-cases-sitemap.xml',
    ],
    description: 'Use Cases, Overview, Auth',
  },
];
