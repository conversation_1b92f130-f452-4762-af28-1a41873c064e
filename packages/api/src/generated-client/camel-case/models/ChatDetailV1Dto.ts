/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ChatDetailV1DtoMessages } from './ChatDetailV1DtoMessages';
import {
  ChatDetailV1DtoMessagesFromJSON,
  ChatDetailV1DtoMessagesFromJSONTyped,
  ChatDetailV1DtoMessagesToJSON,
  ChatDetailV1DtoMessagesToJSONTyped,
} from './ChatDetailV1DtoMessages';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatO<PERSON>inDtoFromJSONTyped,
  Chat<PERSON><PERSON>inDtoToJSON,
  Chat<PERSON><PERSON>inDtoToJSONTyped,
} from './ChatOriginDto';

/**
 *
 * @export
 * @interface ChatDetailV1Dto
 */
export interface ChatDetailV1Dto {
  /**
   * Chat ID
   * @type {string}
   * @memberof ChatDetailV1Dto
   */
  id: string;
  /**
   * Creator user ID
   * @type {string}
   * @memberof ChatDetailV1Dto
   */
  creatorId: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof ChatDetailV1Dto
   */
  createdAt: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof ChatDetailV1Dto
   */
  updatedAt: Date;
  /**
   * Chat title
   * @type {string}
   * @memberof ChatDetailV1Dto
   */
  title: string;
  /**
   * Chat origin context
   * @type {ChatOriginDto}
   * @memberof ChatDetailV1Dto
   */
  origin: ChatOriginDto;
  /**
   * Board ID if applicable
   * @type {string}
   * @memberof ChatDetailV1Dto
   */
  boardId?: string;
  /**
   *
   * @type {ChatDetailV1DtoMessages}
   * @memberof ChatDetailV1Dto
   */
  messages: ChatDetailV1DtoMessages;
  /**
   * Associated board IDs
   * @type {Array<string>}
   * @memberof ChatDetailV1Dto
   */
  boardIds?: Array<string>;
  /**
   * Board item information
   * @type {object}
   * @memberof ChatDetailV1Dto
   */
  boardItem?: object;
}

/**
 * Check if a given object implements the ChatDetailV1Dto interface.
 */
export function instanceOfChatDetailV1Dto(value: object): value is ChatDetailV1Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('creatorId' in value) || value.creatorId === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('title' in value) || value.title === undefined) return false;
  if (!('origin' in value) || value.origin === undefined) return false;
  if (!('messages' in value) || value.messages === undefined) return false;
  return true;
}

export function ChatDetailV1DtoFromJSON(json: any): ChatDetailV1Dto {
  return ChatDetailV1DtoFromJSONTyped(json, false);
}

export function ChatDetailV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ChatDetailV1Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    creatorId: json.creatorId,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    title: json.title,
    origin: ChatOriginDtoFromJSON(json.origin),
    boardId: json.boardId == null ? undefined : json.boardId,
    messages: ChatDetailV1DtoMessagesFromJSON(json.messages),
    boardIds: json.boardIds == null ? undefined : json.boardIds,
    boardItem: json.boardItem == null ? undefined : json.boardItem,
  };
}

export function ChatDetailV1DtoToJSON(json: any): ChatDetailV1Dto {
  return ChatDetailV1DtoToJSONTyped(json, false);
}

export function ChatDetailV1DtoToJSONTyped(
  value?: ChatDetailV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    creatorId: value.creatorId,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    title: value.title,
    origin: ChatOriginDtoToJSON(value.origin),
    boardId: value.boardId,
    messages: ChatDetailV1DtoMessagesToJSON(value.messages),
    boardIds: value.boardIds,
    boardItem: value.boardItem,
  };
}
