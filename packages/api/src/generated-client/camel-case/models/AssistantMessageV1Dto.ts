/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { LLMs } from './LLMs';
import { LLMsFromJSON, LLMsFromJSONTyped, LLMsToJSON, LLMsToJSONTyped } from './LLMs';
import type { MessageRoleEnum } from './MessageRoleEnum';
import {
  MessageRoleEnumFromJSON,
  MessageRoleEnumFromJSONTyped,
  MessageRoleEnumToJSON,
  MessageRoleEnumToJSONTyped,
} from './MessageRoleEnum';
import type { MessageStatusEnum } from './MessageStatusEnum';
import {
  MessageStatusEnumFromJSON,
  MessageStatusEnumFromJSONTyped,
  MessageStatusEnumToJSON,
  MessageStatusEnumToJSONTyped,
} from './MessageStatusEnum';

/**
 *
 * @export
 * @interface AssistantMessageV1Dto
 */
export interface AssistantMessageV1Dto {
  /**
   * Message ID
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  id: string;
  /**
   * Chat ID
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  chatId: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof AssistantMessageV1Dto
   */
  createdAt: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof AssistantMessageV1Dto
   */
  updatedAt: Date;
  /**
   * Message role
   * @type {MessageRoleEnum}
   * @memberof AssistantMessageV1Dto
   */
  role: MessageRoleEnum;
  /**
   * Message status
   * @type {MessageStatusEnum}
   * @memberof AssistantMessageV1Dto
   */
  status: MessageStatusEnum;
  /**
   * AI model used
   * @type {LLMs}
   * @memberof AssistantMessageV1Dto
   */
  model: LLMs;
  /**
   * Trace ID for debugging
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  traceId: string;
  /**
   * Message content (extracted from content blocks)
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  content: string;
  /**
   * Reasoning text (extracted from reasoning blocks)
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  reasoning?: string;
  /**
   * Message events (converted from tool blocks)
   * @type {Array<object>}
   * @memberof AssistantMessageV1Dto
   */
  events: Array<object>;
  /**
   * Error information if any
   * @type {object}
   * @memberof AssistantMessageV1Dto
   */
  error?: object;
  /**
   * Reasoning start timestamp
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  reasoningBeginAt?: string;
  /**
   * Reasoning end timestamp
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  reasoningEndAt?: string;
}

/**
 * Check if a given object implements the AssistantMessageV1Dto interface.
 */
export function instanceOfAssistantMessageV1Dto(value: object): value is AssistantMessageV1Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('chatId' in value) || value.chatId === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('role' in value) || value.role === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('model' in value) || value.model === undefined) return false;
  if (!('traceId' in value) || value.traceId === undefined) return false;
  if (!('content' in value) || value.content === undefined) return false;
  if (!('events' in value) || value.events === undefined) return false;
  return true;
}

export function AssistantMessageV1DtoFromJSON(json: any): AssistantMessageV1Dto {
  return AssistantMessageV1DtoFromJSONTyped(json, false);
}

export function AssistantMessageV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): AssistantMessageV1Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    chatId: json.chatId,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    role: MessageRoleEnumFromJSON(json.role),
    status: MessageStatusEnumFromJSON(json.status),
    model: LLMsFromJSON(json.model),
    traceId: json.traceId,
    content: json.content,
    reasoning: json.reasoning == null ? undefined : json.reasoning,
    events: json.events,
    error: json.error == null ? undefined : json.error,
    reasoningBeginAt: json.reasoningBeginAt == null ? undefined : json.reasoningBeginAt,
    reasoningEndAt: json.reasoningEndAt == null ? undefined : json.reasoningEndAt,
  };
}

export function AssistantMessageV1DtoToJSON(json: any): AssistantMessageV1Dto {
  return AssistantMessageV1DtoToJSONTyped(json, false);
}

export function AssistantMessageV1DtoToJSONTyped(
  value?: AssistantMessageV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    chatId: value.chatId,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    role: MessageRoleEnumToJSON(value.role),
    status: MessageStatusEnumToJSON(value.status),
    model: LLMsToJSON(value.model),
    traceId: value.traceId,
    content: value.content,
    reasoning: value.reasoning,
    events: value.events,
    error: value.error,
    reasoningBeginAt: value.reasoningBeginAt,
    reasoningEndAt: value.reasoningEndAt,
  };
}
