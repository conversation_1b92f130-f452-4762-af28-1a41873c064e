/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CompletionBlockStatusEnum } from './CompletionBlockStatusEnum';
import {
  CompletionBlockStatusEnumFromJSON,
  CompletionBlockStatusEnumFromJSONTyped,
  CompletionBlockStatusEnumToJSON,
  CompletionBlockStatusEnumToJSONTyped,
} from './CompletionBlockStatusEnum';
import type { CompletionBlockTypeEnum } from './CompletionBlockTypeEnum';
import {
  CompletionBlockTypeEnumFromJSON,
  CompletionBlockTypeEnumFromJSONTyped,
  CompletionBlockTypeEnumToJSON,
  CompletionBlockTypeEnumToJSONTyped,
} from './CompletionBlockTypeEnum';

/**
 *
 * @export
 * @interface ToolBlockV2Dto
 */
export interface ToolBlockV2Dto {
  /**
   * Block ID
   * @type {string}
   * @memberof ToolBlockV2Dto
   */
  id: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof ToolBlockV2Dto
   */
  createdAt: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof ToolBlockV2Dto
   */
  updatedAt: Date;
  /**
   * Block type
   * @type {CompletionBlockTypeEnum}
   * @memberof ToolBlockV2Dto
   */
  type: CompletionBlockTypeEnum;
  /**
   * Block status
   * @type {CompletionBlockStatusEnum}
   * @memberof ToolBlockV2Dto
   */
  status: CompletionBlockStatusEnum;
  /**
   * Message ID this block belongs to
   * @type {string}
   * @memberof ToolBlockV2Dto
   */
  messageId: string;
  /**
   * Extra metadata
   * @type {object}
   * @memberof ToolBlockV2Dto
   */
  extra?: object;
  /**
   * Tool ID
   * @type {string}
   * @memberof ToolBlockV2Dto
   */
  toolId: string;
  /**
   * Tool name
   * @type {string}
   * @memberof ToolBlockV2Dto
   */
  toolName: string;
  /**
   * Tool arguments
   * @type {object}
   * @memberof ToolBlockV2Dto
   */
  toolArguments?: object;
  /**
   * Tool execution result
   * @type {object}
   * @memberof ToolBlockV2Dto
   */
  toolResult?: object;
  /**
   * Tool response text
   * @type {string}
   * @memberof ToolBlockV2Dto
   */
  toolResponse?: string;
  /**
   * Tool generation time in milliseconds
   * @type {number}
   * @memberof ToolBlockV2Dto
   */
  toolGenerateElapsedMs?: number;
  /**
   * Tool execution time in milliseconds
   * @type {number}
   * @memberof ToolBlockV2Dto
   */
  toolExecuteElapsedMs?: number;
}

/**
 * Check if a given object implements the ToolBlockV2Dto interface.
 */
export function instanceOfToolBlockV2Dto(value: object): value is ToolBlockV2Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('type' in value) || value.type === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('messageId' in value) || value.messageId === undefined) return false;
  if (!('toolId' in value) || value.toolId === undefined) return false;
  if (!('toolName' in value) || value.toolName === undefined) return false;
  return true;
}

export function ToolBlockV2DtoFromJSON(json: any): ToolBlockV2Dto {
  return ToolBlockV2DtoFromJSONTyped(json, false);
}

export function ToolBlockV2DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ToolBlockV2Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    type: CompletionBlockTypeEnumFromJSON(json.type),
    status: CompletionBlockStatusEnumFromJSON(json.status),
    messageId: json.messageId,
    extra: json.extra == null ? undefined : json.extra,
    toolId: json.toolId,
    toolName: json.toolName,
    toolArguments: json.toolArguments == null ? undefined : json.toolArguments,
    toolResult: json.toolResult == null ? undefined : json.toolResult,
    toolResponse: json.toolResponse == null ? undefined : json.toolResponse,
    toolGenerateElapsedMs:
      json.toolGenerateElapsedMs == null ? undefined : json.toolGenerateElapsedMs,
    toolExecuteElapsedMs: json.toolExecuteElapsedMs == null ? undefined : json.toolExecuteElapsedMs,
  };
}

export function ToolBlockV2DtoToJSON(json: any): ToolBlockV2Dto {
  return ToolBlockV2DtoToJSONTyped(json, false);
}

export function ToolBlockV2DtoToJSONTyped(
  value?: ToolBlockV2Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    type: CompletionBlockTypeEnumToJSON(value.type),
    status: CompletionBlockStatusEnumToJSON(value.status),
    messageId: value.messageId,
    extra: value.extra,
    toolId: value.toolId,
    toolName: value.toolName,
    toolArguments: value.toolArguments,
    toolResult: value.toolResult,
    toolResponse: value.toolResponse,
    toolGenerateElapsedMs: value.toolGenerateElapsedMs,
    toolExecuteElapsedMs: value.toolExecuteElapsedMs,
  };
}
