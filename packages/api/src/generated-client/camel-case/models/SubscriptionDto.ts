/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { BillingInterval } from './BillingInterval';
import {
  BillingIntervalFromJSON,
  BillingIntervalFromJSONTyped,
  BillingIntervalToJSON,
  BillingIntervalToJSONTyped,
} from './BillingInterval';
import type { RenewChangeDto } from './RenewChangeDto';
import {
  RenewChangeDtoFromJSON,
  RenewChangeDtoFromJSONTyped,
  RenewChangeDtoToJSON,
  RenewChangeDtoToJSONTyped,
} from './RenewChangeDto';
import type { SubscriptionProductTier } from './SubscriptionProductTier';
import {
  SubscriptionProductTierFromJSON,
  SubscriptionProductTierFromJSONTyped,
  SubscriptionProductTierToJSON,
  SubscriptionProductTierToJSONTyped,
} from './SubscriptionProductTier';

/**
 *
 * @export
 * @interface SubscriptionDto
 */
export interface SubscriptionDto {
  /**
   * 订阅 ID
   * @type {string}
   * @memberof SubscriptionDto
   */
  id: string;
  /**
   * 产品等级
   * @type {SubscriptionProductTier}
   * @memberof SubscriptionDto
   */
  productTier: SubscriptionProductTier;
  /**
   * 计费周期
   * @type {BillingInterval}
   * @memberof SubscriptionDto
   */
  billingInterval: BillingInterval;
  /**
   * 状态
   * @type {string}
   * @memberof SubscriptionDto
   */
  status: string;
  /**
   * 续期周期锚点
   * @type {Date}
   * @memberof SubscriptionDto
   */
  renewCycleAnchor: Date;
  /**
   * 当前周期开始时间
   * @type {Date}
   * @memberof SubscriptionDto
   */
  currentPeriodStart: Date;
  /**
   * 当前周期结束时间
   * @type {Date}
   * @memberof SubscriptionDto
   */
  currentPeriodEnd: Date;
  /**
   * 是否到期取消
   * @type {boolean}
   * @memberof SubscriptionDto
   */
  cancelAtPeriodEnd: boolean;
  /**
   * 续期变更
   * @type {RenewChangeDto}
   * @memberof SubscriptionDto
   */
  renewChange?: RenewChangeDto;
  /**
   * 提供商
   * @type {string}
   * @memberof SubscriptionDto
   */
  provider: string;
  /**
   * 外部 ID
   * @type {string}
   * @memberof SubscriptionDto
   */
  externalId: string;
  /**
   * 未支付账单 URL
   * @type {string}
   * @memberof SubscriptionDto
   */
  unpaidInvoiceUrl?: string;
}

/**
 * Check if a given object implements the SubscriptionDto interface.
 */
export function instanceOfSubscriptionDto(value: object): value is SubscriptionDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('productTier' in value) || value.productTier === undefined) return false;
  if (!('billingInterval' in value) || value.billingInterval === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('renewCycleAnchor' in value) || value.renewCycleAnchor === undefined) return false;
  if (!('currentPeriodStart' in value) || value.currentPeriodStart === undefined) return false;
  if (!('currentPeriodEnd' in value) || value.currentPeriodEnd === undefined) return false;
  if (!('cancelAtPeriodEnd' in value) || value.cancelAtPeriodEnd === undefined) return false;
  if (!('provider' in value) || value.provider === undefined) return false;
  if (!('externalId' in value) || value.externalId === undefined) return false;
  return true;
}

export function SubscriptionDtoFromJSON(json: any): SubscriptionDto {
  return SubscriptionDtoFromJSONTyped(json, false);
}

export function SubscriptionDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): SubscriptionDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    productTier: SubscriptionProductTierFromJSON(json.productTier),
    billingInterval: BillingIntervalFromJSON(json.billingInterval),
    status: json.status,
    renewCycleAnchor: new Date(json.renewCycleAnchor),
    currentPeriodStart: new Date(json.currentPeriodStart),
    currentPeriodEnd: new Date(json.currentPeriodEnd),
    cancelAtPeriodEnd: json.cancelAtPeriodEnd,
    renewChange: json.renewChange == null ? undefined : RenewChangeDtoFromJSON(json.renewChange),
    provider: json.provider,
    externalId: json.externalId,
    unpaidInvoiceUrl: json.unpaidInvoiceUrl == null ? undefined : json.unpaidInvoiceUrl,
  };
}

export function SubscriptionDtoToJSON(json: any): SubscriptionDto {
  return SubscriptionDtoToJSONTyped(json, false);
}

export function SubscriptionDtoToJSONTyped(
  value?: SubscriptionDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    productTier: SubscriptionProductTierToJSON(value.productTier),
    billingInterval: BillingIntervalToJSON(value.billingInterval),
    status: value.status,
    renewCycleAnchor: value.renewCycleAnchor.toISOString(),
    currentPeriodStart: value.currentPeriodStart.toISOString(),
    currentPeriodEnd: value.currentPeriodEnd.toISOString(),
    cancelAtPeriodEnd: value.cancelAtPeriodEnd,
    renewChange: RenewChangeDtoToJSON(value.renewChange),
    provider: value.provider,
    externalId: value.externalId,
    unpaidInvoiceUrl: value.unpaidInvoiceUrl,
  };
}
