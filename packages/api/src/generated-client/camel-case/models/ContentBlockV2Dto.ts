/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CompletionBlockStatusEnum } from './CompletionBlockStatusEnum';
import {
  CompletionBlockStatusEnumFromJSON,
  CompletionBlockStatusEnumFromJSONTyped,
  CompletionBlockStatusEnumToJSON,
  CompletionBlockStatusEnumToJSONTyped,
} from './CompletionBlockStatusEnum';
import type { CompletionBlockTypeEnum } from './CompletionBlockTypeEnum';
import {
  CompletionBlockTypeEnumFromJSON,
  CompletionBlockTypeEnumFromJSONTyped,
  CompletionBlockTypeEnumToJSON,
  CompletionBlockTypeEnumToJSONTyped,
} from './CompletionBlockTypeEnum';

/**
 *
 * @export
 * @interface ContentBlockV2Dto
 */
export interface ContentBlockV2Dto {
  /**
   * Block ID
   * @type {string}
   * @memberof ContentBlockV2Dto
   */
  id: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof ContentBlockV2Dto
   */
  createdAt: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof ContentBlockV2Dto
   */
  updatedAt: Date;
  /**
   * Block type
   * @type {CompletionBlockTypeEnum}
   * @memberof ContentBlockV2Dto
   */
  type: CompletionBlockTypeEnum;
  /**
   * Block status
   * @type {CompletionBlockStatusEnum}
   * @memberof ContentBlockV2Dto
   */
  status: CompletionBlockStatusEnum;
  /**
   * Message ID this block belongs to
   * @type {string}
   * @memberof ContentBlockV2Dto
   */
  messageId: string;
  /**
   * Extra metadata
   * @type {object}
   * @memberof ContentBlockV2Dto
   */
  extra?: object;
  /**
   * Content data
   * @type {string}
   * @memberof ContentBlockV2Dto
   */
  data: string;
}

/**
 * Check if a given object implements the ContentBlockV2Dto interface.
 */
export function instanceOfContentBlockV2Dto(value: object): value is ContentBlockV2Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('type' in value) || value.type === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('messageId' in value) || value.messageId === undefined) return false;
  if (!('data' in value) || value.data === undefined) return false;
  return true;
}

export function ContentBlockV2DtoFromJSON(json: any): ContentBlockV2Dto {
  return ContentBlockV2DtoFromJSONTyped(json, false);
}

export function ContentBlockV2DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ContentBlockV2Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    type: CompletionBlockTypeEnumFromJSON(json.type),
    status: CompletionBlockStatusEnumFromJSON(json.status),
    messageId: json.messageId,
    extra: json.extra == null ? undefined : json.extra,
    data: json.data,
  };
}

export function ContentBlockV2DtoToJSON(json: any): ContentBlockV2Dto {
  return ContentBlockV2DtoToJSONTyped(json, false);
}

export function ContentBlockV2DtoToJSONTyped(
  value?: ContentBlockV2Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    type: CompletionBlockTypeEnumToJSON(value.type),
    status: CompletionBlockStatusEnumToJSON(value.status),
    messageId: value.messageId,
    extra: value.extra,
    data: value.data,
  };
}
