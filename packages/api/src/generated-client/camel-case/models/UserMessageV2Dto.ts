/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { AtReferenceDto } from './AtReferenceDto';
import {
  AtReferenceDtoFromJSON,
  AtReferenceDtoFromJSONTyped,
  AtReferenceDtoToJSON,
  AtReferenceDtoToJSONTyped,
} from './AtReferenceDto';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  ChatOriginDtoToJSON,
  ChatOriginDtoToJSONTyped,
} from './ChatOriginDto';
import type { EditCommandDto } from './EditCommandDto';
import {
  EditCommandDtoFromJSON,
  EditCommandDtoFromJSONTyped,
  EditCommandDtoToJSON,
  EditCommandDtoToJSONTyped,
} from './EditCommandDto';
import type { MessageModeEnum } from './MessageModeEnum';
import {
  MessageModeEnumFromJSON,
  MessageModeEnumFromJSONTyped,
  MessageModeEnumToJSON,
  MessageModeEnumToJSONTyped,
} from './MessageModeEnum';
import type { MessageRoleEnum } from './MessageRoleEnum';
import {
  MessageRoleEnumFromJSON,
  MessageRoleEnumFromJSONTyped,
  MessageRoleEnumToJSON,
  MessageRoleEnumToJSONTyped,
} from './MessageRoleEnum';
import type { MessageStatusEnum } from './MessageStatusEnum';
import {
  MessageStatusEnumFromJSON,
  MessageStatusEnumFromJSONTyped,
  MessageStatusEnumToJSON,
  MessageStatusEnumToJSONTyped,
} from './MessageStatusEnum';
import type { ShortcutDto } from './ShortcutDto';
import {
  ShortcutDtoFromJSON,
  ShortcutDtoFromJSONTyped,
  ShortcutDtoToJSON,
  ShortcutDtoToJSONTyped,
} from './ShortcutDto';
import type { UseToolsDto } from './UseToolsDto';
import {
  UseToolsDtoFromJSON,
  UseToolsDtoFromJSONTyped,
  UseToolsDtoToJSON,
  UseToolsDtoToJSONTyped,
} from './UseToolsDto';

/**
 *
 * @export
 * @interface UserMessageV2Dto
 */
export interface UserMessageV2Dto {
  /**
   * Message ID
   * @type {string}
   * @memberof UserMessageV2Dto
   */
  id: string;
  /**
   * Chat ID
   * @type {string}
   * @memberof UserMessageV2Dto
   */
  chatId: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof UserMessageV2Dto
   */
  createdAt: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof UserMessageV2Dto
   */
  updatedAt: Date;
  /**
   * Message role
   * @type {MessageRoleEnum}
   * @memberof UserMessageV2Dto
   */
  role: MessageRoleEnum;
  /**
   * Message status
   * @type {MessageStatusEnum}
   * @memberof UserMessageV2Dto
   */
  status: MessageStatusEnum;
  /**
   * Message content
   * @type {string}
   * @memberof UserMessageV2Dto
   */
  content: string;
  /**
   * Message origin context
   * @type {ChatOriginDto}
   * @memberof UserMessageV2Dto
   */
  origin: ChatOriginDto;
  /**
   * Selected text context
   * @type {string}
   * @memberof UserMessageV2Dto
   */
  selection?: string;
  /**
   * Referenced entities
   * @type {Array<AtReferenceDto>}
   * @memberof UserMessageV2Dto
   */
  atReferences?: Array<AtReferenceDto>;
  /**
   * Board ID if applicable
   * @type {string}
   * @memberof UserMessageV2Dto
   */
  boardId: string;
  /**
   * Tools configuration
   * @type {UseToolsDto}
   * @memberof UserMessageV2Dto
   */
  tools?: UseToolsDto;
  /**
   * Edit command
   * @type {EditCommandDto}
   * @memberof UserMessageV2Dto
   */
  command?: EditCommandDto;
  /**
   * Message mode
   * @type {MessageModeEnum}
   * @memberof UserMessageV2Dto
   */
  mode: MessageModeEnum;
  /**
   * Shortcut information
   * @type {ShortcutDto}
   * @memberof UserMessageV2Dto
   */
  shortcut?: ShortcutDto;
}

/**
 * Check if a given object implements the UserMessageV2Dto interface.
 */
export function instanceOfUserMessageV2Dto(value: object): value is UserMessageV2Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('chatId' in value) || value.chatId === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('role' in value) || value.role === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('content' in value) || value.content === undefined) return false;
  if (!('origin' in value) || value.origin === undefined) return false;
  if (!('boardId' in value) || value.boardId === undefined) return false;
  if (!('mode' in value) || value.mode === undefined) return false;
  return true;
}

export function UserMessageV2DtoFromJSON(json: any): UserMessageV2Dto {
  return UserMessageV2DtoFromJSONTyped(json, false);
}

export function UserMessageV2DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): UserMessageV2Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    chatId: json.chatId,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    role: MessageRoleEnumFromJSON(json.role),
    status: MessageStatusEnumFromJSON(json.status),
    content: json.content,
    origin: ChatOriginDtoFromJSON(json.origin),
    selection: json.selection == null ? undefined : json.selection,
    atReferences:
      json.atReferences == null
        ? undefined
        : (json.atReferences as Array<any>).map(AtReferenceDtoFromJSON),
    boardId: json.boardId,
    tools: json.tools == null ? undefined : UseToolsDtoFromJSON(json.tools),
    command: json.command == null ? undefined : EditCommandDtoFromJSON(json.command),
    mode: MessageModeEnumFromJSON(json.mode),
    shortcut: json.shortcut == null ? undefined : ShortcutDtoFromJSON(json.shortcut),
  };
}

export function UserMessageV2DtoToJSON(json: any): UserMessageV2Dto {
  return UserMessageV2DtoToJSONTyped(json, false);
}

export function UserMessageV2DtoToJSONTyped(
  value?: UserMessageV2Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    chatId: value.chatId,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    role: MessageRoleEnumToJSON(value.role),
    status: MessageStatusEnumToJSON(value.status),
    content: value.content,
    origin: ChatOriginDtoToJSON(value.origin),
    selection: value.selection,
    atReferences:
      value.atReferences == null
        ? undefined
        : (value.atReferences as Array<any>).map(AtReferenceDtoToJSON),
    boardId: value.boardId,
    tools: UseToolsDtoToJSON(value.tools),
    command: EditCommandDtoToJSON(value.command),
    mode: MessageModeEnumToJSON(value.mode),
    shortcut: ShortcutDtoToJSON(value.shortcut),
  };
}
