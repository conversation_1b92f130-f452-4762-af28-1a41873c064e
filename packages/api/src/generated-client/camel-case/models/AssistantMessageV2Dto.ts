/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { AssistantMessageV2DtoBlocks } from './AssistantMessageV2DtoBlocks';
import {
  AssistantMessageV2DtoBlocksFromJSON,
  AssistantMessageV2DtoBlocksFromJSONTyped,
  AssistantMessageV2DtoBlocksToJSON,
  AssistantMessageV2DtoBlocksToJSONTyped,
} from './AssistantMessageV2DtoBlocks';
import type { LLMs } from './LLMs';
import { LLMsFromJSON, LLMsFromJSONTyped, LLMsToJSON, LLMsToJSONTyped } from './LLMs';
import type { MessageRoleEnum } from './MessageRoleEnum';
import {
  MessageRoleEnumFromJSON,
  MessageRoleEnumFromJSONTyped,
  MessageRoleEnumToJSON,
  MessageRoleEnumToJSONTyped,
} from './MessageRoleEnum';
import type { MessageStatusEnum } from './MessageStatusEnum';
import {
  MessageStatusEnumFromJSON,
  MessageStatusEnumFromJSONTyped,
  MessageStatusEnumToJSON,
  MessageStatusEnumToJSONTyped,
} from './MessageStatusEnum';

/**
 *
 * @export
 * @interface AssistantMessageV2Dto
 */
export interface AssistantMessageV2Dto {
  /**
   * Message ID
   * @type {string}
   * @memberof AssistantMessageV2Dto
   */
  id: string;
  /**
   * Chat ID
   * @type {string}
   * @memberof AssistantMessageV2Dto
   */
  chatId: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof AssistantMessageV2Dto
   */
  createdAt: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof AssistantMessageV2Dto
   */
  updatedAt: Date;
  /**
   * Message role
   * @type {MessageRoleEnum}
   * @memberof AssistantMessageV2Dto
   */
  role: MessageRoleEnum;
  /**
   * Message status
   * @type {MessageStatusEnum}
   * @memberof AssistantMessageV2Dto
   */
  status: MessageStatusEnum;
  /**
   * AI model used
   * @type {LLMs}
   * @memberof AssistantMessageV2Dto
   */
  model: LLMs;
  /**
   * Trace ID for debugging
   * @type {string}
   * @memberof AssistantMessageV2Dto
   */
  traceId: string;
  /**
   *
   * @type {AssistantMessageV2DtoBlocks}
   * @memberof AssistantMessageV2Dto
   */
  blocks: AssistantMessageV2DtoBlocks;
  /**
   * Error information if any
   * @type {object}
   * @memberof AssistantMessageV2Dto
   */
  error?: object;
}

/**
 * Check if a given object implements the AssistantMessageV2Dto interface.
 */
export function instanceOfAssistantMessageV2Dto(value: object): value is AssistantMessageV2Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('chatId' in value) || value.chatId === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('role' in value) || value.role === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('model' in value) || value.model === undefined) return false;
  if (!('traceId' in value) || value.traceId === undefined) return false;
  if (!('blocks' in value) || value.blocks === undefined) return false;
  return true;
}

export function AssistantMessageV2DtoFromJSON(json: any): AssistantMessageV2Dto {
  return AssistantMessageV2DtoFromJSONTyped(json, false);
}

export function AssistantMessageV2DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): AssistantMessageV2Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    chatId: json.chatId,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    role: MessageRoleEnumFromJSON(json.role),
    status: MessageStatusEnumFromJSON(json.status),
    model: LLMsFromJSON(json.model),
    traceId: json.traceId,
    blocks: AssistantMessageV2DtoBlocksFromJSON(json.blocks),
    error: json.error == null ? undefined : json.error,
  };
}

export function AssistantMessageV2DtoToJSON(json: any): AssistantMessageV2Dto {
  return AssistantMessageV2DtoToJSONTyped(json, false);
}

export function AssistantMessageV2DtoToJSONTyped(
  value?: AssistantMessageV2Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    chatId: value.chatId,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    role: MessageRoleEnumToJSON(value.role),
    status: MessageStatusEnumToJSON(value.status),
    model: LLMsToJSON(value.model),
    traceId: value.traceId,
    blocks: AssistantMessageV2DtoBlocksToJSON(value.blocks),
    error: value.error,
  };
}
