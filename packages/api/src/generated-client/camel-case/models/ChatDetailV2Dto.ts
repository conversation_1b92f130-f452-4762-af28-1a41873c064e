/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ChatDetailV2DtoMessagesInner } from './ChatDetailV2DtoMessagesInner';
import {
  ChatDetailV2DtoMessagesInnerFromJSON,
  ChatDetailV2DtoMessagesInnerFromJSONTyped,
  ChatDetailV2DtoMessagesInnerToJSON,
  ChatDetailV2DtoMessagesInnerToJSONTyped,
} from './ChatDetailV2DtoMessagesInner';
import type { ChatModeEnum } from './ChatModeEnum';
import {
  ChatModeEnumFromJSON,
  ChatModeEnumFromJSONTyped,
  ChatModeEnumToJSON,
  ChatModeEnumToJSONTyped,
} from './ChatModeEnum';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  ChatOriginDtoToJSON,
  ChatOriginDtoToJSONTyped,
} from './ChatOriginDto';

/**
 *
 * @export
 * @interface ChatDetailV2Dto
 */
export interface ChatDetailV2Dto {
  /**
   * Chat ID
   * @type {string}
   * @memberof ChatDetailV2Dto
   */
  id: string;
  /**
   * Creator user ID
   * @type {string}
   * @memberof ChatDetailV2Dto
   */
  creatorId: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof ChatDetailV2Dto
   */
  createdAt: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof ChatDetailV2Dto
   */
  updatedAt: Date;
  /**
   * Chat title
   * @type {string}
   * @memberof ChatDetailV2Dto
   */
  title: string;
  /**
   * Chat origin context
   * @type {ChatOriginDto}
   * @memberof ChatDetailV2Dto
   */
  origin: ChatOriginDto;
  /**
   * Board ID if applicable
   * @type {string}
   * @memberof ChatDetailV2Dto
   */
  boardId?: string;
  /**
   * Chat mode
   * @type {ChatModeEnum}
   * @memberof ChatDetailV2Dto
   */
  mode: ChatModeEnum;
  /**
   * Whether to show new board suggestion
   * @type {boolean}
   * @memberof ChatDetailV2Dto
   */
  showNewBoardSuggestion: boolean;
  /**
   * New board chat ID if applicable
   * @type {string}
   * @memberof ChatDetailV2Dto
   */
  newBoardChatId?: string;
  /**
   * Associated board IDs
   * @type {Array<string>}
   * @memberof ChatDetailV2Dto
   */
  boardIds?: Array<string>;
  /**
   * Board item information
   * @type {object}
   * @memberof ChatDetailV2Dto
   */
  boardItem?: object;
  /**
   * Chat messages (mixed user and assistant messages)
   * @type {Array<ChatDetailV2DtoMessagesInner>}
   * @memberof ChatDetailV2Dto
   */
  messages: Array<ChatDetailV2DtoMessagesInner>;
}

/**
 * Check if a given object implements the ChatDetailV2Dto interface.
 */
export function instanceOfChatDetailV2Dto(value: object): value is ChatDetailV2Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('creatorId' in value) || value.creatorId === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('title' in value) || value.title === undefined) return false;
  if (!('origin' in value) || value.origin === undefined) return false;
  if (!('mode' in value) || value.mode === undefined) return false;
  if (!('showNewBoardSuggestion' in value) || value.showNewBoardSuggestion === undefined)
    return false;
  if (!('messages' in value) || value.messages === undefined) return false;
  return true;
}

export function ChatDetailV2DtoFromJSON(json: any): ChatDetailV2Dto {
  return ChatDetailV2DtoFromJSONTyped(json, false);
}

export function ChatDetailV2DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ChatDetailV2Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    creatorId: json.creatorId,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    title: json.title,
    origin: ChatOriginDtoFromJSON(json.origin),
    boardId: json.boardId == null ? undefined : json.boardId,
    mode: ChatModeEnumFromJSON(json.mode),
    showNewBoardSuggestion: json.showNewBoardSuggestion,
    newBoardChatId: json.newBoardChatId == null ? undefined : json.newBoardChatId,
    boardIds: json.boardIds == null ? undefined : json.boardIds,
    boardItem: json.boardItem == null ? undefined : json.boardItem,
    messages: (json.messages as Array<any>).map(ChatDetailV2DtoMessagesInnerFromJSON),
  };
}

export function ChatDetailV2DtoToJSON(json: any): ChatDetailV2Dto {
  return ChatDetailV2DtoToJSONTyped(json, false);
}

export function ChatDetailV2DtoToJSONTyped(
  value?: ChatDetailV2Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    creatorId: value.creatorId,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    title: value.title,
    origin: ChatOriginDtoToJSON(value.origin),
    boardId: value.boardId,
    mode: ChatModeEnumToJSON(value.mode),
    showNewBoardSuggestion: value.showNewBoardSuggestion,
    newBoardChatId: value.newBoardChatId,
    boardIds: value.boardIds,
    boardItem: value.boardItem,
    messages: (value.messages as Array<any>).map(ChatDetailV2DtoMessagesInnerToJSON),
  };
}
