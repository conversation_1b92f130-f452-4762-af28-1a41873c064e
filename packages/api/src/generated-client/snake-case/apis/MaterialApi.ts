/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type {
  ListMaterialsResponseDto,
  ListRecentMaterialsDto,
  ListUnusedMaterialsForMobileResponseDto,
  ListUnusedMaterialsResponseDto,
  PublishMaterialDto,
  PublishMaterialResponseDto,
  RecentMaterialItemDto,
  SaveSharedMaterialDto,
  SaveSharedMaterialResponseDto,
  UnpublishMaterialDto,
} from '../models/index';
import {
  ListMaterialsResponseDtoFromJSON,
  ListMaterialsResponseDtoToJSON,
  ListRecentMaterialsDtoFromJSON,
  ListRecentMaterialsDtoToJSON,
  ListUnusedMaterialsForMobileResponseDtoFromJSON,
  ListUnusedMaterialsForMobileResponseDtoToJSON,
  ListUnusedMaterialsResponseDtoFromJSON,
  ListUnusedMaterialsResponseDtoToJSON,
  PublishMaterialDtoFromJSON,
  PublishMaterialDtoToJSON,
  PublishMaterialResponseDtoFromJSON,
  PublishMaterialResponseDtoToJSON,
  RecentMaterialItemDtoFromJSON,
  RecentMaterialItemDtoToJSON,
  SaveSharedMaterialDtoFromJSON,
  SaveSharedMaterialDtoToJSON,
  SaveSharedMaterialResponseDtoFromJSON,
  SaveSharedMaterialResponseDtoToJSON,
  UnpublishMaterialDtoFromJSON,
  UnpublishMaterialDtoToJSON,
} from '../models/index';

export interface MaterialControllerListRecentMaterialsRequest {
  listRecentMaterialsDto: ListRecentMaterialsDto;
}

export interface MaterialControllerPublishRequest {
  publishMaterialDto: PublishMaterialDto;
}

export interface MaterialControllerSaveSharedRequest {
  saveSharedMaterialDto: SaveSharedMaterialDto;
}

export interface MaterialControllerUnpublishRequest {
  unpublishMaterialDto: UnpublishMaterialDto;
}

/**
 * MaterialApi - interface
 *
 * @export
 * @interface MaterialApiInterface
 */
export interface MaterialApiInterface {
  /**
   * 获取材料，包括 snips、thoughts 和 chats
   * @summary 获取材料列表
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MaterialApiInterface
   */
  listMaterialsRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ListMaterialsResponseDto>>;

  /**
   * 获取材料，包括 snips、thoughts 和 chats
   * 获取材料列表
   */
  listMaterials(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ListMaterialsResponseDto>;

  /**
   * 按时间顺序获取最近的材料（snips 和 thoughts），支持按数量和是否包含归档内容过滤
   * @summary 获取最近材料列表
   * @param {ListRecentMaterialsDto} listRecentMaterialsDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MaterialApiInterface
   */
  listRecentMaterialsRaw(
    requestParameters: MaterialControllerListRecentMaterialsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<RecentMaterialItemDto>>>;

  /**
   * 按时间顺序获取最近的材料（snips 和 thoughts），支持按数量和是否包含归档内容过滤
   * 获取最近材料列表
   */
  listRecentMaterials(
    listRecentMaterialsDto: ListRecentMaterialsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<RecentMaterialItemDto>>;

  /**
   * 获取未使用的材料，包括 snips 和 thoughts（未添加到任何 board 中的材料）
   * @summary 获取未使用的材料列表
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MaterialApiInterface
   */
  listUnusedMaterialsRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ListUnusedMaterialsResponseDto>>;

  /**
   * 获取未使用的材料，包括 snips 和 thoughts（未添加到任何 board 中的材料）
   * 获取未使用的材料列表
   */
  listUnusedMaterials(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ListUnusedMaterialsResponseDto>;

  /**
   * 获取未使用的材料，包括 snips 和 thoughts（未添加到任何 board 中的材料），每种类型限制 20 条
   * @summary 获取未使用的材料列表（移动端）
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MaterialApiInterface
   */
  listUnusedMaterialsForMobileRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ListUnusedMaterialsForMobileResponseDto>>;

  /**
   * 获取未使用的材料，包括 snips 和 thoughts（未添加到任何 board 中的材料），每种类型限制 20 条
   * 获取未使用的材料列表（移动端）
   */
  listUnusedMaterialsForMobile(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ListUnusedMaterialsForMobileResponseDto>;

  /**
   * 将材料（snip、thought、chat）设置为公开可见，并生成分享链接
   * @summary 发布材料
   * @param {PublishMaterialDto} publishMaterialDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MaterialApiInterface
   */
  publishRaw(
    requestParameters: MaterialControllerPublishRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<PublishMaterialResponseDto>>;

  /**
   * 将材料（snip、thought、chat）设置为公开可见，并生成分享链接
   * 发布材料
   */
  publish(
    publishMaterialDto: PublishMaterialDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<PublishMaterialResponseDto>;

  /**
   * 通过短链接ID将分享的材料（snip、thought）保存到指定看板，支持跨空间克隆
   * @summary 保存分享的材料
   * @param {SaveSharedMaterialDto} saveSharedMaterialDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MaterialApiInterface
   */
  saveSharedRaw(
    requestParameters: MaterialControllerSaveSharedRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<SaveSharedMaterialResponseDto>>;

  /**
   * 通过短链接ID将分享的材料（snip、thought）保存到指定看板，支持跨空间克隆
   * 保存分享的材料
   */
  saveShared(
    saveSharedMaterialDto: SaveSharedMaterialDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<SaveSharedMaterialResponseDto>;

  /**
   * 将材料（snip、thought）从公开状态改为私有状态，并停用关联的短链接
   * @summary 取消发布材料
   * @param {UnpublishMaterialDto} unpublishMaterialDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MaterialApiInterface
   */
  unpublishRaw(
    requestParameters: MaterialControllerUnpublishRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 将材料（snip、thought）从公开状态改为私有状态，并停用关联的短链接
   * 取消发布材料
   */
  unpublish(
    unpublishMaterialDto: UnpublishMaterialDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;
}

/**
 *
 */
export class MaterialApi extends runtime.BaseAPI implements MaterialApiInterface {
  /**
   * 获取材料，包括 snips、thoughts 和 chats
   * 获取材料列表
   */
  async listMaterialsRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ListMaterialsResponseDto>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/material/listMaterials`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ListMaterialsResponseDtoFromJSON(jsonValue),
    );
  }

  /**
   * 获取材料，包括 snips、thoughts 和 chats
   * 获取材料列表
   */
  async listMaterials(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ListMaterialsResponseDto> {
    const response = await this.listMaterialsRaw(initOverrides);
    return await response.value();
  }

  /**
   * 按时间顺序获取最近的材料（snips 和 thoughts），支持按数量和是否包含归档内容过滤
   * 获取最近材料列表
   */
  async listRecentMaterialsRaw(
    requestParameters: MaterialControllerListRecentMaterialsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<RecentMaterialItemDto>>> {
    if (requestParameters.listRecentMaterialsDto == null) {
      throw new runtime.RequiredError(
        'listRecentMaterialsDto',
        'Required parameter "listRecentMaterialsDto" was null or undefined when calling listRecentMaterials().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/material/listRecentMaterials`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ListRecentMaterialsDtoToJSON(requestParameters.listRecentMaterialsDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      jsonValue.map(RecentMaterialItemDtoFromJSON),
    );
  }

  /**
   * 按时间顺序获取最近的材料（snips 和 thoughts），支持按数量和是否包含归档内容过滤
   * 获取最近材料列表
   */
  async listRecentMaterials(
    listRecentMaterialsDto: ListRecentMaterialsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<RecentMaterialItemDto>> {
    const response = await this.listRecentMaterialsRaw(
      { listRecentMaterialsDto: listRecentMaterialsDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * 获取未使用的材料，包括 snips 和 thoughts（未添加到任何 board 中的材料）
   * 获取未使用的材料列表
   */
  async listUnusedMaterialsRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ListUnusedMaterialsResponseDto>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/material/listUnusedMaterials`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ListUnusedMaterialsResponseDtoFromJSON(jsonValue),
    );
  }

  /**
   * 获取未使用的材料，包括 snips 和 thoughts（未添加到任何 board 中的材料）
   * 获取未使用的材料列表
   */
  async listUnusedMaterials(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ListUnusedMaterialsResponseDto> {
    const response = await this.listUnusedMaterialsRaw(initOverrides);
    return await response.value();
  }

  /**
   * 获取未使用的材料，包括 snips 和 thoughts（未添加到任何 board 中的材料），每种类型限制 20 条
   * 获取未使用的材料列表（移动端）
   */
  async listUnusedMaterialsForMobileRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ListUnusedMaterialsForMobileResponseDto>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/material/listUnusedMaterialsForMobile`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ListUnusedMaterialsForMobileResponseDtoFromJSON(jsonValue),
    );
  }

  /**
   * 获取未使用的材料，包括 snips 和 thoughts（未添加到任何 board 中的材料），每种类型限制 20 条
   * 获取未使用的材料列表（移动端）
   */
  async listUnusedMaterialsForMobile(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ListUnusedMaterialsForMobileResponseDto> {
    const response = await this.listUnusedMaterialsForMobileRaw(initOverrides);
    return await response.value();
  }

  /**
   * 将材料（snip、thought、chat）设置为公开可见，并生成分享链接
   * 发布材料
   */
  async publishRaw(
    requestParameters: MaterialControllerPublishRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<PublishMaterialResponseDto>> {
    if (requestParameters.publishMaterialDto == null) {
      throw new runtime.RequiredError(
        'publishMaterialDto',
        'Required parameter "publishMaterialDto" was null or undefined when calling publish().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/material/publish`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: PublishMaterialDtoToJSON(requestParameters.publishMaterialDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      PublishMaterialResponseDtoFromJSON(jsonValue),
    );
  }

  /**
   * 将材料（snip、thought、chat）设置为公开可见，并生成分享链接
   * 发布材料
   */
  async publish(
    publishMaterialDto: PublishMaterialDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<PublishMaterialResponseDto> {
    const response = await this.publishRaw(
      { publishMaterialDto: publishMaterialDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * 通过短链接ID将分享的材料（snip、thought）保存到指定看板，支持跨空间克隆
   * 保存分享的材料
   */
  async saveSharedRaw(
    requestParameters: MaterialControllerSaveSharedRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<SaveSharedMaterialResponseDto>> {
    if (requestParameters.saveSharedMaterialDto == null) {
      throw new runtime.RequiredError(
        'saveSharedMaterialDto',
        'Required parameter "saveSharedMaterialDto" was null or undefined when calling saveShared().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/material/saveShared`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: SaveSharedMaterialDtoToJSON(requestParameters.saveSharedMaterialDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      SaveSharedMaterialResponseDtoFromJSON(jsonValue),
    );
  }

  /**
   * 通过短链接ID将分享的材料（snip、thought）保存到指定看板，支持跨空间克隆
   * 保存分享的材料
   */
  async saveShared(
    saveSharedMaterialDto: SaveSharedMaterialDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<SaveSharedMaterialResponseDto> {
    const response = await this.saveSharedRaw(
      { saveSharedMaterialDto: saveSharedMaterialDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * 将材料（snip、thought）从公开状态改为私有状态，并停用关联的短链接
   * 取消发布材料
   */
  async unpublishRaw(
    requestParameters: MaterialControllerUnpublishRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.unpublishMaterialDto == null) {
      throw new runtime.RequiredError(
        'unpublishMaterialDto',
        'Required parameter "unpublishMaterialDto" was null or undefined when calling unpublish().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/material/unpublish`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: UnpublishMaterialDtoToJSON(requestParameters.unpublishMaterialDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 将材料（snip、thought）从公开状态改为私有状态，并停用关联的短链接
   * 取消发布材料
   */
  async unpublish(
    unpublishMaterialDto: UnpublishMaterialDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.unpublishRaw({ unpublishMaterialDto: unpublishMaterialDto }, initOverrides);
  }
}
