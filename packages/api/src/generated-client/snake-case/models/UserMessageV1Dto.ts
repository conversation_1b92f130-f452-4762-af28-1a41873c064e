/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  ChatOriginDtoToJSON,
  ChatOriginDtoToJSONTyped,
} from './ChatOriginDto';
import type { MessageRoleEnum } from './MessageRoleEnum';
import {
  MessageRoleEnumFromJSON,
  MessageRoleEnumFromJSONTyped,
  MessageRoleEnumToJSON,
  MessageRoleEnumToJSONTyped,
} from './MessageRoleEnum';
import type { MessageStatusEnum } from './MessageStatusEnum';
import {
  MessageStatusEnumFromJSON,
  MessageStatusEnumFromJSONTyped,
  MessageStatusEnumToJSON,
  MessageStatusEnumToJSONTyped,
} from './MessageStatusEnum';

/**
 *
 * @export
 * @interface UserMessageV1Dto
 */
export interface UserMessageV1Dto {
  /**
   * Message ID
   * @type {string}
   * @memberof UserMessageV1Dto
   */
  id: string;
  /**
   * Chat ID
   * @type {string}
   * @memberof UserMessageV1Dto
   */
  chat_id: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof UserMessageV1Dto
   */
  created_at: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof UserMessageV1Dto
   */
  updated_at: Date;
  /**
   * Message role
   * @type {MessageRoleEnum}
   * @memberof UserMessageV1Dto
   */
  role: MessageRoleEnum;
  /**
   * Message status
   * @type {MessageStatusEnum}
   * @memberof UserMessageV1Dto
   */
  status: MessageStatusEnum;
  /**
   * Message content
   * @type {string}
   * @memberof UserMessageV1Dto
   */
  content: string;
  /**
   * Message origin context
   * @type {ChatOriginDto}
   * @memberof UserMessageV1Dto
   */
  origin: ChatOriginDto;
  /**
   * Selected text context
   * @type {string}
   * @memberof UserMessageV1Dto
   */
  selection?: string;
}

/**
 * Check if a given object implements the UserMessageV1Dto interface.
 */
export function instanceOfUserMessageV1Dto(value: object): value is UserMessageV1Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('chat_id' in value) || value.chat_id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('role' in value) || value.role === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('content' in value) || value.content === undefined) return false;
  if (!('origin' in value) || value.origin === undefined) return false;
  return true;
}

export function UserMessageV1DtoFromJSON(json: any): UserMessageV1Dto {
  return UserMessageV1DtoFromJSONTyped(json, false);
}

export function UserMessageV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): UserMessageV1Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    chat_id: json.chat_id,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    role: MessageRoleEnumFromJSON(json.role),
    status: MessageStatusEnumFromJSON(json.status),
    content: json.content,
    origin: ChatOriginDtoFromJSON(json.origin),
    selection: json.selection == null ? undefined : json.selection,
  };
}

export function UserMessageV1DtoToJSON(json: any): UserMessageV1Dto {
  return UserMessageV1DtoToJSONTyped(json, false);
}

export function UserMessageV1DtoToJSONTyped(
  value?: UserMessageV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    chat_id: value.chat_id,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    role: MessageRoleEnumToJSON(value.role),
    status: MessageStatusEnumToJSON(value.status),
    content: value.content,
    origin: ChatOriginDtoToJSON(value.origin),
    selection: value.selection,
  };
}
