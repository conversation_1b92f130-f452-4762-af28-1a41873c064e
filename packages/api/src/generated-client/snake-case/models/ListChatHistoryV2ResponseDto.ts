/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ChatV2Dto } from './ChatV2Dto';
import {
  ChatV2DtoFromJSON,
  ChatV2DtoFromJSONTyped,
  ChatV2DtoToJSON,
  ChatV2DtoToJSONTyped,
} from './ChatV2Dto';
import type { PaginationDto } from './PaginationDto';
import {
  PaginationDtoFromJSON,
  PaginationDtoFromJSONTyped,
  PaginationDtoToJSON,
  PaginationDtoToJSONTyped,
} from './PaginationDto';

/**
 *
 * @export
 * @interface ListChatHistoryV2ResponseDto
 */
export interface ListChatHistoryV2ResponseDto {
  /**
   * Paginated chat history data
   * @type {Array<ChatV2Dto>}
   * @memberof ListChatHistoryV2ResponseDto
   */
  data: Array<ChatV2Dto>;
  /**
   * Pagination information
   * @type {PaginationDto}
   * @memberof ListChatHistoryV2ResponseDto
   */
  paging: PaginationDto;
}

/**
 * Check if a given object implements the ListChatHistoryV2ResponseDto interface.
 */
export function instanceOfListChatHistoryV2ResponseDto(
  value: object,
): value is ListChatHistoryV2ResponseDto {
  if (!('data' in value) || value.data === undefined) return false;
  if (!('paging' in value) || value.paging === undefined) return false;
  return true;
}

export function ListChatHistoryV2ResponseDtoFromJSON(json: any): ListChatHistoryV2ResponseDto {
  return ListChatHistoryV2ResponseDtoFromJSONTyped(json, false);
}

export function ListChatHistoryV2ResponseDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ListChatHistoryV2ResponseDto {
  if (json == null) {
    return json;
  }
  return {
    data: (json.data as Array<any>).map(ChatV2DtoFromJSON),
    paging: PaginationDtoFromJSON(json.paging),
  };
}

export function ListChatHistoryV2ResponseDtoToJSON(json: any): ListChatHistoryV2ResponseDto {
  return ListChatHistoryV2ResponseDtoToJSONTyped(json, false);
}

export function ListChatHistoryV2ResponseDtoToJSONTyped(
  value?: ListChatHistoryV2ResponseDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    data: (value.data as Array<any>).map(ChatV2DtoToJSON),
    paging: PaginationDtoToJSON(value.paging),
  };
}
