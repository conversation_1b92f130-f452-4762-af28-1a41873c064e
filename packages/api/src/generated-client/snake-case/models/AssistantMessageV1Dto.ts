/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { LLMs } from './LLMs';
import { LLMsFromJSON, LLMsFromJSONTyped, LLMsToJSON, LLMsToJSONTyped } from './LLMs';
import type { MessageRoleEnum } from './MessageRoleEnum';
import {
  MessageRoleEnumFromJSON,
  MessageRoleEnumFromJSONTyped,
  MessageRoleEnumToJSON,
  MessageRoleEnumToJSONTyped,
} from './MessageRoleEnum';
import type { MessageStatusEnum } from './MessageStatusEnum';
import {
  MessageStatusEnumFromJSON,
  MessageStatusEnumFromJSONTyped,
  MessageStatusEnumToJSON,
  MessageStatusEnumToJSONTyped,
} from './MessageStatusEnum';

/**
 *
 * @export
 * @interface AssistantMessageV1Dto
 */
export interface AssistantMessageV1Dto {
  /**
   * Message ID
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  id: string;
  /**
   * Chat ID
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  chat_id: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof AssistantMessageV1Dto
   */
  created_at: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof AssistantMessageV1Dto
   */
  updated_at: Date;
  /**
   * Message role
   * @type {MessageRoleEnum}
   * @memberof AssistantMessageV1Dto
   */
  role: MessageRoleEnum;
  /**
   * Message status
   * @type {MessageStatusEnum}
   * @memberof AssistantMessageV1Dto
   */
  status: MessageStatusEnum;
  /**
   * AI model used
   * @type {LLMs}
   * @memberof AssistantMessageV1Dto
   */
  model: LLMs;
  /**
   * Trace ID for debugging
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  trace_id: string;
  /**
   * Message content (extracted from content blocks)
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  content: string;
  /**
   * Reasoning text (extracted from reasoning blocks)
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  reasoning?: string;
  /**
   * Message events (converted from tool blocks)
   * @type {Array<object>}
   * @memberof AssistantMessageV1Dto
   */
  events: Array<object>;
  /**
   * Error information if any
   * @type {object}
   * @memberof AssistantMessageV1Dto
   */
  error?: object;
  /**
   * Reasoning start timestamp
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  reasoning_begin_at?: string;
  /**
   * Reasoning end timestamp
   * @type {string}
   * @memberof AssistantMessageV1Dto
   */
  reasoning_end_at?: string;
}

/**
 * Check if a given object implements the AssistantMessageV1Dto interface.
 */
export function instanceOfAssistantMessageV1Dto(value: object): value is AssistantMessageV1Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('chat_id' in value) || value.chat_id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('role' in value) || value.role === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('model' in value) || value.model === undefined) return false;
  if (!('trace_id' in value) || value.trace_id === undefined) return false;
  if (!('content' in value) || value.content === undefined) return false;
  if (!('events' in value) || value.events === undefined) return false;
  return true;
}

export function AssistantMessageV1DtoFromJSON(json: any): AssistantMessageV1Dto {
  return AssistantMessageV1DtoFromJSONTyped(json, false);
}

export function AssistantMessageV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): AssistantMessageV1Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    chat_id: json.chat_id,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    role: MessageRoleEnumFromJSON(json.role),
    status: MessageStatusEnumFromJSON(json.status),
    model: LLMsFromJSON(json.model),
    trace_id: json.trace_id,
    content: json.content,
    reasoning: json.reasoning == null ? undefined : json.reasoning,
    events: json.events,
    error: json.error == null ? undefined : json.error,
    reasoning_begin_at: json.reasoning_begin_at == null ? undefined : json.reasoning_begin_at,
    reasoning_end_at: json.reasoning_end_at == null ? undefined : json.reasoning_end_at,
  };
}

export function AssistantMessageV1DtoToJSON(json: any): AssistantMessageV1Dto {
  return AssistantMessageV1DtoToJSONTyped(json, false);
}

export function AssistantMessageV1DtoToJSONTyped(
  value?: AssistantMessageV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    chat_id: value.chat_id,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    role: MessageRoleEnumToJSON(value.role),
    status: MessageStatusEnumToJSON(value.status),
    model: LLMsToJSON(value.model),
    trace_id: value.trace_id,
    content: value.content,
    reasoning: value.reasoning,
    events: value.events,
    error: value.error,
    reasoning_begin_at: value.reasoning_begin_at,
    reasoning_end_at: value.reasoning_end_at,
  };
}
