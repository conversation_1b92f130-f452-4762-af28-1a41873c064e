/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * Message role
 * @export
 * @enum {string}
 */
export enum MessageRoleEnum {
  user = 'user',
  assistant = 'assistant',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfMessageRoleEnum(value: any): boolean {
  for (const key in MessageRoleEnum) {
    if (Object.hasOwn(MessageRoleEnum, key)) {
      if (MessageRoleEnum[key as keyof typeof MessageRoleEnum] === value) {
        return true;
      }
    }
  }
  return false;
}

export function MessageRoleEnumFromJSON(json: any): MessageRoleEnum {
  return MessageRoleEnumFromJSONTyped(json, false);
}

export function MessageRoleEnumFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): MessageRoleEnum {
  return json as MessageRoleEnum;
}

export function MessageRoleEnumToJSON(value?: MessageRoleEnum | null): any {
  return value as any;
}

export function MessageRoleEnumToJSONTyped(
  value: any,
  _ignoreDiscriminator: boolean,
): MessageRoleEnum {
  return value as MessageRoleEnum;
}
