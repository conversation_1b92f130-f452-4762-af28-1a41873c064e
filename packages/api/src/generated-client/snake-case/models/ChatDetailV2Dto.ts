/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ChatDetailV2DtoMessagesInner } from './ChatDetailV2DtoMessagesInner';
import {
  ChatDetailV2DtoMessagesInnerFromJSON,
  ChatDetailV2DtoMessagesInnerFromJSONTyped,
  ChatDetailV2DtoMessagesInnerToJSON,
  ChatDetailV2DtoMessagesInnerToJSONTyped,
} from './ChatDetailV2DtoMessagesInner';
import type { ChatModeEnum } from './ChatModeEnum';
import {
  ChatModeEnumFromJSON,
  ChatModeEnumFromJSONTyped,
  ChatModeEnumToJSON,
  ChatModeEnumToJSONTyped,
} from './ChatModeEnum';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  ChatOriginDtoToJSON,
  ChatOriginDtoToJSONTyped,
} from './ChatOriginDto';

/**
 *
 * @export
 * @interface ChatDetailV2Dto
 */
export interface ChatDetailV2Dto {
  /**
   * Chat ID
   * @type {string}
   * @memberof ChatDetailV2Dto
   */
  id: string;
  /**
   * Creator user ID
   * @type {string}
   * @memberof ChatDetailV2Dto
   */
  creator_id: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof ChatDetailV2Dto
   */
  created_at: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof ChatDetailV2Dto
   */
  updated_at: Date;
  /**
   * Chat title
   * @type {string}
   * @memberof ChatDetailV2Dto
   */
  title: string;
  /**
   * Chat origin context
   * @type {ChatOriginDto}
   * @memberof ChatDetailV2Dto
   */
  origin: ChatOriginDto;
  /**
   * Board ID if applicable
   * @type {string}
   * @memberof ChatDetailV2Dto
   */
  board_id?: string;
  /**
   * Chat mode
   * @type {ChatModeEnum}
   * @memberof ChatDetailV2Dto
   */
  mode: ChatModeEnum;
  /**
   * Whether to show new board suggestion
   * @type {boolean}
   * @memberof ChatDetailV2Dto
   */
  show_new_board_suggestion: boolean;
  /**
   * New board chat ID if applicable
   * @type {string}
   * @memberof ChatDetailV2Dto
   */
  new_board_chat_id?: string;
  /**
   * Associated board IDs
   * @type {Array<string>}
   * @memberof ChatDetailV2Dto
   */
  board_ids?: Array<string>;
  /**
   * Board item information
   * @type {object}
   * @memberof ChatDetailV2Dto
   */
  board_item?: object;
  /**
   * Chat messages (mixed user and assistant messages)
   * @type {Array<ChatDetailV2DtoMessagesInner>}
   * @memberof ChatDetailV2Dto
   */
  messages: Array<ChatDetailV2DtoMessagesInner>;
}

/**
 * Check if a given object implements the ChatDetailV2Dto interface.
 */
export function instanceOfChatDetailV2Dto(value: object): value is ChatDetailV2Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('creator_id' in value) || value.creator_id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('title' in value) || value.title === undefined) return false;
  if (!('origin' in value) || value.origin === undefined) return false;
  if (!('mode' in value) || value.mode === undefined) return false;
  if (!('show_new_board_suggestion' in value) || value.show_new_board_suggestion === undefined)
    return false;
  if (!('messages' in value) || value.messages === undefined) return false;
  return true;
}

export function ChatDetailV2DtoFromJSON(json: any): ChatDetailV2Dto {
  return ChatDetailV2DtoFromJSONTyped(json, false);
}

export function ChatDetailV2DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ChatDetailV2Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    creator_id: json.creator_id,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    title: json.title,
    origin: ChatOriginDtoFromJSON(json.origin),
    board_id: json.board_id == null ? undefined : json.board_id,
    mode: ChatModeEnumFromJSON(json.mode),
    show_new_board_suggestion: json.show_new_board_suggestion,
    new_board_chat_id: json.new_board_chat_id == null ? undefined : json.new_board_chat_id,
    board_ids: json.board_ids == null ? undefined : json.board_ids,
    board_item: json.board_item == null ? undefined : json.board_item,
    messages: (json.messages as Array<any>).map(ChatDetailV2DtoMessagesInnerFromJSON),
  };
}

export function ChatDetailV2DtoToJSON(json: any): ChatDetailV2Dto {
  return ChatDetailV2DtoToJSONTyped(json, false);
}

export function ChatDetailV2DtoToJSONTyped(
  value?: ChatDetailV2Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    creator_id: value.creator_id,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    title: value.title,
    origin: ChatOriginDtoToJSON(value.origin),
    board_id: value.board_id,
    mode: ChatModeEnumToJSON(value.mode),
    show_new_board_suggestion: value.show_new_board_suggestion,
    new_board_chat_id: value.new_board_chat_id,
    board_ids: value.board_ids,
    board_item: value.board_item,
    messages: (value.messages as Array<any>).map(ChatDetailV2DtoMessagesInnerToJSON),
  };
}
