/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CompletionBlockStatusEnum } from './CompletionBlockStatusEnum';
import {
  CompletionBlockStatusEnumFromJSON,
  CompletionBlockStatusEnumFromJSONTyped,
  CompletionBlockStatusEnumToJSON,
  CompletionBlockStatusEnumToJSONTyped,
} from './CompletionBlockStatusEnum';
import type { CompletionBlockTypeEnum } from './CompletionBlockTypeEnum';
import {
  CompletionBlockTypeEnumFromJSON,
  CompletionBlockTypeEnumFromJSONTyped,
  CompletionBlockTypeEnumToJSON,
  CompletionBlockTypeEnumToJSONTyped,
} from './CompletionBlockTypeEnum';

/**
 *
 * @export
 * @interface ReasoningBlockV2Dto
 */
export interface ReasoningBlockV2Dto {
  /**
   * Block ID
   * @type {string}
   * @memberof ReasoningBlockV2Dto
   */
  id: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof ReasoningBlockV2Dto
   */
  created_at: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof ReasoningBlockV2Dto
   */
  updated_at: Date;
  /**
   * Block type
   * @type {CompletionBlockTypeEnum}
   * @memberof ReasoningBlockV2Dto
   */
  type: CompletionBlockTypeEnum;
  /**
   * Block status
   * @type {CompletionBlockStatusEnum}
   * @memberof ReasoningBlockV2Dto
   */
  status: CompletionBlockStatusEnum;
  /**
   * Message ID this block belongs to
   * @type {string}
   * @memberof ReasoningBlockV2Dto
   */
  message_id: string;
  /**
   * Extra metadata
   * @type {object}
   * @memberof ReasoningBlockV2Dto
   */
  extra?: object;
  /**
   * Reasoning data
   * @type {string}
   * @memberof ReasoningBlockV2Dto
   */
  data: string;
}

/**
 * Check if a given object implements the ReasoningBlockV2Dto interface.
 */
export function instanceOfReasoningBlockV2Dto(value: object): value is ReasoningBlockV2Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('type' in value) || value.type === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('message_id' in value) || value.message_id === undefined) return false;
  if (!('data' in value) || value.data === undefined) return false;
  return true;
}

export function ReasoningBlockV2DtoFromJSON(json: any): ReasoningBlockV2Dto {
  return ReasoningBlockV2DtoFromJSONTyped(json, false);
}

export function ReasoningBlockV2DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ReasoningBlockV2Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    type: CompletionBlockTypeEnumFromJSON(json.type),
    status: CompletionBlockStatusEnumFromJSON(json.status),
    message_id: json.message_id,
    extra: json.extra == null ? undefined : json.extra,
    data: json.data,
  };
}

export function ReasoningBlockV2DtoToJSON(json: any): ReasoningBlockV2Dto {
  return ReasoningBlockV2DtoToJSONTyped(json, false);
}

export function ReasoningBlockV2DtoToJSONTyped(
  value?: ReasoningBlockV2Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    type: CompletionBlockTypeEnumToJSON(value.type),
    status: CompletionBlockStatusEnumToJSON(value.status),
    message_id: value.message_id,
    extra: value.extra,
    data: value.data,
  };
}
