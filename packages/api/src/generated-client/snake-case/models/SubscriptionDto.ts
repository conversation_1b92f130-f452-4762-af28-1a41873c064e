/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { BillingInterval } from './BillingInterval';
import {
  BillingIntervalFromJSON,
  BillingIntervalFromJSONTyped,
  BillingIntervalToJSON,
  BillingIntervalToJSONTyped,
} from './BillingInterval';
import type { RenewChangeDto } from './RenewChangeDto';
import {
  RenewChangeDtoFromJSON,
  RenewChangeDtoFromJSONTyped,
  RenewChangeDtoToJSON,
  RenewChangeDtoToJSONTyped,
} from './RenewChangeDto';
import type { SubscriptionProductTier } from './SubscriptionProductTier';
import {
  SubscriptionProductTierFromJSON,
  SubscriptionProductTierFromJSONTyped,
  SubscriptionProductTierToJSON,
  SubscriptionProductTierToJSONTyped,
} from './SubscriptionProductTier';

/**
 *
 * @export
 * @interface SubscriptionDto
 */
export interface SubscriptionDto {
  /**
   * 订阅 ID
   * @type {string}
   * @memberof SubscriptionDto
   */
  id: string;
  /**
   * 产品等级
   * @type {SubscriptionProductTier}
   * @memberof SubscriptionDto
   */
  product_tier: SubscriptionProductTier;
  /**
   * 计费周期
   * @type {BillingInterval}
   * @memberof SubscriptionDto
   */
  billing_interval: BillingInterval;
  /**
   * 状态
   * @type {string}
   * @memberof SubscriptionDto
   */
  status: string;
  /**
   * 续期周期锚点
   * @type {Date}
   * @memberof SubscriptionDto
   */
  renew_cycle_anchor: Date;
  /**
   * 当前周期开始时间
   * @type {Date}
   * @memberof SubscriptionDto
   */
  current_period_start: Date;
  /**
   * 当前周期结束时间
   * @type {Date}
   * @memberof SubscriptionDto
   */
  current_period_end: Date;
  /**
   * 是否到期取消
   * @type {boolean}
   * @memberof SubscriptionDto
   */
  cancel_at_period_end: boolean;
  /**
   * 续期变更
   * @type {RenewChangeDto}
   * @memberof SubscriptionDto
   */
  renew_change?: RenewChangeDto;
  /**
   * 提供商
   * @type {string}
   * @memberof SubscriptionDto
   */
  provider: string;
  /**
   * 外部 ID
   * @type {string}
   * @memberof SubscriptionDto
   */
  external_id: string;
  /**
   * 未支付账单 URL
   * @type {string}
   * @memberof SubscriptionDto
   */
  unpaid_invoice_url?: string;
}

/**
 * Check if a given object implements the SubscriptionDto interface.
 */
export function instanceOfSubscriptionDto(value: object): value is SubscriptionDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('product_tier' in value) || value.product_tier === undefined) return false;
  if (!('billing_interval' in value) || value.billing_interval === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('renew_cycle_anchor' in value) || value.renew_cycle_anchor === undefined) return false;
  if (!('current_period_start' in value) || value.current_period_start === undefined) return false;
  if (!('current_period_end' in value) || value.current_period_end === undefined) return false;
  if (!('cancel_at_period_end' in value) || value.cancel_at_period_end === undefined) return false;
  if (!('provider' in value) || value.provider === undefined) return false;
  if (!('external_id' in value) || value.external_id === undefined) return false;
  return true;
}

export function SubscriptionDtoFromJSON(json: any): SubscriptionDto {
  return SubscriptionDtoFromJSONTyped(json, false);
}

export function SubscriptionDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): SubscriptionDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    product_tier: SubscriptionProductTierFromJSON(json.product_tier),
    billing_interval: BillingIntervalFromJSON(json.billing_interval),
    status: json.status,
    renew_cycle_anchor: new Date(json.renew_cycle_anchor),
    current_period_start: new Date(json.current_period_start),
    current_period_end: new Date(json.current_period_end),
    cancel_at_period_end: json.cancel_at_period_end,
    renew_change: json.renew_change == null ? undefined : RenewChangeDtoFromJSON(json.renew_change),
    provider: json.provider,
    external_id: json.external_id,
    unpaid_invoice_url: json.unpaid_invoice_url == null ? undefined : json.unpaid_invoice_url,
  };
}

export function SubscriptionDtoToJSON(json: any): SubscriptionDto {
  return SubscriptionDtoToJSONTyped(json, false);
}

export function SubscriptionDtoToJSONTyped(
  value?: SubscriptionDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    product_tier: SubscriptionProductTierToJSON(value.product_tier),
    billing_interval: BillingIntervalToJSON(value.billing_interval),
    status: value.status,
    renew_cycle_anchor: value.renew_cycle_anchor.toISOString(),
    current_period_start: value.current_period_start.toISOString(),
    current_period_end: value.current_period_end.toISOString(),
    cancel_at_period_end: value.cancel_at_period_end,
    renew_change: RenewChangeDtoToJSON(value.renew_change),
    provider: value.provider,
    external_id: value.external_id,
    unpaid_invoice_url: value.unpaid_invoice_url,
  };
}
