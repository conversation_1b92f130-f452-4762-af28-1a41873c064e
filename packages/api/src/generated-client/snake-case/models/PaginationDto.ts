/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface PaginationDto
 */
export interface PaginationDto {
  /**
   * Current page (0-based)
   * @type {number}
   * @memberof PaginationDto
   */
  current: number;
  /**
   * Page size
   * @type {number}
   * @memberof PaginationDto
   */
  page_size: number;
  /**
   * Total number of items
   * @type {number}
   * @memberof PaginationDto
   */
  total: number;
}

/**
 * Check if a given object implements the PaginationDto interface.
 */
export function instanceOfPaginationDto(value: object): value is PaginationDto {
  if (!('current' in value) || value.current === undefined) return false;
  if (!('page_size' in value) || value.page_size === undefined) return false;
  if (!('total' in value) || value.total === undefined) return false;
  return true;
}

export function PaginationDtoFromJSON(json: any): PaginationDto {
  return PaginationDtoFromJSONTyped(json, false);
}

export function PaginationDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): PaginationDto {
  if (json == null) {
    return json;
  }
  return {
    current: json.current,
    page_size: json.page_size,
    total: json.total,
  };
}

export function PaginationDtoToJSON(json: any): PaginationDto {
  return PaginationDtoToJSONTyped(json, false);
}

export function PaginationDtoToJSONTyped(
  value?: PaginationDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    current: value.current,
    page_size: value.page_size,
    total: value.total,
  };
}
