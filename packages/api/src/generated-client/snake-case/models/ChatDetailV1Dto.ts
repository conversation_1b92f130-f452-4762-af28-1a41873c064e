/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ChatDetailV1DtoMessages } from './ChatDetailV1DtoMessages';
import {
  ChatDetailV1DtoMessagesFromJSON,
  ChatDetailV1DtoMessagesFromJSONTyped,
  ChatDetailV1DtoMessagesToJSON,
  ChatDetailV1DtoMessagesToJSONTyped,
} from './ChatDetailV1DtoMessages';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  Chat<PERSON><PERSON><PERSON><PERSON>toToJSON,
  Chat<PERSON><PERSON>inDtoToJSONTyped,
} from './ChatOriginDto';

/**
 *
 * @export
 * @interface ChatDetailV1Dto
 */
export interface ChatDetailV1Dto {
  /**
   * Chat ID
   * @type {string}
   * @memberof ChatDetailV1Dto
   */
  id: string;
  /**
   * Creator user ID
   * @type {string}
   * @memberof ChatDetailV1Dto
   */
  creator_id: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof ChatDetailV1Dto
   */
  created_at: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof ChatDetailV1Dto
   */
  updated_at: Date;
  /**
   * Chat title
   * @type {string}
   * @memberof ChatDetailV1Dto
   */
  title: string;
  /**
   * Chat origin context
   * @type {ChatOriginDto}
   * @memberof ChatDetailV1Dto
   */
  origin: ChatOriginDto;
  /**
   * Board ID if applicable
   * @type {string}
   * @memberof ChatDetailV1Dto
   */
  board_id?: string;
  /**
   *
   * @type {ChatDetailV1DtoMessages}
   * @memberof ChatDetailV1Dto
   */
  messages: ChatDetailV1DtoMessages;
  /**
   * Associated board IDs
   * @type {Array<string>}
   * @memberof ChatDetailV1Dto
   */
  board_ids?: Array<string>;
  /**
   * Board item information
   * @type {object}
   * @memberof ChatDetailV1Dto
   */
  board_item?: object;
}

/**
 * Check if a given object implements the ChatDetailV1Dto interface.
 */
export function instanceOfChatDetailV1Dto(value: object): value is ChatDetailV1Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('creator_id' in value) || value.creator_id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('title' in value) || value.title === undefined) return false;
  if (!('origin' in value) || value.origin === undefined) return false;
  if (!('messages' in value) || value.messages === undefined) return false;
  return true;
}

export function ChatDetailV1DtoFromJSON(json: any): ChatDetailV1Dto {
  return ChatDetailV1DtoFromJSONTyped(json, false);
}

export function ChatDetailV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ChatDetailV1Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    creator_id: json.creator_id,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    title: json.title,
    origin: ChatOriginDtoFromJSON(json.origin),
    board_id: json.board_id == null ? undefined : json.board_id,
    messages: ChatDetailV1DtoMessagesFromJSON(json.messages),
    board_ids: json.board_ids == null ? undefined : json.board_ids,
    board_item: json.board_item == null ? undefined : json.board_item,
  };
}

export function ChatDetailV1DtoToJSON(json: any): ChatDetailV1Dto {
  return ChatDetailV1DtoToJSONTyped(json, false);
}

export function ChatDetailV1DtoToJSONTyped(
  value?: ChatDetailV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    creator_id: value.creator_id,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    title: value.title,
    origin: ChatOriginDtoToJSON(value.origin),
    board_id: value.board_id,
    messages: ChatDetailV1DtoMessagesToJSON(value.messages),
    board_ids: value.board_ids,
    board_item: value.board_item,
  };
}
