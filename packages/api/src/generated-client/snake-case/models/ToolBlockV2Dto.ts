/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CompletionBlockStatusEnum } from './CompletionBlockStatusEnum';
import {
  CompletionBlockStatusEnumFromJSON,
  CompletionBlockStatusEnumFromJSONTyped,
  CompletionBlockStatusEnumToJSON,
  CompletionBlockStatusEnumToJSONTyped,
} from './CompletionBlockStatusEnum';
import type { CompletionBlockTypeEnum } from './CompletionBlockTypeEnum';
import {
  CompletionBlockTypeEnumFromJSON,
  CompletionBlockTypeEnumFromJSONTyped,
  CompletionBlockTypeEnumToJSON,
  CompletionBlockTypeEnumToJSONTyped,
} from './CompletionBlockTypeEnum';

/**
 *
 * @export
 * @interface ToolBlockV2Dto
 */
export interface ToolBlockV2Dto {
  /**
   * Block ID
   * @type {string}
   * @memberof ToolBlockV2Dto
   */
  id: string;
  /**
   * Creation timestamp
   * @type {Date}
   * @memberof ToolBlockV2Dto
   */
  created_at: Date;
  /**
   * Update timestamp
   * @type {Date}
   * @memberof ToolBlockV2Dto
   */
  updated_at: Date;
  /**
   * Block type
   * @type {CompletionBlockTypeEnum}
   * @memberof ToolBlockV2Dto
   */
  type: CompletionBlockTypeEnum;
  /**
   * Block status
   * @type {CompletionBlockStatusEnum}
   * @memberof ToolBlockV2Dto
   */
  status: CompletionBlockStatusEnum;
  /**
   * Message ID this block belongs to
   * @type {string}
   * @memberof ToolBlockV2Dto
   */
  message_id: string;
  /**
   * Extra metadata
   * @type {object}
   * @memberof ToolBlockV2Dto
   */
  extra?: object;
  /**
   * Tool ID
   * @type {string}
   * @memberof ToolBlockV2Dto
   */
  tool_id: string;
  /**
   * Tool name
   * @type {string}
   * @memberof ToolBlockV2Dto
   */
  tool_name: string;
  /**
   * Tool arguments
   * @type {object}
   * @memberof ToolBlockV2Dto
   */
  tool_arguments?: object;
  /**
   * Tool execution result
   * @type {object}
   * @memberof ToolBlockV2Dto
   */
  tool_result?: object;
  /**
   * Tool response text
   * @type {string}
   * @memberof ToolBlockV2Dto
   */
  tool_response?: string;
  /**
   * Tool generation time in milliseconds
   * @type {number}
   * @memberof ToolBlockV2Dto
   */
  tool_generate_elapsed_ms?: number;
  /**
   * Tool execution time in milliseconds
   * @type {number}
   * @memberof ToolBlockV2Dto
   */
  tool_execute_elapsed_ms?: number;
}

/**
 * Check if a given object implements the ToolBlockV2Dto interface.
 */
export function instanceOfToolBlockV2Dto(value: object): value is ToolBlockV2Dto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('type' in value) || value.type === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('message_id' in value) || value.message_id === undefined) return false;
  if (!('tool_id' in value) || value.tool_id === undefined) return false;
  if (!('tool_name' in value) || value.tool_name === undefined) return false;
  return true;
}

export function ToolBlockV2DtoFromJSON(json: any): ToolBlockV2Dto {
  return ToolBlockV2DtoFromJSONTyped(json, false);
}

export function ToolBlockV2DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ToolBlockV2Dto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    type: CompletionBlockTypeEnumFromJSON(json.type),
    status: CompletionBlockStatusEnumFromJSON(json.status),
    message_id: json.message_id,
    extra: json.extra == null ? undefined : json.extra,
    tool_id: json.tool_id,
    tool_name: json.tool_name,
    tool_arguments: json.tool_arguments == null ? undefined : json.tool_arguments,
    tool_result: json.tool_result == null ? undefined : json.tool_result,
    tool_response: json.tool_response == null ? undefined : json.tool_response,
    tool_generate_elapsed_ms:
      json.tool_generate_elapsed_ms == null ? undefined : json.tool_generate_elapsed_ms,
    tool_execute_elapsed_ms:
      json.tool_execute_elapsed_ms == null ? undefined : json.tool_execute_elapsed_ms,
  };
}

export function ToolBlockV2DtoToJSON(json: any): ToolBlockV2Dto {
  return ToolBlockV2DtoToJSONTyped(json, false);
}

export function ToolBlockV2DtoToJSONTyped(
  value?: ToolBlockV2Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    type: CompletionBlockTypeEnumToJSON(value.type),
    status: CompletionBlockStatusEnumToJSON(value.status),
    message_id: value.message_id,
    extra: value.extra,
    tool_id: value.tool_id,
    tool_name: value.tool_name,
    tool_arguments: value.tool_arguments,
    tool_result: value.tool_result,
    tool_response: value.tool_response,
    tool_generate_elapsed_ms: value.tool_generate_elapsed_ms,
    tool_execute_elapsed_ms: value.tool_execute_elapsed_ms,
  };
}
