export enum YouMindEnv {
  PROD = 'production',
  PREVIEW = 'preview',
  /** @deprecated 使用 LOCAL 代替 */
  DEV = 'development',
  LOCAL = 'local',
}

export function isNode(): boolean {
  return (
    typeof process !== 'undefined' && process.versions != null && process.versions.node != null
  );
}

export function getServicesEnv(): YouMindEnv {
  if (isProductionServices()) {
    return YouMindEnv.PROD;
  } else if (isPreviewServices()) {
    return YouMindEnv.PREVIEW;
  } else if (isLocalServices()) {
    return YouMindEnv.LOCAL;
  }

  throw new Error('Cannot determine services environment');
}

/**
 * 检查是否使用生产环境服务（数据库、API等）
 * 基于 YOUMIND_ENV 环境变量判断
 */
export function isProductionServices(): boolean {
  const env = process.env.YOUMIND_ENV;
  return env === 'production';
}

/**
 * 检查是否使用预览环境服务（数据库、API等）
 * 基于 YOUMIND_ENV 环境变量判断，未设置时默认为预览环境
 */
export function isPreviewServices(): boolean {
  const env = process.env.YOUMIND_ENV;
  return !env || env === 'preview';
}

/**
 * 检查是否使用本地环境服务（数据库、API等）
 * 用于本地 docker compose 一把梭启动所有环境、所有依赖时
 */
export function isLocalServices(): boolean {
  throw new Error('isLocalServices() is not implemented');
}

/**
 * 检查代码是否在本地开发环境运行
 * 服务端：检查 NODE_ENV 或 hostname
 * 客户端：检查浏览器 hostname
 */
export function isLocalRuntime(): boolean {
  if (isServerSide()) {
    // 服务端环境
    return process.env.NODE_ENV === 'development';
  } else {
    // 客户端环境
    // 使用 globalThis 来避免 TypeScript 编译错误
    const hostname = (globalThis as any).window?.location?.hostname;
    return hostname === 'localhost' || hostname === '127.0.0.1';
  }
}

/**
 * 检查代码是否在已部署的服务器上运行
 * 与 isLocalRuntime() 相反
 */
export function isDeployedRuntime(): boolean {
  return !isLocalRuntime();
}

/**
 * 检查当前代码是否在客户端运行
 * 在浏览器环境返回 true，Node.js 环境返回 false
 */
export function isClientSide(): boolean {
  // 使用字符串检查来避免直接引用可能不存在的全局变量
  return (
    typeof globalThis !== 'undefined' &&
    typeof (globalThis as any).window !== 'undefined' &&
    typeof (globalThis as any).document !== 'undefined'
  );
}

/**
 * 检查当前代码是否在服务端运行
 * 在 Node.js 环境返回 true，浏览器环境返回 false
 */
export function isServerSide(): boolean {
  return !isClientSide();
}

/**
 * @deprecated 使用 isProductionServices() 或 isPreviewServices() 代替
 */
export function getEnv() {
  console.warn(
    'getEnv() is deprecated. Use isProductionServices() or isPreviewServices() instead.',
  );
  const env =
    process.env.YOUMIND_ENV ||
    process.env.NEXT_PUBLIC_YOUMIND_ENV ||
    process.env.NODE_ENV ||
    process.env.NEXT_PUBLIC_NODE_ENV;
  if (!env) {
    throw new Error(
      "YOUMIND_ENV or NEXT_PUBLIC_YOUMIND_ENV or NODE_ENV or NEXT_PUBLIC_NODE_ENV is not set, you must specify one in env, call 双扬 if you don't know what this is",
    );
  }

  if (!Object.values(YouMindEnv).includes(env as YouMindEnv)) {
    throw new Error(`Invalid environment: ${env}`);
  }

  return env as YouMindEnv;
}

/**
 * @deprecated 使用 isPreviewServices() 代替
 */
export function isPre() {
  console.warn('isPre() is deprecated. Use isPreviewServices() instead.');
  return isPreview();
}

/**
 * @deprecated 使用 isProductionServices() 代替
 */
export function isProd() {
  console.warn('isProd() is deprecated. Use isProductionServices() instead.');
  try {
    return getEnv() === YouMindEnv.PROD;
  } catch {
    return isProductionServices();
  }
}

/**
 * @deprecated 使用 isPreviewServices() 代替
 */
export function isPreview() {
  console.warn('isPreview() is deprecated. Use isPreviewServices() instead.');
  try {
    return getEnv() === YouMindEnv.PREVIEW;
  } catch {
    return isPreviewServices();
  }
}

/**
 * @deprecated 使用 isLocalRuntime() 代替
 * 注意：原 isDev() 混淆了"开发环境"和"本地运行"的概念
 */
export function isDev() {
  console.warn(
    'isDev() is deprecated. Use isLocalRuntime() to check if running locally, or check NODE_ENV directly for build mode.',
  );
  return isLocalRuntime();
}
