'use client';

import { useEffect } from 'react';
import { toast } from 'sonner';
import { copy } from '../../lib/clipboard';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { YouMindLogo, YouMindTextLogo } from './logo';

interface InternalErrorProps {
  error?: Error & { digest?: string };
  from?: 'client' | 'server';
  className?: string;
  onGoHomeClick?: () => void;
  hideTopBar?: boolean;
}

export function InternalError({
  error,
  from = 'client',
  className,
  hideTopBar = false,
  onGoHomeClick,
}: InternalErrorProps) {
  const handleContactUs = async () => {
    try {
      await copy('<EMAIL>', 'text');
      toast.success('Email address copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy email address');
      console.error('复制失败:', err);
    }
  };

  // 根据来源显示不同的描述文案
  const getDescription = () => {
    if (from === 'server') {
      return {
        line1: 'Hmm...Our server ran into a problem, and our',
        line2: 'engineers are working on it.',
      };
    } else {
      return {
        line1: "Something didn't load quite right — try",
        line2: 'refreshing the page.',
      };
    }
  };

  const description = getDescription();

  useEffect(() => {
    if (error) {
      console.error(`Internal Error from ${from}:`, error);
    }
  }, [error, from]);

  return (
    <div
      className={cn('min-h-screen bg-gray-50 flex flex-col relative overflow-hidden', className)}
    >
      {/* 圆形虚化背景 */}
      <div
        className="absolute w-[1280px] h-[1280px] left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 shrink-0 rounded-full blur-[60px]"
        style={{
          background: 'rgba(128, 128, 229, 0.06)',
        }}
      />

      {/* 顶部 Logo */}
      {!hideTopBar && (
        <div
          className="p-6 relative z-10 flex items-center cursor-pointer"
          onClick={() => {
            if (onGoHomeClick) {
              onGoHomeClick();
            } else {
              window.location.href = '/';
            }
          }}
        >
          <YouMindLogo size={16} />
          <YouMindTextLogo className="ml-[6px]" size={16} />
        </div>
      )}

      {/* 主内容区域 */}
      <div className="flex-1 flex items-center justify-center px-6 relative z-10">
        <div className="text-center max-w-md">
          {/* Internal Error 插画 */}
          <div className="mb-8">
            <img
              src="https://cdn.gooo.ai/assets/internal-error-illustration.png"
              alt="Internal Error Illustration"
              width={240}
              height={240}
              className="mx-auto"
            />
          </div>

          {/* 标题 */}
          <h1 className="text-2xl font-semibold text-foreground mb-2">Internal error.</h1>

          {/* 描述文本 */}
          <div className="text-caption-fg mb-1 text-sm">{description.line1}</div>
          <div className="text-caption-fg mb-6 text-sm">{description.line2}</div>

          {/* Contact US 按钮 */}
          <Button
            onClick={handleContactUs}
            size="lg"
            variant="outline"
            className="bg-transparent border-border"
          >
            Contact us
          </Button>
        </div>
      </div>
    </div>
  );
}
