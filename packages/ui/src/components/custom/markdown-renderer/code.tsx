import { CODEBLOCK_LANGUAGES } from '@repo/editor-common';
import { toast } from '@repo/ui/components/ui/sonner';
import copy from 'copy-to-clipboard';
import { Copy } from 'lucide-react';
import mermaid from 'mermaid';
import { useEffect, useRef } from 'react';
import Syntax<PERSON>ighlighter from 'react-syntax-highlighter';

import { atelierForestLight } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { escapeMermaidMarkdown, formatSVG } from './util';

export function CodeRenderer({
  className,
  code = '',
  ...props
}: {
  className?: string;
  code: string;
}) {
  const match = /language-(\w+)/.exec(className || '');
  const lang = match?.[1];
  const codeString = code.replace(/\n$/, '');

  if (match) {
    let preview: React.ReactNode;
    if (lang === 'mermaid') {
      preview = <MermaidComponent source={code} />;
    } else if (lang === 'xml' || lang === 'svg') {
      if (code.includes('<svg')) {
        preview = <div className="svg-container">{formatSVG(code as string)}</div>;
      }
    }

    return (
      <div className="code-block-container">
        {lang && (
          <div className="code-language">
            {CODEBLOCK_LANGUAGES.find((l) => l.value === lang)?.label}
          </div>
        )}
        <div className="p-4 pt-0">
          <button
            type="button"
            className="absolute inline-flex items-center justify-center w-6 h-6 text-sm font-medium rounded-md ym-code-block-copy-button z-1 right-1 top-1 whitespace-nowrap text-secondary-fg ring-offset-background hover:bg-accent hover:text-accent-foreground disabled:pointer-events-none disabled:opacity-50"
          >
            <Copy
              size={16}
              onClick={() => {
                copy(code);
                toast('Copied to clipboard');
              }}
            />
          </button>
          {preview || (
            <SyntaxHighlighter
              language="python"
              style={atelierForestLight}
              customStyle={{
                background: 'transparent',
                border: 'none',
                boxShadow: 'none',
              }}
            >
              {codeString}
            </SyntaxHighlighter>
          )}
        </div>
      </div>
    );
  }

  // TODO 当前没有实现 inline code
  return (
    <code className={className} {...props}>
      {code}
    </code>
  );
}

const MermaidComponent = ({ source }: { source: string }) => {
  const mermaidRef = useRef<HTMLDivElement>(null);
  const idRef = useRef<string>(Math.random().toString(36).substring(2, 15));

  useEffect(() => {
    const initializeMermaid = async () => {
      if (mermaidRef.current) {
        try {
          mermaid.initialize({
            startOnLoad: true,
            // @see https://github.com/mermaid-js/mermaid/issues/4358
            suppressErrorRendering: true,
          });

          // @see https://linear.app/youmind/issue/YOU-3710/mermaid-%E7%94%9F%E6%88%90%E5%A4%B1%E8%B4%A5-again
          // 转义描述中可能出现的 markdown 语法，例如："1. Label" -> "1\. Label"
          source = escapeMermaidMarkdown(source);

          mermaidRef.current.innerHTML = source;
          const { svg, bindFunctions } = await mermaid.render(
            `mermaid-diagram-${idRef.current}`,
            source,
          );
          mermaidRef.current.innerHTML = svg;
          bindFunctions?.(mermaidRef.current);
        } catch (error) {
          console.error(
            'Failed to render Mermaid diagram. Displaying source as plain text instead.',
            error,
          );
          // Fallback: Display the original source text
          if (mermaidRef.current) {
            mermaidRef.current.innerHTML = ''; // Clear previous content
            const preElement = document.createElement('pre');
            const codeElement = document.createElement('code');
            codeElement.textContent = source; // Safely set text content
            preElement.appendChild(codeElement);
            mermaidRef.current.appendChild(preElement);
          }
        }
      }
    };

    initializeMermaid();

    // Clean up mermaid instance when unmounting; doing nothing at the momemt
    return () => {};
  }, [source]);

  return <div ref={mermaidRef}></div>;
};

export default MermaidComponent;
