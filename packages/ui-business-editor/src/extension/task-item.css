.youmind-editor-node-task-item-ui {
  display: flex;
  align-items: flex-start;
  margin: 0.5rem 0;

  p {
    margin: 0;
    transition:
      color 0.18s cubic-bezier(0.25, 0.46, 0.45, 0.94),
      text-decoration 0.18s cubic-bezier(0.25, 0.46, 0.45, 0.94),
      transform 0.18s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* 确保内容区域有足够的高度 */
  > div {
    flex: 1;
    line-height: 1.5rem;
  }

  label {
    padding-right: 10px;
    display: flex;
    align-items: center;
    padding-top: 4.5px;

    input[type="checkbox"] {
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      width: 16px;
      min-width: 16px;
      height: 16px;
      box-shadow: rgba(31, 34, 37, 0.22) 0px 0px 0px 1.5px inset;
      position: relative;
      vertical-align: middle;
      box-sizing: content-box;
      user-select: none;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      transition:
        background 0.18s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        box-shadow 0.18s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        transform 0.18s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      cursor: pointer;

      &::after {
        content: "";
        position: absolute;
        top: 1.5px;
        left: 5px;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        border-radius: 0 1px 1px 1px;
        transform: rotate(45deg) scale(0) translateY(2px);
        opacity: 0;
        transition:
          transform 0.18s cubic-bezier(0.34, 1.56, 0.64, 1),
          opacity 0.18s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      &:hover:not(:checked) {
        background: rgba(31, 34, 37, 0.04);
        transform: scale(1.02);
      }

      &:checked {
        background-color: hsla(var(--primary));
        box-shadow: rgba(31, 34, 37, 0.12) 0px 0px 0px 1px inset;
        transform: scale(1);

        &::after {
          transform: rotate(45deg) scale(1) translateY(0);
          opacity: 1;
        }
      }
    }
  }

  &:has(input:checked) {
    p {
      text-decoration: line-through 1px hsla(var(--gray2));
      color: hsla(var(--gray2));
      transform: translateX(2px);
    }
  }
}
