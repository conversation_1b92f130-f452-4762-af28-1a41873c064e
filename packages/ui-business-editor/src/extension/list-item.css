/* ========== 基础列表项样式 ========== */
.youmind-editor-node-list-item-ui {
  display: list-item;
  margin: 4px 0;
}

/* ========== 公共列表项样式 ========== */
.youmind-editor-node-bullet-list-ui .youmind-editor-node-list-item-ui,
.youmind-editor-node-ordered-list-ui .youmind-editor-node-list-item-ui {
  list-style: none;
  position: relative;
  padding-left: 1.5em; /* 统一的左边距 */

  /* 确保没有原生的列表标记 */
  &::marker {
    content: none;
  }

  /* 公共的伪元素样式 */
  &::before {
    position: absolute;
    right: calc(100% - 1.5em + 0.2em); /* 统一的定位方式 */
    top: 0;
    font-weight: normal;
    color: hsla(var(--foreground));
    font-size: inherit;
    line-height: inherit;
    min-width: 1.2em; /* 统一的最小宽度 */
  }
}

/* ========== 有序列表特定样式 ========== */
.youmind-editor-node-ordered-list-ui {
  /* 每个有序列表都重置自己的计数器和层级计数器 */
  counter-reset: ordered-list-counter list-depth-counter;

  /* 递增层级计数器 */
  counter-increment: list-depth-counter;

  /* 只有直接的有序列表项才递增计数器 */
  > .youmind-editor-node-list-item-ui {
    counter-increment: ordered-list-counter;

    &::before {
      text-align: right; /* 有序列表标头右对齐，让数字向左展开 */
    }
  }
}

/* ========== 无序列表特定样式 ========== */
/* 使用更高优先级的选择器，确保在嵌套情况下也能正确应用 */
.youmind-editor-node-bullet-list-ui .youmind-editor-node-list-item-ui::before,
.youmind-editor-node-ordered-list-ui
  .youmind-editor-node-bullet-list-ui
  .youmind-editor-node-list-item-ui::before {
  content: "•";
  text-align: center !important; /* 无序列表标头居中对齐，使用 !important 确保优先级 */
  /* 无序列表项不参与有序列表的计数 */
  counter-increment: none !important;
}

/*
 * 穷举式循环模式：数字 -> 字母 -> 罗马数字 -> 数字 -> ...
 * 支持21层嵌套，确保循环正确
 */

/* 第1层：数字 (1, 2, 3...) */
.youmind-editor-node-ordered-list-ui > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, decimal) ". ";
}

/* 第2层：小写字母 (a, b, c...) */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-alpha) ". ";
}

/* 第3层：小写罗马数字 (i, ii, iii...) */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-roman) ". ";
}

/* 第4层：重新回到数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, decimal) ". ";
}

/* 第5层：重新回到字母 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-alpha) ". ";
}

/* 第6层：重新回到罗马数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-roman) ". ";
}

/* 第7层：重新回到数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, decimal) ". ";
}

/* 第8层：重新回到字母 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-alpha) ". ";
}

/* 第9层：重新回到罗马数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-roman) ". ";
}

/* 第10层：重新回到数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, decimal) ". ";
}

/* 第11层：重新回到字母 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-alpha) ". ";
}

/* 第12层：重新回到罗马数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-roman) ". ";
}

/* 第13层：重新回到数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, decimal) ". ";
}

/* 第14层：重新回到字母 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-alpha) ". ";
}

/* 第15层：重新回到罗马数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-roman) ". ";
}

/* 第16层：重新回到数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, decimal) ". ";
}

/* 第17层：重新回到字母 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-alpha) ". ";
}

/* 第18层：重新回到罗马数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-roman) ". ";
}

/* 第19层：重新回到数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, decimal) ". ";
}

/* 第20层：重新回到字母 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-alpha) ". ";
}

/* 第21层：重新回到罗马数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(ordered-list-counter, lower-roman) ". ";
}
