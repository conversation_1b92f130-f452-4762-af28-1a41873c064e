import {
  DIFF_CHANGE_TYPE,
  diffTransformUtils,
  markdownParse,
  markdownSerializer,
} from '@repo/editor-common';
import { Extension } from '@tiptap/core';
import { DOMParser, DOMSerializer } from '@tiptap/pm/model';
import { Plugin, PluginKey } from '@tiptap/pm/state';

/** YouMind 编辑器内容的 HTML 标记，用于识别来自编辑器的内容 */
export const YOU_MIND_HTML_MARKER = '<!-- youmind-editor-content -->';

export const ClipboardAdapterExtensionName = 'clipboardAdapter';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    clipboardAdapter: {
      copyExternalMarkdownContent: () => ReturnType;
      copyPlainTextContent: () => ReturnType;
    };
  }
}

interface ClipboardData {
  [key: string]: string;
}

export async function writeToClipboard(data: ClipboardData): Promise<void> {
  // 使用现代的 Clipboard API，支持自定义键值对
  if (navigator.clipboard && window.isSecureContext) {
    try {
      const items: ClipboardItem[] = [];
      const clipboardData: Record<string, Blob> = {};

      Object.entries(data).forEach(([mimeType, content]) => {
        clipboardData[mimeType] = new Blob([content], { type: mimeType });
      });

      items.push(new ClipboardItem(clipboardData));
      await navigator.clipboard.write(items);
      return;
    } catch (error) {
      console.warn('Modern clipboard API failed:', error);
      throw new Error('Clipboard operation failed: Modern API not available or permission denied');
    }
  }

  // 如果不支持现代 Clipboard API，抛出错误
  throw new Error(
    'Clipboard operation failed: Modern Clipboard API not supported in current context',
  );
}

export const ClipboardAdapter = Extension.create({
  name: ClipboardAdapterExtensionName,

  addCommands() {
    return {
      copyExternalMarkdownContent:
        () =>
        ({ state }) => {
          const doc = state.doc;

          const oldContent = diffTransformUtils.extractContent(doc, DIFF_CHANGE_TYPE.REMOVED);

          // 使用 markdownSerializer 将内容序列化为 Markdown
          const markdown = markdownSerializer.externalCustomSerialize(oldContent);

          // 写入剪贴板
          writeToClipboard({
            'text/plain': markdown,
            'text/markdown': markdown,
          }).catch((error) => {
            console.error('Failed to copy markdown content:', error);
          });

          return true;
        },
      copyPlainTextContent:
        () =>
        ({ editor }) => {
          // 获取编辑器的纯文本内容
          const plainText = editor.getText();

          // 写入剪贴板
          writeToClipboard({
            'text/plain': plainText,
          }).catch((error) => {
            console.error('Failed to copy plain text content:', error);
          });

          return true;
        },
    };
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('clipboardAdapter'),
        props: {
          handleDOMEvents: {
            copy: (view, event) => {
              const { state } = view;
              const { selection } = state;

              // 获取选中的内容
              const { from, to } = selection;
              const slice = state.doc.slice(from, to);

              // 序列化为 HTML
              const tempDiv = document.createElement('div');
              const serializer = DOMSerializer.fromSchema(state.schema);
              const fragment = serializer.serializeFragment(slice.content);
              tempDiv.appendChild(fragment);

              // 在 HTML 前面添加标记
              const htmlWithMarker = YOU_MIND_HTML_MARKER + tempDiv.innerHTML;

              // 设置剪贴板数据
              event.clipboardData?.setData('text/html', htmlWithMarker);
              event.clipboardData?.setData('text/plain', view.state.doc.textBetween(from, to));

              event.preventDefault();
              return true;
            },
            cut: (view, event) => {
              const { state, dispatch } = view;
              const { selection } = state;

              // 获取选中的内容
              const { from, to } = selection;
              const slice = state.doc.slice(from, to);

              // 序列化为 HTML
              const tempDiv = document.createElement('div');
              const serializer = DOMSerializer.fromSchema(state.schema);
              const fragment = serializer.serializeFragment(slice.content);
              tempDiv.appendChild(fragment);

              // 在 HTML 前面添加标记
              const htmlWithMarker = YOU_MIND_HTML_MARKER + tempDiv.innerHTML;

              // 设置剪贴板数据
              event.clipboardData?.setData('text/html', htmlWithMarker);
              event.clipboardData?.setData('text/plain', view.state.doc.textBetween(from, to));

              // 删除选中的内容
              dispatch(state.tr.deleteSelection());

              event.preventDefault();
              return true;
            },
          },
          handlePaste: (view, event) => {
            const clipboardData = event.clipboardData;
            if (!clipboardData) return false;

            // 获取 HTML 内容
            const html = clipboardData.getData('text/html');

            // 如果 HTML 中包含我们的标记，让编辑器内部处理
            if (html?.includes(YOU_MIND_HTML_MARKER)) {
              return false;
            }

            // 获取纯文本内容
            const text = clipboardData.getData('text/plain');
            if (!text) return false;

            // 尝试将文本作为 markdown 解析
            try {
              const parsedHtml = markdownParse.parse(text);

              // 检查解析后的 HTML 是否包含有效内容
              // 创建临时 DOM 来检查内容
              const tempDiv = document.createElement('div');
              tempDiv.innerHTML = parsedHtml;

              // 如果解析后有段落、标题、列表等有效元素，说明是 markdown 内容
              const hasValidContent = tempDiv.querySelector(
                'h1, h2, h3, h4, h5, h6, ul, ol, blockquote, pre, table',
              );

              if (hasValidContent) {
                // 阻止默认粘贴行为
                event.preventDefault();

                // 将解析后的 HTML 插入到编辑器
                const { state, dispatch } = view;
                const parser = DOMParser.fromSchema(view.state.schema);
                const doc = parser.parse(tempDiv);
                const slice = doc.slice(0);

                const tr = state.tr.replaceSelection(slice);
                dispatch(tr);

                return true;
              }
            } catch (error) {
              // 解析失败，继续默认处理
              console.error('Markdown parse error:', error);
            }

            // 其他情况让编辑器默认处理
            return false;
          },
        },
      }),
    ];
  },
});
