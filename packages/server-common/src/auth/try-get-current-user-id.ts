import { CookieOptions, createServerClient } from '@supabase/ssr';
import { jwtVerify } from 'jose';

type Methods = {
  getHeader: (key: string) => string | null | undefined;
  getCookie: (key: string) => Promise<string | null | undefined> | string | null | undefined;
  setCookie: (key: string, value: string, options: CookieOptions) => Promise<void> | void;
  removeCookie?: (key: string, options: CookieOptions) => Promise<void> | void;
};

/**
 * 返回 userId 和 token 方便调用方缓存
 */
export async function tryGetCurrentUserIdAndToken(methods: Methods, autoRefresh: boolean = true) {
  const accessToken = await tryGetAccessToken(methods, autoRefresh);
  if (!accessToken) {
    return { userId: undefined, accessToken: undefined };
  }
  const userId = await tryParseUserIdFromAccessToken(accessToken);
  return { userId, accessToken };
}

async function tryGetAccessToken(methods: Methods, autoRefresh: boolean = true) {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get: methods.getCookie,
        set: methods.setCookie,
        remove: methods.removeCookie,
      },
      auth: {
        autoRefreshToken: autoRefresh,
        persistSession: autoRefresh,
        detectSessionInUrl: autoRefresh,
      },
    },
  );

  const { data } = await supabase.auth.getSession();
  const sessionAccessToken = data.session?.access_token;
  if (sessionAccessToken) {
    return sessionAccessToken;
  }

  const bearerToken = methods.getHeader('authorization');
  if (bearerToken) {
    return bearerToken.replace('Bearer ', '');
  }

  const cookieAccessToken = await methods.getCookie('YOUMIND_MOBILE_AUTH');
  if (cookieAccessToken) {
    return cookieAccessToken;
  }
}

async function tryParseUserIdFromAccessToken(accessToken: string) {
  try {
    const secret = new TextEncoder().encode(process.env.SUPABASE_JWT_SECRET);
    const { payload } = await jwtVerify(accessToken, secret);
    return payload.sub;
  } catch (error) {
    console.warn('Failed to parse user id from access token', error);
    return;
  }
}
