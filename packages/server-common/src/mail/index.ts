import { render } from '@react-email/components';
import { sendBatchEmails, sendEmail } from './plunk-api';

class EmailService {
  public async send(param: { email: string; subject: string; content: React.ReactNode }) {
    const { email, subject, content } = param;

    const html = await render(content);
    const { success, error, emails, message } = await sendEmail(email, subject, html);

    if (!success) {
      throw new Error(`Failed to send email to ${email}: ${error}(${message})`);
    }

    return { emails };
  }

  public async sendBatch(
    // 最多 100 封
    mails: string[],
    subject: string,
    content: React.ReactNode,
  ) {
    const html = await render(content);
    const { success, error, emails, message } = await sendBatchEmails(mails, subject, html);

    if (!success) {
      throw new Error(`Failed to send email to ${mails.join(', ')}: ${error}(${message})`);
    }

    return { emails };
  }
}

const emailService = new EmailService();
export { emailService };
