const AUTH_TOKEN = process.env.PLUNK_API_AUTH_TOKEN;
const API_BASE_URI = process.env.PLUNK_API_BASE_URI;

type PlunkBaseApiResponse = {
  success?: boolean;
  error?: string;
  code?: number;
  message?: string;
};

async function plunkApi(endpoint: string, method: 'POST' | 'PUT' | 'DELETE', body: object) {
  if (!AUTH_TOKEN) {
    console.error('Plunk API Auth token is not set, email service will not work');
    return;
  }

  try {
    const response = await fetch(`${API_BASE_URI}${endpoint}`, {
      method,
      headers: {
        Authorization: `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Plunk API error: ${response.status} ${response.statusText}`, errorText);
      throw new Error(`Plunk API error: ${response.status} ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Failed to call Plunk API', error);
    throw error;
  }
}

export async function sendEmail(
  to: string,
  subject: string,
  body: string,
): Promise<
  PlunkBaseApiResponse & {
    emails?: {
      contact: {
        id: string;
        email: string;
      };
    }[];
  }
> {
  return plunkApi('/send', 'POST', {
    to,
    subject,
    body,
  });
}

export async function sendBatchEmails(
  /** 最多 100 封，超过需要分批次处理 */
  emails: string[],
  subject: string,
  body: string,
): Promise<
  PlunkBaseApiResponse & {
    emails?: {
      contact: { id: string; email: string }[];
    }[];
  }
> {
  return plunkApi('/send', 'POST', {
    to: emails,
    subject,
    body,
  });
}
