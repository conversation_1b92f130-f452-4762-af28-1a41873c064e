name: Deploy YouAPI to AWS ECS

on:
  push:
    branches: [main, preview]
    paths:
      - "apps/youapi/**"
      - ".github/workflows/deploy-youapi.yml"
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'preview'
        type: choice
        options:
          - preview
          - prod

# 并发控制：取消正在进行的部署
concurrency:
  group: deploy-youapi-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  id-token: write # OIDC for AWS

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    # 设置超时防止挂起
    timeout-minutes: 20
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      DO_NOT_TRACK: ${{ secrets.DO_NOT_TRACK }}
      # 优化 Node.js 内存使用
      NODE_OPTIONS: "--max-old-space-size=4096"
    steps:
      - name: Determine Environment
        id: env
        run: |
          # 确定部署环境基于分支或手动输入
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            if [[ "${{ github.event.inputs.environment }}" == "prod" ]]; then
              ENV_NAME="production"
              DOPPLER_CONFIG="prod"
              ENV_SUFFIX="prod"
            else
              ENV_NAME="preview"
              DOPPLER_CONFIG="pre"
              ENV_SUFFIX="preview"
            fi
          else
            if [[ "${{ github.ref_name }}" == "main" ]]; then
              ENV_NAME="production"
              DOPPLER_CONFIG="prod"
              ENV_SUFFIX="prod"
            else
              ENV_NAME="preview"
              DOPPLER_CONFIG="pre"
              ENV_SUFFIX="preview"
            fi
          fi

          echo "env_name=$ENV_NAME" >> $GITHUB_OUTPUT
          echo "doppler_config=$DOPPLER_CONFIG" >> $GITHUB_OUTPUT
          echo "env_suffix=$ENV_SUFFIX" >> $GITHUB_OUTPUT

          echo "🌍 部署环境: $ENV_NAME"
          echo "📦 Doppler配置: $DOPPLER_CONFIG"
          echo "🏷️ 环境后缀: $ENV_SUFFIX"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          # 浅克隆减少下载时间
          fetch-depth: 1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"

      - name: Create .npmrc from Doppler secret
        run: echo "${{ secrets.NPM_RC_CONTENT }}" > .npmrc

      - name: Fetch App secrets from Doppler
        uses: dopplerhq/secrets-fetch-action@v1.3.0
        id: doppler
        with:
          doppler-token: ${{ secrets.DOPPLER_TOKEN }}
          doppler-project: youniverse-youapi
          doppler-config: ${{ steps.env.outputs.doppler_config }}
          inject-env-vars: true

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_DEPLOY_ROLE_ARN }}
          role-session-name: GitHubActionsDeploy
          aws-region: us-west-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build, tag, and push image to ECR
        uses: docker/build-push-action@v5
        with:
          context: .
          file: apps/youapi/Dockerfile
          build-args: |
            NODE_ENV=production
            YOUMIND_ENV=${{ steps.env.outputs.env_name }}
          tags: ${{ steps.login-ecr.outputs.registry }}/youapi:${{ github.sha }}
          push: true
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Prepare task definition with secrets
        id: task-def
        run: |
          ENV_SUFFIX=${{ steps.env.outputs.env_suffix }}

          # Start with base task definition
          cp apps/youapi/ecs/task-definition.json task-definition.json

          # Update log group based on environment
          jq --arg lg "/ecs/youapi-${ENV_SUFFIX}" \
             '.containerDefinitions[0].logConfiguration.options."awslogs-group" = $lg' \
             task-definition.json > tmp.json && mv tmp.json task-definition.json

          # Add environment variables from Doppler
          echo '${{ toJson(steps.doppler.outputs) }}' | \
            jq 'to_entries |
                map(select(.key != "DOPPLER_PROJECT" and .key != "DOPPLER_ENVIRONMENT" and .key != "DOPPLER_CONFIG")) |
                map({name: .key, value: .value})' > env-vars.json

          jq --slurpfile envs env-vars.json \
             '.containerDefinitions[0].environment += $envs[0]' \
             task-definition.json > tmp.json && mv tmp.json task-definition.json

          # Add YOUMIND_ENV based on branch or manual input
          jq --arg env "${{ steps.env.outputs.env_name }}" \
             '.containerDefinitions[0].environment += [{name: "YOUMIND_ENV", value: $env}]' \
             task-definition.json > tmp.json && mv tmp.json task-definition.json

          echo "definition-file=task-definition.json" >> $GITHUB_OUTPUT

      - name: Render task definition
        id: render_td
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.definition-file }}
          container-name: youapi
          image: ${{ steps.login-ecr.outputs.registry }}/youapi:${{ github.sha }}

      - name: Deploy Amazon ECS service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        timeout-minutes: 15
        with:
          task-definition: ${{ steps.render_td.outputs.task-definition }}
          service: youapi-${{ steps.env.outputs.env_suffix }}
          cluster: youapi
          wait-for-service-stability: true

      - name: Set Release Version from Tag
        run: echo "RELEASE_VERSION=${{ steps.env.outputs.env_name }}-${{ github.ref_name }}" >> $GITHUB_ENV

      - name: New Relic Application Deployment Marker
        uses: newrelic/deployment-marker-action@v2.3.0
        with:
          apiKey: ${{ secrets.NEW_RELIC_API_KEY }}
          guid: ${{ secrets.NEW_RELIC_DEPLOYMENT_ENTITY_GUID_YOUAPI }}
          version: "${{ env.RELEASE_VERSION }}"
          user: "${{ github.actor }}"
