'use client';

import { Button } from '@repo/ui/components/ui/button';
import { Calendar } from '@repo/ui/components/ui/calendar';
import { Input } from '@repo/ui/components/ui/input';
import { Label } from '@repo/ui/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@repo/ui/components/ui/popover';
import { cn } from '@repo/ui/lib/utils';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Clock } from 'lucide-react';
import * as React from 'react';

interface DateTimePickerProps {
  date?: Date;
  onDateChange?: (date: Date | undefined) => void;
  placeholder?: string;
  className?: string;
  inline?: boolean;
  defaultValue?: Date;
}

export function DateTimePicker({
  date,
  onDateChange,
  placeholder = 'Pick a date and time',
  className,
  inline = false,
  defaultValue,
}: DateTimePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(date || defaultValue);
  const [timeValue, setTimeValue] = React.useState<string>(
    date || defaultValue ? format(date || defaultValue!, 'HH:mm') : '12:00',
  );
  const [displayMonth, setDisplayMonth] = React.useState<Date>(date || defaultValue || new Date());

  // Update internal state when external date or defaultValue changes
  React.useEffect(() => {
    const currentValue = date || defaultValue;
    setSelectedDate(currentValue);
    if (currentValue) {
      setTimeValue(format(currentValue, 'HH:mm'));
      setDisplayMonth(currentValue);
    }
  }, [date, defaultValue]);

  const handleDateSelect = (newDate: Date | undefined) => {
    if (newDate) {
      const [hours, minutes] = timeValue.split(':').map(Number);
      const combinedDateTime = new Date(newDate);
      combinedDateTime.setHours(hours, minutes, 0, 0);

      setSelectedDate(combinedDateTime);
      onDateChange?.(combinedDateTime);
    } else {
      setSelectedDate(undefined);
      onDateChange?.(undefined);
    }
  };

  const handleTimeChange = (newTime: string) => {
    setTimeValue(newTime);

    if (selectedDate && newTime.match(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)) {
      const [hours, minutes] = newTime.split(':').map(Number);
      const combinedDateTime = new Date(selectedDate);
      combinedDateTime.setHours(hours, minutes, 0, 0);

      setSelectedDate(combinedDateTime);
      onDateChange?.(combinedDateTime);
    }
  };

  const handleApply = () => {
    if (selectedDate && timeValue.match(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)) {
      const [hours, minutes] = timeValue.split(':').map(Number);
      const finalDateTime = new Date(selectedDate);
      finalDateTime.setHours(hours, minutes, 0, 0);

      onDateChange?.(finalDateTime);
    }
    setIsOpen(false);
  };

  if (inline) {
    return (
      <div className={cn('flex flex-col space-y-4', className)}>
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={handleDateSelect}
          className="w-full"
          month={displayMonth}
          onMonthChange={setDisplayMonth}
        />
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4" />
            <Label htmlFor="time-input" className="text-sm">
              Time
            </Label>
          </div>
          <Input
            id="time-input"
            type="time"
            value={timeValue}
            onChange={(e) => handleTimeChange(e.target.value)}
            className="w-full"
          />
        </div>
      </div>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={'outline'}
          className={cn(
            'w-full justify-start text-left font-normal',
            !selectedDate && 'text-muted-foreground',
            className,
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selectedDate ? format(selectedDate, "PPP 'at' HH:mm") : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[350px] p-0" align="start">
        <div className="flex flex-col space-y-2 p-3">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
            initialFocus
            className="w-full"
            month={displayMonth}
            onMonthChange={setDisplayMonth}
          />
          <div className="border-t pt-3">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <Label htmlFor="time-input" className="text-sm">
                Time
              </Label>
            </div>
            <Input
              id="time-input"
              type="time"
              value={timeValue}
              onChange={(e) => handleTimeChange(e.target.value)}
              className="mt-1"
            />
          </div>
          <div className="flex justify-end space-x-2 pt-2">
            <Button variant="outline" size="sm" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button size="sm" onClick={handleApply}>
              Apply
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
