import { pick } from 'lodash';

export const extractDomain = (url: string) => {
  try {
    const hostname = new URL(url).hostname;
    const parts = hostname.split('.');
    if (parts.length > 2 && parts[0] === 'www') {
      parts.shift();
    }
    const domain = parts[parts.length - 2];
    return domain.length > 2
      ? domain.charAt(0).toUpperCase() + domain.slice(1)
      : domain.toUpperCase();
  } catch {
    return null;
  }
};

const MAX_SURROUNDING_LENGTH = 100; // Maximum characters to show before/after mark

function isCJKChar(char: string): boolean {
  return /[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/.test(
    char,
  );
}

export function truncateHighlightedText(text: string): string {
  // Split the text into segments: non-highlighted and highlighted
  const segments = text.split(/(<mark>.*?<\/mark>)/g);

  // Process each non-highlighted segment while keeping highlighted ones intact
  return segments
    .map((segment, i) => {
      // If this is a highlighted segment, return it as is
      if (segment.startsWith('<mark>')) {
        return segment;
      }

      // For non-highlighted segments
      if (segment.length <= MAX_SURROUNDING_LENGTH) {
        return segment;
      }

      // Determine if this is a leading or trailing segment
      const isLeading = i === 0;
      const isTrailing = i === segments.length - 1;

      // Function to find the best breakpoint
      const findBreakPoint = (text: string, fromStart: boolean) => {
        // Match western punctuation with spaces, spaces alone, or CJK punctuation
        // Include common CJK punctuation: 。，！？；：""''「」『』（）、
        const breakPoints = [
          ...text.matchAll(/(?:[.!?;,]\s+|\s+|[。，！？；：""''「」『』（）、])/g),
        ];

        // If no punctuation found, also consider breaks between CJK characters
        if (breakPoints.length === 0) {
          const positions: Array<RegExpExecArray> = [];
          for (let i = 0; i < text.length - 1; i++) {
            if (isCJKChar(text[i]) && isCJKChar(text[i + 1])) {
              // Create a RegExpExecArray-like object
              const match = [text[i + 1]] as RegExpExecArray;
              match.index = i + 1;
              match.input = text;
              positions.push(match);
            }
          }
          breakPoints.push(...positions);
        }

        if (breakPoints.length === 0) {
          return fromStart ? MAX_SURROUNDING_LENGTH : text.length - MAX_SURROUNDING_LENGTH;
        }

        // Adjust character count for CJK (count as 2 for width consideration)
        const getAdjustedLength = (str: string) => {
          return Array.from(str).reduce((len, char) => len + (isCJKChar(char) ? 2 : 1), 0);
        };

        if (fromStart) {
          // Find the last break point within adjusted MAX_SURROUNDING_LENGTH from start
          for (let i = breakPoints.length - 1; i >= 0; i--) {
            const subStr = text.slice(0, breakPoints[i].index!);
            if (getAdjustedLength(subStr) <= MAX_SURROUNDING_LENGTH) {
              return breakPoints[i].index! + (breakPoints[i][0]?.length ?? 1);
            }
          }
        } else {
          // Find the first break point within adjusted MAX_SURROUNDING_LENGTH from end
          const totalLen = getAdjustedLength(text);
          const targetPos = totalLen - MAX_SURROUNDING_LENGTH;
          for (const match of breakPoints) {
            const subStr = text.slice(0, match.index!);
            if (getAdjustedLength(subStr) >= targetPos) {
              return match.index!;
            }
          }
        }

        return fromStart ? MAX_SURROUNDING_LENGTH : text.length - MAX_SURROUNDING_LENGTH;
      };

      if (isLeading) {
        const breakPoint = findBreakPoint(segment, false);
        return `...${segment.slice(breakPoint)}`;
      } else if (isTrailing) {
        const breakPoint = findBreakPoint(segment, true);
        return `${segment.slice(0, breakPoint)}...`;
      } else {
        const startBreak = findBreakPoint(segment, true);
        const endBreak = findBreakPoint(segment, false);
        return `${segment.slice(0, startBreak)}...${segment.slice(endBreak)}`;
      }
    })
    .join('');
}

//
const LANGUAGE_LOCALE_MAP = {
  cmn: 'zh',
  eng: 'en',
  jpn: 'ja',
  kor: 'ko',
  tha: 'th',
  ell: 'el',
  rus: 'ru',
  srp: 'sr',
  ukr: 'uk',
  bel: 'be',
} as const;
const SEARCH_LANGUAGE_LOCALE_MAP = pick(LANGUAGE_LOCALE_MAP, ['cmn', 'eng']);
function removeLocalePrefix(fieldName: string): string {
  const localesToRemove = Object.values(SEARCH_LANGUAGE_LOCALE_MAP);
  return fieldName.replace(new RegExp(`^(${localesToRemove.join('|')})_`), '');
}

export function restoreToOriginalField(fieldName: string) {
  return removeLocalePrefix(fieldName).replace(/_\d+$/, '');
}
