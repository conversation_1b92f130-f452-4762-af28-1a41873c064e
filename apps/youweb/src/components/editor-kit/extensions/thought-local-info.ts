import type { Editor } from '@tiptap/core';
import { debounce } from 'lodash';

interface ThoughtPosition {
  cursor: number | null;
  scroll: {
    top: number;
  };
  lastUpdated: number;
}

interface ThoughtPositionsStorage {
  positions: Record<string, ThoughtPosition>;
}

interface ThoughtLocalInfoOptions {
  id: string;
  editor: Editor;
}

const STORAGE_KEY = '$_youmind-thought-local-info-positions';
const MAX_STORED_THOUGHTS = 50;

export class ThoughtLocalInfo {
  private id: string;
  private editor: Editor;
  private scrollContainer: HTMLElement | null = null;
  private debouncedSavePosition: ReturnType<typeof debounce>;
  private debouncedSaveScroll: ReturnType<typeof debounce>;

  constructor(options: ThoughtLocalInfoOptions) {
    this.id = options.id;
    this.editor = options.editor;

    this.debouncedSavePosition = debounce(this.savePosition.bind(this), 1000);
    this.debouncedSaveScroll = debounce(this.saveScroll.bind(this), 800);
  }

  private findScrollContainer(element: HTMLElement): HTMLElement | null {
    let parent = element.parentElement;

    while (parent) {
      const { overflowY } = window.getComputedStyle(parent);
      if (
        ['auto', 'scroll', 'overlay'].includes(overflowY) &&
        parent.scrollHeight > parent.clientHeight
      ) {
        return parent;
      }
      parent = parent.parentElement;
    }

    return document.documentElement.scrollHeight > window.innerHeight
      ? document.documentElement
      : null;
  }

  setupEventListeners(): void {
    this.scrollContainer = this.findScrollContainer(this.editor.view.dom);

    if (this.scrollContainer) {
      this.scrollContainer.addEventListener('scroll', this.debouncedSaveScroll);
    }

    this.editor.on('selectionUpdate', this.debouncedSavePosition);

    this.editor.on('update', this.debouncedSavePosition);
  }

  private getStoredPositions(): ThoughtPositionsStorage {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : { positions: {} };
    } catch (error) {
      console.error('Failed to read thought positions from localStorage:', error);
      return { positions: {} };
    }
  }

  private savePositions(positions: ThoughtPositionsStorage): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(positions));
    } catch (error) {
      console.error('Failed to save thought positions to localStorage:', error);
    }
  }

  private savePosition(): void {
    if (!this.id) return;

    const { from } = this.editor.state.selection;
    const storage = this.getStoredPositions();

    const scrollTop = this.scrollContainer?.scrollTop || 0;

    const position = storage.positions[this.id] || {
      cursor: null,
      scroll: { top: 0 },
      lastUpdated: 0,
    };

    position.cursor = from;
    position.scroll.top = scrollTop;
    position.lastUpdated = Date.now();

    storage.positions[this.id] = position;

    this.cleanupOldPositions(storage);

    this.savePositions(storage);
  }

  private saveScroll(): void {
    if (!this.id || !this.scrollContainer) return;

    const storage = this.getStoredPositions();

    const position = storage.positions[this.id] || {
      cursor: null,
      scroll: { top: 0 },
      lastUpdated: 0,
    };

    position.scroll.top = this.scrollContainer.scrollTop;
    position.lastUpdated = Date.now();

    storage.positions[this.id] = position;

    this.cleanupOldPositions(storage);

    this.savePositions(storage);
  }

  private cleanupOldPositions(storage: ThoughtPositionsStorage): void {
    const positions = storage.positions;
    const ids = Object.keys(positions);

    if (ids.length <= MAX_STORED_THOUGHTS) return;

    const sortedIds = ids.sort((a, b) => positions[a].lastUpdated - positions[b].lastUpdated);

    const idsToRemove = sortedIds.slice(0, ids.length - MAX_STORED_THOUGHTS);
    idsToRemove.forEach((id) => {
      delete positions[id];
    });
  }

  tryRecoverPosition(): void {
    if (!this.id) return;

    const storage = this.getStoredPositions();
    const position = storage.positions[this.id];

    if (!this.scrollContainer) {
      this.scrollContainer = this.findScrollContainer(this.editor.view.dom);
    }

    if (!position) {
      // 如果编辑器为空，此时需要聚焦在第一个光标位置, 创建的时候比较耗时, 需要延迟一点
      if (this.editor.isEmpty) {
        setTimeout(() => {
          this.editor.commands.focus('start');
        }, 400);
      }
      return;
    }

    if (typeof position.cursor === 'number') {
      setTimeout(() => {
        this.editor.commands.focus(position.cursor, { scrollIntoView: false });

        if (this.scrollContainer && position.scroll) {
          this.scrollContainer?.scrollTo({
            top: position.scroll.top,
            behavior: 'smooth',
          });
        }
      }, 150);
    }
  }

  public destroy(): void {
    if (this.scrollContainer) {
      this.scrollContainer.removeEventListener('scroll', this.debouncedSaveScroll);
    }

    this.editor.off('selectionUpdate', this.debouncedSavePosition);
    this.editor.off('update', this.debouncedSavePosition);

    this.debouncedSavePosition.cancel();
    this.debouncedSaveScroll.cancel();
  }
}
