import { debounce } from 'lodash';
import type { RefObject } from 'react';
import { PatchThought } from '@/typings/thought';
import { getNativeMessageControllerFromEditor } from '../../editor-kit/extensions/native-message/utils';
import type { ThoughtBodyComponentRef } from '../type';
import { BaseThoughtWorkflow, type BaseThoughtWorkflowOptions } from './base-thought-workflow';

export interface MobileThoughtWorkflowOptions
  extends Omit<BaseThoughtWorkflowOptions, 'componentRef' | 'indexedDB'> {
  componentRef: RefObject<ThoughtBodyComponentRef>;
}

// 移动端版本保存间隔：1.5分钟
const MOBILE_VERSION_SAVE_INTERVAL_MS = 1.5 * 60 * 1000;

export class MobileThoughtWorkflow extends BaseThoughtWorkflow {
  constructor(options: MobileThoughtWorkflowOptions) {
    super({
      id: options.id,
      editor: options.editor,
      ydoc: options.ydoc,
      componentRef: options.componentRef,
      // 移动端不使用 indexedDB
      indexedDB: null,
      // 移动端使用2分钟的版本保存间隔
      versionSaveInterval: options.versionSaveInterval || MOBILE_VERSION_SAVE_INTERVAL_MS,
    });
  }

  protected performUpdate(data: PatchThought) {
    // 移动端特有的更新逻辑
    this.thoughtComponent?.onUpdate?.(data);
    this.nativeMessageController?.updateEditorData(data);
  }

  get nativeMessageController() {
    return getNativeMessageControllerFromEditor(this.editor);
  }

  uppdateTitleToExternal = debounce(() => {
    const patchData = this.nativeMessageController?.getEditorPatchData();
    if (patchData) {
      this.nativeMessageController?.updateEditorData(patchData);
    }
  }, 200);

  editTitle(title: string): void {
    super.editTitle(title);
    this.uppdateTitleToExternal();
  }
}

export type IMobileThoughtWorkflow = MobileThoughtWorkflow;
