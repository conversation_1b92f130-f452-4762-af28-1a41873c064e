'use client';

import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { useAsyncEffect } from 'ahooks';
import { useAtom, useSetAtom } from 'jotai';
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { AiAskChatBox } from '@/components/ai-ask';
import { createEmptyAssistantChatAtom, switchChatAtom } from '@/hooks/ask-ai/useChatInitialization';
import { isMobileChatAtom } from '@/hooks/ask-ai/useChatPanelState';
import { exportMethodsToIOSAtom, initIOSMethodsAtom } from '@/hooks/ask-ai/useMobileChat';
import useRequestWithAbortController from '@/hooks/useRequestWithAbort';

export default function ChatPage() {
  // 启动 chat 之前全局 loading 态
  const [loading, setLoading] = useState(true);
  const switchChat = useSetAtom(switchChatAtom);
  const [, setIsMobileChat] = useAtom(isMobileChatAtom);
  const createEmptyChat = useSetAtom(createEmptyAssistantChatAtom);
  const initIOSMethods = useSetAtom(initIOSMethodsAtom);
  const exportMethodsToIOS = useSetAtom(exportMethodsToIOSAtom);

  const { slug } = useParams<{ slug: string }>();

  const {
    runAsync: runLoadChat,
    abortController,
    loading: fetchRunning,
  } = useRequestWithAbortController(async (_abortSignal, chatId: string) => switchChat(chatId), {
    refreshDeps: [],
    stopWhenUnmount: true,
    manual: true,
  });

  useAsyncEffect(async () => {
    document.title = 'YouMind Chat';
    // 设置移动端聊天模式，隐藏输入框
    setIsMobileChat(true);
    setLoading(true);

    if (slug === 'new-chat') {
      createEmptyChat();
    } else {
      await runLoadChat(slug!);
    }
    setLoading(false);

    // 初始化从 iOS 获取的方法
    await initIOSMethods();

    // 导出方法给 iOS
    exportMethodsToIOS();
  }, [setIsMobileChat, initIOSMethods, exportMethodsToIOS]);

  // 从 slug 中读取 chat id 并执行 switch chat

  if (fetchRunning || loading) {
    return <SimpleLoading />;
  }

  return (
    <div className="box-border h-full w-full px-4">
      <AiAskChatBox className="px-0" chatAtOptions={[]} />
    </div>
  );
}
