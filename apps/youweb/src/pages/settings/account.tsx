import { zodResolver } from '@hookform/resolvers/zod';
import {
  BillingInterval,
  CreditAccountDto,
  SubscriptionDto,
  SubscriptionProductTier,
} from '@repo/api/generated-client/snake-case/index';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Button } from '@repo/ui/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/ui/components/ui/dialog';
import { Form, FormControl, FormField, FormItem } from '@repo/ui/components/ui/form';
import { Input } from '@repo/ui/components/ui/input';
import { Label } from '@repo/ui/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/ui/select';
import { useAsyncEffect } from 'ahooks';
import { format } from 'date-fns';
import { useAtom } from 'jotai';
import { Calendar, Copy, Edit, ExternalLink } from 'lucide-react';
import React, { useState } from 'react';
import { type SubmitHandler, useForm } from 'react-hook-form';
import { z } from 'zod';
import { CreditsUsage } from '@/components/credits-usage';
import { DevOnly } from '@/components/DevOnly';
import { FileInput, FileUploader } from '@/components/file-upload';
import { DateTimePicker } from '@/components/ui/custom/date-time-picker';
import { spaceAtom } from '@/hooks/useSpace';
import { useTranslation } from '@/hooks/useTranslation';
import { uploadFile } from '@/hooks/useUploadFiles';
import { userAtom } from '@/hooks/useUser';
import { User } from '@/typings/user';
import { apiClient, callAPI } from '@/utils/callHTTP';
import { convertAvator } from '@/utils/convertAvator';
import { cn, sha256File } from '@/utils/utils';

const DEFAULT_AVATAR = 'https://cdn.gooo.ai/assets/default-user-picture.png';

export interface AccountProps extends React.HTMLAttributes<HTMLDivElement> {}

const FormSchema = z.object({
  name: z.string(),
});
type FormType = z.infer<typeof FormSchema>;

const MAX_FILES = 1;
const MAX_FILE_SIZE = 1024 * 1024 * 10;
const dropZoneConfig = {
  maxFiles: MAX_FILES,
  maxSize: MAX_FILE_SIZE,
  multiple: false,
  accept: {
    'image/*': ['.png', '.gif', '.jpeg', '.jpg'],
  },
};

export default function Account({ className }: AccountProps) {
  const { t } = useTranslation('Settings');
  const [user, setUser] = useAtom(userAtom);
  const [space] = useAtom(spaceAtom);

  const [files, setFiles] = useState<File[] | null>(null);
  const [open, setOpen] = useState(false);
  const [loadingActions, setLoadingActions] = useState<Record<string, boolean>>({});

  // Dialog states
  const [timeZoneDialogOpen, setTimeZoneDialogOpen] = useState(false);
  const [testClockDialogOpen, setTestClockDialogOpen] = useState(false);
  const [consumeCreditsDialogOpen, setConsumeCreditsDialogOpen] = useState(false);

  // Form values
  const [timeZoneValue, setTimeZoneValue] = useState('');
  const [testClockDateTime, setTestClockDateTime] = useState<Date>();
  const [consumeAmount, setConsumeAmount] = useState('');

  const picture = user?.picture || DEFAULT_AVATAR;

  const userName = user?.name || user?.email?.split('@')[0] || '';
  let avatar: string;
  if (picture.startsWith('files/')) {
    avatar = `/${picture}`;
  } else {
    avatar = convertAvator(picture);
  }

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: userName,
    },
  });

  const [creditAccount, setCreditAccount] = useState<CreditAccountDto>();
  const [subscription, setSubscription] = useState<SubscriptionDto>();

  useAsyncEffect(async () => {
    const { data } = await callAPI(apiClient.creditApi.getCreditAccount());
    setCreditAccount(data);
  }, [user]);

  useAsyncEffect(async () => {
    const { data } = await callAPI(apiClient.subscriptionApi.findSubscription());
    if (data) {
      setSubscription(data);
    }
  }, [user]);

  // Initialize testClockDateTime when dialog opens
  React.useEffect(() => {
    if (testClockDialogOpen && !testClockDateTime) {
      const defaultDateTime = creditAccount?.test_clock
        ? // @ts-ignore
          new Date(creditAccount.test_clock.frozen_time * 1000)
        : new Date();
      setTestClockDateTime(defaultDateTime);
    }
  }, [testClockDialogOpen, testClockDateTime, creditAccount?.test_clock]);

  const handleCreateSubscription = async (
    product_tier: SubscriptionProductTier,
    billing_interval: BillingInterval,
  ) => {
    const { data } = await callAPI(
      apiClient.subscriptionApi.createSubscription({
        product_tier,
        billing_interval,
      }),
    );
    if (data) {
      if (data.redirect) {
        window.location.href = data.redirect;
      }
      if (data.subscription) {
        setSubscription(data.subscription);
      }
      if (data.credit_account) {
        setCreditAccount(data.credit_account);
      }
    }
  };

  const handleCancelSubscription = async () => {
    const { data } = await callAPI(apiClient.subscriptionApi.cancelSubscription({}));
    if (data) {
      setSubscription(data);
    }
  };

  const handleCreateBillingPortalSession = async () => {
    const { data } = await callAPI(apiClient.subscriptionApi.createBillingPortalSession());
    if (data) {
      window.location.href = data.redirect;
    }
  };

  const handleUpdateSubscription = async (
    product_tier: SubscriptionProductTier,
    billing_interval: BillingInterval,
  ) => {
    const { data } = await callAPI(
      apiClient.subscriptionApi.updateSubscription({
        product_tier,
        billing_interval,
      }),
    );
    if (data) {
      if (data.subscription) {
        setSubscription(data.subscription);
      }
      if (data.credit_account) {
        setCreditAccount(data.credit_account);
      }
      if (data.redirect) {
        window.location.href = data.redirect;
      }
    }
  };

  const handleDeleteSubscriptionForTest = async () => {
    await callAPI(apiClient.subscriptionApi.deleteSubscriptionForTest());
    // 删除后清空订阅状态
    setSubscription(undefined);
  };

  // 辅助函数：获取订阅的计划
  const getCurrentPlan = (subscription: SubscriptionDto) => {
    if (subscription.cancel_at_period_end) {
      return {};
    }

    if (subscription.renew_change) {
      return {
        product_tier: subscription.renew_change.product_tier,
        billing_interval: subscription.renew_change.billing_interval,
      };
    }
    return {
      product_tier: subscription.product_tier,
      billing_interval: subscription.billing_interval,
    };
  };

  // 辅助函数：判断按钮是否为当前计划
  const isCurrentPlan = (
    subscription: SubscriptionDto,
    targetTier: SubscriptionProductTier,
    targetInterval: BillingInterval,
  ) => {
    const currentPlan = getCurrentPlan(subscription);
    return (
      currentPlan.product_tier === targetTier && currentPlan.billing_interval === targetInterval
    );
  };

  // 辅助函数：获取按钮文案
  const getButtonText = (
    subscription: SubscriptionDto,
    targetTier: SubscriptionProductTier,
    targetInterval: BillingInterval,
  ) => {
    const tierName = targetTier === 'pro' ? 'Pro' : 'Max';
    const intervalName = targetInterval === 'monthly' ? 'monthly' : 'yearly';

    // 如果不是当前计划，显示 "Change to"
    if (!isCurrentPlan(subscription, targetTier, targetInterval)) {
      return `Change to ${tierName} ${intervalName}`;
    }

    // 如果是当前计划，显示计划名称
    let text = `${tierName} ${intervalName}`;

    // 如果是 renew_change 计划，加上尾巴
    if (subscription.renew_change) {
      text += ' ✓ (at period end)';
    }

    return text;
  };

  // 辅助函数：根据用户时区过滤可用的订阅选项
  const getAvailablePlans = () => {
    const isAsiaShanghai = user?.time_zone === 'Asia/Shanghai';

    if (isAsiaShanghai) {
      // 中国用户只能选择年付计划
      return [
        { tier: SubscriptionProductTier.pro, interval: BillingInterval.yearly },
        { tier: SubscriptionProductTier.max, interval: BillingInterval.yearly },
      ];
    }

    // 其他地区用户可以选择所有计划
    return [
      { tier: SubscriptionProductTier.pro, interval: BillingInterval.monthly },
      { tier: SubscriptionProductTier.pro, interval: BillingInterval.yearly },
      { tier: SubscriptionProductTier.max, interval: BillingInterval.monthly },
      { tier: SubscriptionProductTier.max, interval: BillingInterval.yearly },
    ];
  };

  const handleUpdateTimeZone = async (timeZone: string) => {
    const { data } = await callAPI(
      apiClient.userApi.updateTimeZone({
        time_zone: timeZone,
      }),
    );

    // 更新用户状态
    if (data) {
      setUser({
        ...user,
        ...data,
      } as User);
    }

    setTimeZoneDialogOpen(false);
  };

  const handleAdvanceTestClock = async (fozenTime: Date) => {
    const { data } = await callAPI(
      apiClient.creditApi.advanceTestClock({
        fozen_time: fozenTime,
      }),
    );
    if (data) {
      setCreditAccount(data);
    }

    setTestClockDialogOpen(false);
  };

  const handleConsumeCredits = async (amount: number) => {
    await callAPI(
      apiClient.creditApi.consumeCredits({
        amount,
      }),
    );
    setConsumeCreditsDialogOpen(false);
    // 刷新 CreditAccount
    const { data: creditData } = await callAPI(apiClient.creditApi.getCreditAccount());
    if (creditData) {
      setCreditAccount(creditData);
    }
  };

  const handleAsyncAction = async (actionKey: string, action: () => Promise<void>) => {
    setLoadingActions((prev) => ({ ...prev, [actionKey]: true }));
    try {
      await action();
    } finally {
      setLoadingActions((prev) => ({ ...prev, [actionKey]: false }));
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // TODO: 添加成功提示 toast
      console.log(`${label} ID copied to clipboard: ${text}`);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const pending = form.formState.isSubmitting;

  const onSubmit: SubmitHandler<FormType> = async (userData) => {
    const { data, error } = await callAPI(
      apiClient.userApi.updateUserName({
        name: userData.name,
      }),
    );

    if (error) {
      return;
    }

    if (data) {
      setUser({
        ...user,
        ...data,
      } as User);
    }
    setOpen(false);
  };

  const handleFileChange = async (files: File[] | null) => {
    if (files?.length) {
      setFiles(files);
      const file = files[0];
      const hash = await sha256File(file);
      try {
        await uploadFile({ hash, file, isPublic: true });
      } catch {
        setFiles([]);
        return;
      }

      const { data, error } = await callAPI(
        apiClient.userApi.updateUserAvatar({
          avatar_url: `https://cdn.gooo.ai/web-images/${hash}`,
        }),
      );

      if (error) {
        setFiles([]);
        return;
      }

      if (data) {
        setUser({
          ...user,
          ...data,
        } as User);
      }
    }
  };

  // const handleSubscribe = () => {};

  return (
    <div className={cn('rounded-2xl bg-background p-2', className)}>
      <div className="flex justify-between">
        <div className="px-4 pb-4 pt-2">
          <p className="title mb-4">{t('Account.title')}</p>
          <div className="flex gap-4">
            <FileUploader
              value={files}
              onValueChange={handleFileChange}
              dropzoneOptions={dropZoneConfig}
              className="w-auto"
            >
              <FileInput>
                <Avatar className="h-[96px] w-[96px] object-cover">
                  <AvatarImage src={avatar} alt="user avatar" className="object-cover" />
                  <AvatarFallback></AvatarFallback>
                </Avatar>
              </FileInput>
            </FileUploader>
            <div className="flex-1">
              <p className="title-large mb-5 flex items-center">
                {userName}
                <Dialog open={open} onOpenChange={setOpen}>
                  <DialogTrigger asChild>
                    <Button variant="icon" size="sm" className="ml-2">
                      <Edit />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="w-[400px]">
                    <DialogHeader>
                      <DialogTitle>{t('Account.changeName')}</DialogTitle>
                    </DialogHeader>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)}>
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input className="h-8 rounded-md" placeholder="" {...field} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <DialogFooter>
                          <Button
                            type="submit"
                            className="mt-4 h-8 rounded-full bg-foreground text-background hover:bg-foreground hover:text-background"
                            disabled={pending}
                          >
                            {t('Account.apply')}
                          </Button>
                        </DialogFooter>
                      </form>
                    </Form>
                  </DialogContent>
                </Dialog>
              </p>
              <p className="text-xs font-normal text-caption-fg">Email: {user?.email}</p>
              <p className="mt-2 text-xs font-normal text-caption-fg">UID: {user?.id}</p>
            </div>
          </div>
        </div>
        {space && (
          <div
            className="h-[176px] w-[280px] rounded-2xl"
            style={{
              // tailwind 不支持特殊角度，这里直接用 style 了
              background:
                'linear-gradient(119.34deg, #5F89E7 9.11%, #ACA0F8 36.53%, #F08A75 63.38%, #C269F2 92.62%)',
            }}
          >
            <div className="m-0.5 flex h-[calc(100%-4px)] w-[calc(100%-4px)] flex-col rounded-[14px] bg-white p-5">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <span className="bg-gradient-to-r from-[rgba(242,135,54,0.88)] via-[rgba(193,86,240,0.88)] via-[rgba(236,100,216,0.88)] via-[rgba(245,92,137,0.88)] to-[rgba(101,81,246,0.88)] bg-clip-text font-['Avenir'] text-[18px] font-extrabold leading-[26px] text-transparent">
                    YouMind Pro
                  </span>
                </div>
                {/* {space.status === SpaceStatusEnum.TRIALING ||
                space.status === SpaceStatusEnum.TRIAL_EXPIRING ||
                space.subscription?.status === SubscriptionStatusEnum.TRIALING ? (
                  <div className="flex h-[20px] w-[59px] items-center justify-center gap-[10px] rounded-[6px] border border-solid border-[rgba(0,122,255,0.24)] bg-[rgba(0,122,255,0.08)] p-[3px_4px] text-[10px] text-[#007AFF]">
                    Free trial
                  </div>
                ) : null} */}
              </div>
              {/* <div className="text-xs text-caption-fg">
                {space.subscription_price === PriceLookupKeyEnum.YOUMIND_PRO_MONTHLY
                  ? '$20 / month'
                  : 'CNY 200 / year'}
              </div> */}
              {/* {space.status === SpaceStatusEnum.TRIALING ||
              space.status === SpaceStatusEnum.TRIAL_EXPIRING ||
              space.subscription?.status === SubscriptionStatusEnum.TRIALING ? (
                <div className="mt-3 text-xs text-secondary-fg">
                  You have {getTrialDaysLeft(space.trial_expires_at!)} days remaining in free trial.
                </div>
              ) : null}
              {space.status === SpaceStatusEnum.SUBSCRIBED &&
              !space.subscription?.cancel_at_period_end ? (
                <div className="mt-2 text-xs text-secondary-fg">
                  Next billing date {format(space.subscription!.next_billing_date, 'PPP')}
                </div>
              ) : null}
              {space.status === SpaceStatusEnum.SUBSCRIBED ? (
                space.subscription?.source === SubscriptionSourceEnum.STRIPE ? (
                  <a href="/billing" target="_blank" className="mt-auto" rel="noopener">
                    <Button variant="outline" className="h-8 w-[85px] rounded-[40px] font-normal">
                      Manage
                    </Button>
                  </a>
                ) : (
                  <div className="mt-auto text-xs text-secondary-fg">
                    Manage subscription on your iOS device
                  </div>
                )
              ) : (
                <div className="mt-auto">
                  <Button
                    className="h-8 w-[130px] rounded-[40px] font-normal"
                    onClick={handleSubscribe}
                  >
                    Subscribe now
                  </Button>
                </div>
              )} */}
            </div>
          </div>
        )}
      </div>
      <DevOnly>
        {creditAccount && (
          <CreditsUsage
            credits={creditAccount.monthly_balance}
            usage={creditAccount.monthly_quota}
          />
        )}
        <div className="mt-4 space-y-3">
          {/* User Card */}
          <div className="rounded-lg border p-4 bg-card">
            <h3 className="text-lg font-semibold mb-3">User</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 flex-1">
                  <p className="text-sm text-muted-foreground">Time zone:</p>
                  <p className="font-medium text-sm">{user?.time_zone || 'Not set'}</p>
                </div>
                <Dialog open={timeZoneDialogOpen} onOpenChange={setTimeZoneDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // 初始化当前时区值
                        const currentTimeZone =
                          user?.time_zone || Intl.DateTimeFormat().resolvedOptions().timeZone;
                        setTimeZoneValue(currentTimeZone);
                        setTimeZoneDialogOpen(true);
                      }}
                    >
                      Change
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="w-[400px]">
                    <DialogHeader>
                      <DialogTitle>Update time zone</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label>Time zone</Label>
                        <Select value={timeZoneValue} onValueChange={setTimeZoneValue}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a time zone" />
                          </SelectTrigger>
                          <SelectContent>
                            {/* 如果当前时区不在预定义列表中，先显示当前时区 */}
                            {timeZoneValue &&
                              ![
                                'UTC',
                                'Asia/Shanghai',
                                'Asia/Tokyo',
                                'Asia/Kolkata',
                                'Europe/London',
                                'Europe/Paris',
                                'America/New_York',
                                'America/Chicago',
                                'America/Denver',
                                'America/Los_Angeles',
                                'Australia/Sydney',
                              ].includes(timeZoneValue) && (
                                <>
                                  <SelectItem value={timeZoneValue}>
                                    {timeZoneValue} (Current)
                                  </SelectItem>
                                  <div className="h-px bg-muted mx-1 my-1" />
                                </>
                              )}
                            <SelectItem value="UTC">UTC (UTC+0)</SelectItem>
                            <SelectItem value="Asia/Shanghai">Asia/Shanghai (UTC+8)</SelectItem>
                            <SelectItem value="Asia/Tokyo">Asia/Tokyo (UTC+9)</SelectItem>
                            <SelectItem value="Asia/Kolkata">Asia/Kolkata (UTC+5:30)</SelectItem>
                            <SelectItem value="Europe/London">Europe/London (UTC+0/+1)</SelectItem>
                            <SelectItem value="Europe/Paris">Europe/Paris (UTC+1/+2)</SelectItem>
                            <SelectItem value="America/New_York">
                              America/New_York (UTC-5/-4)
                            </SelectItem>
                            <SelectItem value="America/Chicago">
                              America/Chicago (UTC-6/-5)
                            </SelectItem>
                            <SelectItem value="America/Denver">
                              America/Denver (UTC-7/-6)
                            </SelectItem>
                            <SelectItem value="America/Los_Angeles">
                              America/Los_Angeles (UTC-8/-7)
                            </SelectItem>
                            <SelectItem value="Australia/Sydney">
                              Australia/Sydney (UTC+10/+11)
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setTimeZoneDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button
                        loading={loadingActions['set-timezone']}
                        onClick={() =>
                          handleAsyncAction('set-timezone', () =>
                            handleUpdateTimeZone(timeZoneValue),
                          )
                        }
                        disabled={!timeZoneValue.trim()}
                      >
                        Update
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>

              {creditAccount?.test_clock && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1">
                    <p className="text-sm text-muted-foreground">Test clock:</p>
                    <p className="font-medium text-orange-600 text-sm">
                      {format(
                        // @ts-ignore
                        new Date(creditAccount.test_clock.frozen_time * 1000),
                        'yyyy-MM-dd HH:mm:ss',
                      )}
                    </p>
                    <div
                      className={`text-xs px-1.5 py-0.5 rounded ${
                        // @ts-ignore
                        creditAccount.test_clock.status === 'advancing'
                          ? 'bg-blue-100 text-blue-700'
                          : 'bg-green-100 text-green-700'
                      }`}
                    >
                      {/* @ts-ignore */}
                      {creditAccount.test_clock.status}
                    </div>
                  </div>
                  <Dialog open={testClockDialogOpen} onOpenChange={setTestClockDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        Advance
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="w-[400px]">
                      <DialogHeader>
                        <DialogTitle>Advance test clock</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <div className="flex items-center space-x-2 mb-2">
                            <Calendar className="h-4 w-4" />
                            <Label>Date</Label>
                          </div>
                          <DateTimePicker
                            date={testClockDateTime}
                            onDateChange={setTestClockDateTime}
                            placeholder="Select date and time"
                            className="mt-1"
                            inline
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setTestClockDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button
                          loading={loadingActions['advance-clock']}
                          onClick={() =>
                            handleAsyncAction('advance-clock', () => {
                              if (!testClockDateTime) return Promise.resolve();
                              return handleAdvanceTestClock(testClockDateTime);
                            })
                          }
                          disabled={!testClockDateTime}
                        >
                          Advance
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              )}
            </div>
          </div>

          {/* Credit Account Card */}
          {creditAccount && (
            <div className="rounded-lg border p-4 bg-card">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold">Credit account</h3>
                <div className="flex items-center gap-1">
                  <Dialog
                    open={consumeCreditsDialogOpen}
                    onOpenChange={setConsumeCreditsDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        Consume
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="w-[400px]">
                      <DialogHeader>
                        <DialogTitle>Consume credits</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label>Amount</Label>
                          <Input
                            type="number"
                            value={consumeAmount}
                            onChange={(e) => setConsumeAmount(e.target.value)}
                            placeholder="Enter amount to consume"
                            min="1"
                            max={creditAccount?.monthly_balance || 0}
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            Available balance: {creditAccount?.monthly_balance || 0}
                          </p>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setConsumeCreditsDialogOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          loading={loadingActions['consume-credits']}
                          onClick={() =>
                            handleAsyncAction('consume-credits', () =>
                              handleConsumeCredits(parseInt(consumeAmount)),
                            )
                          }
                          // disabled={
                          //   !consumeAmount ||
                          //   parseInt(consumeAmount) <= 0 ||
                          //   parseInt(consumeAmount) > (creditAccount?.monthly_balance || 0)
                          // }
                        >
                          Consume
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                  <Button
                    variant="icon"
                    size="sm"
                    onClick={() => copyToClipboard(creditAccount.id, 'Credit Account')}
                    className="opacity-60 hover:opacity-100"
                    title="Copy ID"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-muted-foreground">Product tier:</p>
                    <p className="font-medium text-sm">{creditAccount.product_tier}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-muted-foreground">Monthly credits:</p>
                    <p className="text-sm font-medium">
                      {creditAccount.monthly_balance} / {creditAccount.monthly_quota}
                    </p>
                  </div>
                </div>

                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min((creditAccount.monthly_balance / creditAccount.monthly_quota) * 100, 100)}%`,
                    }}
                  />
                </div>

                <div>
                  <p className="text-sm text-muted-foreground mb-2">Current credit period</p>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>
                        {format(
                          new Date(creditAccount.current_period_start),
                          'yyyy-MM-dd HH:mm:ss',
                        )}
                      </span>
                      <span>
                        {format(new Date(creditAccount.current_period_end), 'yyyy-MM-dd HH:mm:ss')}
                      </span>
                    </div>
                    <div className="relative w-full bg-muted rounded-full h-2">
                      {(() => {
                        const now = creditAccount.test_clock
                          ? // @ts-ignore
                            new Date(creditAccount.test_clock.frozen_time * 1000)
                          : new Date();
                        const start = new Date(creditAccount.current_period_start);
                        const end = new Date(creditAccount.current_period_end);
                        const totalDuration = end.getTime() - start.getTime();
                        const elapsed = now.getTime() - start.getTime();
                        const progress = Math.max(
                          0,
                          Math.min(100, (elapsed / totalDuration) * 100),
                        );

                        return (
                          <>
                            <div
                              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${progress}%` }}
                            />
                            <div
                              className="absolute top-0 w-1 h-2 bg-blue-700 rounded-full"
                              style={{ left: `${progress}%`, transform: 'translateX(-50%)' }}
                            />
                          </>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Subscription Card */}
          <div className="rounded-lg border p-4 bg-card">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold">Subscription</h3>
              <div className="flex items-center gap-1">
                {subscription && (
                  <>
                    {subscription.unpaid_invoice_url && (
                      <a
                        href={subscription.unpaid_invoice_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex"
                      >
                        <Button
                          variant="default"
                          size="sm"
                          className="bg-orange-500 hover:bg-orange-600"
                        >
                          Pay invoice
                        </Button>
                      </a>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      loading={loadingActions['billing-portal']}
                      onClick={() =>
                        handleAsyncAction('billing-portal', handleCreateBillingPortalSession)
                      }
                    >
                      Go to customer portal
                    </Button>
                    {subscription?.external_id ? (
                      <a
                        href={`https://dashboard.stripe.com/test/subscriptions/${subscription.external_id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex"
                      >
                        <Button
                          variant="icon"
                          size="sm"
                          className="opacity-60 hover:opacity-100"
                          title="Open in Stripe Dashboard"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </a>
                    ) : null}
                    <Button
                      variant="icon"
                      size="sm"
                      onClick={() => copyToClipboard(subscription.id, 'Subscription')}
                      className="opacity-60 hover:opacity-100"
                      title="Copy Subscription ID"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>
            </div>

            {subscription ? (
              // 有订阅时的内容

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-muted-foreground">Current product tier:</p>
                    <p className="font-medium text-sm">
                      {subscription.product_tier || 'Active Subscription'}
                    </p>
                    {subscription.cancel_at_period_end && (
                      <div className="text-xs text-orange-600 bg-orange-100 px-1.5 py-0.5 rounded">
                        Canceling at period end
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-muted-foreground">Billing interval:</p>
                    <p className="font-medium text-sm">
                      {subscription.billing_interval === 'monthly' ? 'Monthly' : 'Yearly'}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-muted-foreground">Status:</p>
                    <span
                      className={`text-xs px-2 py-1 rounded-full font-medium ${
                        subscription.status === 'active'
                          ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                          : subscription.status === 'past_due'
                            ? 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300'
                            : subscription.status === 'incomplete'
                              ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300'
                              : 'bg-gray-100 text-gray-700 dark:bg-gray-900 dark:text-gray-300'
                      }`}
                    >
                      {subscription.status}
                    </span>
                  </div>
                </div>

                {subscription.current_period_start && subscription.current_period_end && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">Current billing period</p>
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs">
                        <span>
                          {format(
                            new Date(subscription.current_period_start),
                            'yyyy-MM-dd HH:mm:ss',
                          )}
                        </span>
                        <span>
                          {format(new Date(subscription.current_period_end), 'yyyy-MM-dd HH:mm:ss')}
                        </span>
                      </div>
                      <div className="relative w-full bg-muted rounded-full h-2">
                        {(() => {
                          const now = creditAccount?.test_clock
                            ? // @ts-ignore
                              new Date(creditAccount.test_clock.frozen_time * 1000)
                            : new Date();
                          const start = new Date(subscription.current_period_start);
                          const end = new Date(subscription.current_period_end);
                          const totalDuration = end.getTime() - start.getTime();
                          const elapsed = now.getTime() - start.getTime();
                          const progress = Math.max(
                            0,
                            Math.min(100, (elapsed / totalDuration) * 100),
                          );

                          return (
                            <>
                              <div
                                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${progress}%` }}
                              />
                              <div
                                className="absolute top-0 w-1 h-2 bg-green-700 rounded-full"
                                style={{ left: `${progress}%`, transform: 'translateX(-50%)' }}
                              />
                            </>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                )}

                {/* Subscription Test Actions */}
                <div className="space-y-1.5">
                  <div
                    className={`grid gap-1.5 ${getAvailablePlans().length <= 2 ? 'grid-cols-2' : 'grid-cols-4'}`}
                  >
                    {subscription ? (
                      <>
                        {getAvailablePlans().map(({ tier, interval }) => {
                          const actionKey = `change-${tier}-${interval}`;
                          return (
                            <Button
                              key={`${tier}-${interval}`}
                              variant={
                                isCurrentPlan(subscription, tier, interval) ? 'default' : 'outline'
                              }
                              size="xs"
                              disabled={isCurrentPlan(subscription, tier, interval)}
                              loading={loadingActions[actionKey]}
                              onClick={() =>
                                handleAsyncAction(actionKey, () =>
                                  handleUpdateSubscription(tier, interval),
                                )
                              }
                              className="text-xs"
                            >
                              {getButtonText(subscription, tier, interval)}
                            </Button>
                          );
                        })}
                      </>
                    ) : (
                      <>
                        {getAvailablePlans().map(({ tier, interval }) => {
                          const actionKey = `subscribe-${tier}-${interval}`;
                          const tierName = tier === 'pro' ? 'Pro' : 'Max';
                          const intervalName = interval === 'monthly' ? 'monthly' : 'yearly';
                          return (
                            <Button
                              key={`${tier}-${interval}`}
                              variant="outline"
                              size="xs"
                              loading={loadingActions[actionKey]}
                              onClick={() =>
                                handleAsyncAction(actionKey, () =>
                                  handleCreateSubscription(tier, interval),
                                )
                              }
                              className="text-xs"
                            >
                              Subscribe {tierName} {intervalName}
                            </Button>
                          );
                        })}
                      </>
                    )}
                  </div>

                  {subscription && (
                    <div className="grid grid-cols-2 gap-1.5">
                      <Button
                        variant="outline"
                        size="xs"
                        loading={loadingActions['cancel-at-period-end']}
                        onClick={() =>
                          handleAsyncAction('cancel-at-period-end', handleCancelSubscription)
                        }
                        className="text-xs"
                      >
                        Cancel at period end
                      </Button>
                      <Button
                        variant="destructive"
                        size="xs"
                        loading={loadingActions['cancel-now']}
                        onClick={() =>
                          handleAsyncAction('cancel-now', handleDeleteSubscriptionForTest)
                        }
                        className="text-xs"
                      >
                        Cancel right now
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              // 无订阅时的内容
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  No active subscription. Choose a plan to get started.
                </p>

                {/* Create Subscription Actions */}
                <div className="space-y-1.5">
                  <div
                    className={`grid gap-1.5 ${getAvailablePlans().length <= 2 ? 'grid-cols-2' : 'grid-cols-4'}`}
                  >
                    {getAvailablePlans().map(({ tier, interval }) => {
                      const actionKey = `subscribe-${tier}-${interval}`;
                      const tierName = tier === 'pro' ? 'Pro' : 'Max';
                      const intervalName = interval === 'monthly' ? 'monthly' : 'yearly';
                      return (
                        <Button
                          key={`${tier}-${interval}`}
                          variant="outline"
                          size="xs"
                          loading={loadingActions[actionKey]}
                          onClick={() =>
                            handleAsyncAction(actionKey, () =>
                              handleCreateSubscription(tier, interval),
                            )
                          }
                          className="text-xs"
                        >
                          Subscribe {tierName} {intervalName}
                        </Button>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </DevOnly>
    </div>
  );
}
