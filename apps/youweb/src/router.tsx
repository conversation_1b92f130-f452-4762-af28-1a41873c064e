import { isLocalRuntime } from '@repo/common/env';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { MobileThoughtBodyApp } from './components/thought/mobile/mobile-thought-body-app';
import NotFoundPage from './pages/404/page';
import InternalErrorPage from './pages/500/page';
import BoardDetailPage from './pages/board-detail/page';
// 直接导入页面组件
import BoardPage from './pages/boards/page';
import ChatPage from './pages/chats/[slug]/page';
import IconShowcase from './pages/icon-showcase/page';
// 打印页面的独立布局
import { BasicLayout } from './pages/layout/basic-layout';
import { MobileLayout } from './pages/layout/mobile-layout';
// 根布局组件 - 包含 RootProvider
import { RootLayout } from './pages/layout/root';
import { MobileEditorPage } from './pages/Mobile-Editor';
import PrintViewPage from './pages/print-view/page';
import { Settings } from './pages/settings/Settings';
import SnipDetailPage from './pages/snips/[slug]/page';
import SnipSimplifiedPage from './pages/snips/[slug]/simplified/page';
import SnipsPage from './pages/snips/page';
import ThoughtDetailPage from './pages/thoughts/[slug]/page';
import ThoughtsPage from './pages/thoughts/page';

export const router = createBrowserRouter([
  // 打印页面 - 独立路由，不使用主布局
  {
    path: '/print-view',
    Component: BasicLayout,
    children: [
      {
        index: true,
        Component: PrintViewPage,
      },
    ],
  },
  // 404页面 - 独立路由，使用BasicLayout
  {
    path: '/404',
    Component: BasicLayout,
    children: [
      {
        index: true,
        Component: NotFoundPage,
      },
    ],
  },
  // 500页面 - 独立路由，使用BasicLayout
  {
    path: '/500',
    Component: BasicLayout,
    children: [
      {
        index: true,
        Component: InternalErrorPage,
      },
    ],
  },
  // 主应用路由 - 使用统一布局
  {
    path: '/',
    Component: RootLayout,
    children: [
      {
        index: true,
        element: <Navigate to="/boards" replace />,
      },
      {
        path: 'boards',
        children: [
          {
            index: true,
            Component: BoardPage,
          },
          {
            path: ':boardId',
            Component: BoardDetailPage,
          },
        ],
      },
      {
        path: 'snips',
        children: [
          {
            index: true,
            Component: SnipsPage,
          },
          {
            path: ':slug',
            children: [
              {
                index: true,
                Component: SnipDetailPage,
              },
            ],
          },
        ],
      },
      {
        path: 'thoughts',
        children: [
          {
            index: true,
            Component: ThoughtsPage,
          },
          {
            path: ':slug',
            Component: ThoughtDetailPage,
          },
        ],
      },

      {
        path: 'settings',
        Component: Settings,
      },
      ...(isLocalRuntime()
        ? [
            {
              path: 'icon',
              Component: IconShowcase,
            },
          ]
        : []),
      // 通配符路由 - 重定向到404页面
      {
        path: '*',
        element: <Navigate to="/404" replace />,
      },
    ],
  },
  // mobile thought 系列
  {
    path: '/mobileThought',
    Component: MobileLayout,
    children: [
      {
        index: true,
        Component: MobileThoughtBodyApp,
      },
    ],
  },
  {
    path: '/mobileEditor',
    Component: MobileLayout,
    children: [
      {
        index: true,
        Component: MobileEditorPage,
      },
    ],
  },
  {
    path: '/mobile',
    Component: MobileLayout,
    children: [
      {
        path: 'thought-detail',
        Component: MobileThoughtBodyApp,
      },
      {
        path: 'thought-editor',
        Component: MobileEditorPage,
      },
    ],
  },
  {
    path: '/snips/:slug/simplified',
    Component: MobileLayout,
    children: [
      {
        index: true,
        Component: SnipSimplifiedPage,
      },
    ],
  },
  // 同上，重复路由, 三个月之后下掉老的，用新的
  {
    path: '/mobile/snips/:slug',
    Component: MobileLayout,
    children: [
      {
        index: true,
        Component: SnipSimplifiedPage,
      },
    ],
  },

  {
    path: '/mobile/chats/:slug',
    Component: MobileLayout,
    children: [
      {
        index: true,
        Component: ChatPage,
      },
    ],
  },
]);
