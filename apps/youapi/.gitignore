# Dependencies
/node_modules

# Build outputs
/dist
/build

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment files
.env
.env.*
.env.local
.env.*.local

# Config files
.config.*.json
/src/types/config.generated.ts

# IDE
.vscode/*
!.vscode/extensions.json
.idea

# OS
.DS_Store
*.swp
*.swo

# Testing
/coverage
.nyc_output
test/**/*.js
test/**/*.d.ts

# Temporary files
*.tmp
*.temp

# Supabase
supabase
!src/shared/supabase
