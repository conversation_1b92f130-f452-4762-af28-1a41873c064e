{"extends": "@repo/typescript-config/nestjs.json", "compilerOptions": {"strict": false, "allowJs": true, "sourceMap": true, "jsx": "react-jsx", "esModuleInterop": true, "lib": ["es2023", "DOM"], "target": "ES2022", "baseUrl": ".", "outDir": "./dist", "paths": {"@/*": ["./src/*"], "@shared/*": ["./src/shared/*"]}, "plugins": [{"name": "@nestjs/swagger", "options": {"introspectComments": true}}]}, "watchOptions": {"watchFile": "fixedPollingInterval"}, "include": ["src/**/*.ts", "test/**/*.ts"], "exclude": ["scripts", "node_modules", "dist"]}