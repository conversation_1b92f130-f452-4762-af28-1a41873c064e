# 重要文档

## 开发规范和架构

- [代码结构和开发规范](./docs/code-structure-and-standards.md) - 项目架构、代码组织、编码规范的完整指南
- [对象参数模式避免参数传错](./docs/avoiding-parameter-errors.md) - 函数参数设计的最佳实践，避免参数顺序错误

## 业务系统设计

### 订阅积分系统

- [积分系统设计文档](./docs/subscription-and-credit/CREDIT-SYSTEM-DESIGN.md) - 基于 DDD 的积分系统核心设计方案
- [积分系统测试用例](./docs/subscription-and-credit/CREDIT-SYSTEM-TESTS.md) - 完整的 E2E 测试场景和验证方案


# 当前任务计划

## 苹果内购接入任务计划

### 项目目标
在现有 Stripe 订阅和积分系统基础上，完成苹果内购的完整接入，实现多平台订阅统一管理。

### 现状分析
✅ **已完成**：
- Stripe 订阅创建、更新、取消流程完整实现
- 积分刷新、重置机制完善，支持事件驱动和定时任务
- Apple 客户端配置和交易验证框架搭建
- 基础的 Apple 通知处理器框架

🔄 **进行中**：
- Apple 交易验证逻辑部分实现（缺少积分同步）
- Apple 通知处理框架已搭建（核心逻辑待实现）

⏳ **待完成**：苹果内购核心业务逻辑和完整测试覆盖

---

### 阶段零：方案设计和讨论（最高优先级）

#### 任务 0.0：现有架构和设计梳理
- **任务内容**:
  - [x] 梳理现有 Stripe 集成的完整架构和实现
  - [x] 分析现有积分系统的设计模式和业务规则
  - [x] 整理当前订阅聚合和积分账户聚合的核心逻辑
  - [x] 提取可复用的设计模式和架构组件
  - [x] 产出实际领域模型类图文档
- **输出物**: `docs/subscription-and-credit/CURRENT-ARCHITECTURE-ANALYSIS.md` - 现有架构分析文档
- **参考资源**: 
  - `docs/subscription-and-credit/CREDIT-SYSTEM-DESIGN.md`
  - `docs/subscription-and-credit/CREDIT-SYSTEM-TESTS.md`
  - 现有 Stripe 集成代码实现
- **参与人员**: 架构师、后端开发

#### 任务 0.0a：业务用例时序图分析
- **任务内容**:
  - [ ] 分析 `subscription.controller.ts` 所有业务用例的完整时序图
  - [ ] 分析 `credit.controller.ts` 所有业务用例的完整时序图
  - [ ] 梳理跨聚合的业务流程和交互模式
  - [ ] 提取可复用的业务流程模式
- **输出物**: `docs/subscription-and-credit/CURRENT-BUSINESS-FLOWS.md` - 现有业务流程时序图分析
- **依赖**: 任务 0.0 的架构分析结果
- **参与人员**: 架构师、后端开发

#### 任务 0.1：技术方案设计
- **任务内容**:
  - [ ] 基于现有架构分析，设计 Apple 订阅状态到系统状态的完整映射方案
  - [ ] 设计 Apple 通知处理的数据流和状态转换（参考 Stripe 模式）
  - [ ] 设计积分同步的触发时机和防重机制（复用现有机制）
  - [ ] 设计多平台订阅冲突处理策略
- **输出物**: `docs/subscription-and-credit/APPLE-INTEGRATION-DESIGN.md` - 技术设计文档，包含状态图和时序图
- **依赖**: 任务 0.0 的架构分析结果
- **参与人员**: 架构师、后端开发、产品经理

#### 任务 0.2：业务流程设计讨论
- **讨论议题**:
  - [ ] 基于现有 Stripe 业务规则，设计 Apple 订阅与 Stripe 订阅的优先级策略
  - [ ] 用户在不同平台间切换订阅的处理逻辑（基于现有积分重置逻辑）
  - [ ] 异常情况的降级和容错方案（如 Apple 通知失败）
  - [ ] 积分重置时机的业务规则确认（与现有规则保持一致）
- **输出物**: `docs/subscription-and-credit/APPLE-BUSINESS-FLOWS.md` - 业务流程确认文档和边界情况处理方案
- **依赖**: 任务 0.0 的架构分析结果
- **参与人员**: 产品经理、业务负责人、技术团队

#### 任务 0.3：数据一致性方案设计
- **设计重点**:
  - [ ] 基于现有 Stripe 数据一致性方案，设计 Apple 通知延迟或失败的处理
  - [ ] 用户同时在多平台操作订阅的并发控制（扩展现有 SELECT FOR UPDATE 机制）
  - [ ] 积分账户与订阅状态的最终一致性方案（复用现有防重机制）
  - [ ] 异常恢复和数据修复机制（基于现有模式扩展）
- **输出物**: `docs/subscription-and-credit/APPLE-DATA-CONSISTENCY.md` - 数据一致性方案文档
- **技术方案**: 基于现有事务机制和幂等性设计
- **依赖**: 任务 0.0 的架构分析结果

#### 任务 0.4：测试策略设计
- **策略内容**:
  - [ ] 基于现有 Stripe 测试框架，设计 Apple 通知的各种场景测试用例
  - [ ] 设计多平台订阅切换的测试场景（扩展现有测试模式）
  - [ ] 设计异常情况和边界条件的测试覆盖（复用现有测试工具）
  - [ ] 确定测试环境和数据准备方案（基于现有 TestIamModuleHelper）
- **输出物**: `docs/subscription-and-credit/APPLE-TEST-STRATEGY.md` - 测试策略文档和测试用例清单
- **依赖**: 任务 0.0 的架构分析结果

#### 任务 0.5：技术评审和方案确认
- **评审内容**:
  - [ ] 技术方案的可行性和风险评估
  - [ ] 与现有系统的兼容性分析
  - [ ] 性能影响评估和优化建议
  - [ ] 监控和告警方案设计
- **输出物**: `docs/subscription-and-credit/APPLE-IMPLEMENTATION-PLAN.md` - 技术评审报告和最终实施方案
- **参与人员**: 技术委员会、架构师、开发团队

---

### 阶段一：核心业务逻辑实现（高优先级）

#### 任务 1.1：完善 Apple 通知处理逻辑
- **文件位置**: `/src/modules/iam/services/handlers/space/apple-event.handler.ts`
- **任务内容**:
  - [ ] 实现订阅创建逻辑（基于 transactionInfo）
  - [ ] 实现订阅更新逻辑（状态变更、续期处理）
  - [ ] 实现订阅取消逻辑（过期、退款处理）
  - [ ] 实现积分账户同步逻辑
- **参考实现**: `StripeEventHandler` 的事件处理模式

#### 任务 1.2：完善交易验证积分同步
- **文件位置**: `/src/modules/iam/services/handlers/subscription/verify-transaction.handler.ts`
- **任务内容**:
  - [ ] 添加积分账户创建/同步逻辑
  - [ ] 实现基于 Apple 订阅状态的积分重置
  - [ ] 处理订阅状态与积分账户的一致性
- **技术要点**: 复用现有 `CreditAccount.syncFromSubscription()` 方法

#### 任务 1.3：Apple 订阅状态映射
- **文件位置**: `/src/modules/iam/domain/subscription/` 相关文件
- **任务内容**:
  - [ ] 定义 Apple 订阅状态到系统状态的映射规则
  - [ ] 实现 `Subscription.createFromAppleTransaction()` 方法
  - [ ] 实现 `Subscription.syncFromAppleTransaction()` 方法
- **技术要点**: 参考现有 Stripe 的实现模式

---

### 阶段二：产品配置和环境管理（中优先级）

#### 任务 2.1：Apple 产品配置验证
- **任务内容**:
  - [ ] 验证产品 ID 映射关系（Free/Pro/Max）
  - [ ] 确认价格层级对应关系
  - [ ] 配置沙盒和生产环境产品 ID
- **配置文件**: 环境变量和配置常量

#### 任务 2.2：环境隔离和配置管理
- **任务内容**:
  - [ ] 完善沙盒环境配置
  - [ ] 实现环境自动切换逻辑
  - [ ] 配置生产环境 Apple 证书和密钥
- **技术要点**: 基于现有 `AppleHolder` 扩展

---

### 阶段三：测试覆盖和质量保证（高优先级）

#### 任务 3.1：Apple 通知处理测试
- **文件位置**: `/src/modules/iam/tests/` 新增测试文件
- **任务内容**:
  - [ ] 编写 Apple 通知处理的集成测试
  - [ ] 模拟各种订阅状态变更场景
  - [ ] 验证积分账户同步正确性
- **测试场景**: 订阅创建、更新、续期、取消、退款

#### 任务 3.2：交易验证完整流程测试
- **任务内容**:
  - [ ] 扩展现有 `subscription-verify-transaction.integration.spec.ts`
  - [ ] 添加积分同步验证测试用例
  - [ ] 测试错误处理和边界情况
- **技术要点**: 使用现有测试框架和模拟服务

#### 任务 3.3：E2E 测试扩展
- **任务内容**:
  - [ ] 扩展 `iam-routes.e2e-spec.ts` 支持 Apple 路由
  - [ ] 添加 Apple 订阅完整生命周期测试
  - [ ] 验证与 Stripe 订阅的兼容性

---

### 阶段四：监控和错误处理（中优先级）

#### 任务 4.1：错误处理和重试机制
- **任务内容**:
  - [ ] 实现 Apple 通知失败重试逻辑
  - [ ] 添加死信队列处理
  - [ ] 完善异常情况的错误码和消息
- **技术方案**: 基于现有异常处理框架扩展

#### 任务 4.2：监控和告警
- **任务内容**:
  - [ ] 添加 Apple 订阅状态监控指标
  - [ ] 实现数据一致性检查和告警
  - [ ] 配置关键业务流程的监控告警

---

### 阶段五：文档和部署（低优先级）

#### 任务 5.1：API 文档更新
- **任务内容**:
  - [ ] 更新 Apple 相关 API 的文档注释
  - [ ] 完善错误码和响应格式说明
  - [ ] 添加使用示例和最佳实践

#### 任务 5.2：部署和配置
- **任务内容**:
  - [ ] 配置生产环境 Apple 相关环境变量
  - [ ] 更新部署脚本和配置文件
  - [ ] 验证生产环境功能正常

---

### 总体规划
- **阶段零（方案设计）**: ⭐ **最关键阶段**
- **阶段一（核心逻辑）**: 核心业务逻辑实现
- **阶段二（配置管理）**: 产品配置和环境管理  
- **阶段三（测试覆盖）**: 测试覆盖和质量保证
- **阶段四（监控告警）**: 监控和错误处理
- **阶段五（文档部署）**: 文档和部署

### 风险和注意事项

1. **技术风险**:
   - Apple 通知的异步性可能导致状态不一致
   - 需要处理 Apple 和 Stripe 订阅的竞态条件

2. **业务风险**:
   - 积分重置逻辑的正确性关系到用户体验
   - 订阅状态映射错误可能导致计费问题

3. **测试重点**:
   - 重点测试边界情况和异常处理
   - 确保与现有 Stripe 系统的兼容性
   - 验证多平台订阅切换场景

### 成功标准

1. ✅ Apple 订阅完整生命周期管理
2. ✅ 积分系统与 Apple 订阅状态同步准确
3. ✅ 完整的测试覆盖（单元测试 + 集成测试 + E2E 测试）
4. ✅ 生产环境稳定运行，无数据一致性问题
5. ✅ 监控告警覆盖关键业务指标
