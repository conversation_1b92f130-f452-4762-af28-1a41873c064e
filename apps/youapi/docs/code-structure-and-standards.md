# 代码结构和开发规范

本文档整理了 YouAPI 项目的代码组织结构、架构原则和开发规范，为开发团队提供统一的标准。

## 目录

- [项目架构概述](#项目架构概述)
- [代码组织结构](#代码组织结构)
- [分层架构设计](#分层架构设计)
- [API 设计规范](#api-设计规范)
- [命名约定](#命名约定)
- [编码规范](#编码规范)
- [业务开发模式](#业务开发模式)
- [异常处理策略](#异常处理策略)
- [测试规范](#测试规范)

## 项目架构概述

YouAPI 是一个基于 DDD（领域驱动设计）和 Clean Architecture 的 NestJS 项目，采用模块化结构和 CQRS 模式。

### 核心设计原则

1. **DDD + Clean Architecture**: 清晰的领域边界和分层职责
2. **YAGNI 原则**: 只实现当前确实需要的功能，避免过度设计
3. **模块化设计**: 每个模块代表一个限界上下文（Bounded Context）
4. **CQRS 模式**: 命令查询职责分离
5. **事件驱动**: 使用领域事件进行模块间通信

### 技术栈

- **框架**: NestJS + TypeScript
- **数据库**: PostgreSQL + TypeORM
- **包管理**: pnpm
- **架构模式**: DDD + CQRS + Event Sourcing

## 代码组织结构

### 整体项目结构

```
apps/youapi/
├── src/
│   ├── modules/                    # 业务模块
│   │   ├── iam/                   # 身份认证和访问管理
│   │   ├── material/              # 素材管理
│   │   └── ...
│   ├── common/                    # 通用工具
│   └── main.ts                    # 应用入口
├── docs/                          # 项目文档
├── test/                          # E2E 测试文件
└── package.json
```

### 模块内部结构

每个业务模块遵循以下标准结构：

```
xxx/                                           # 模块根目录
├── controllers/                               # 按聚合组织的控制器
│   ├── xxx.controller.ts                     # 聚合控制器
│   ├── xxx.controller.spec.ts                # 控制器单元测试
│   ├── yyy.controller.ts                     # 另一个聚合控制器
│   └── yyy.controller.spec.ts                # 控制器单元测试
├── services/                                  # 应用服务层
│   ├── xxx.service.ts                        # 通用应用服务，细粒度方法
│   ├── xxx.service.spec.ts                   # 服务单元测试
│   ├── commands/                              # 命令（复杂业务用例）
│   │   ├── create-xxx.command.ts
│   │   ├── create-xxx.handler.ts
│   │   ├── create-xxx.handler.spec.ts
│   │   ├── update-xxx.command.ts
│   │   ├── update-xxx.handler.ts
│   │   └── update-xxx.handler.spec.ts
│   ├── queries/                               # 查询（复杂查询用例）
│   │   ├── list-xxx.query.ts
│   │   ├── list-xxx.handler.ts
│   │   └── list-xxx.handler.spec.ts
│   └── handlers/                              # 事件处理器
│       ├── xxx-event.handler.ts
│       └── xxx-event.handler.spec.ts
├── domain/                                    # 按聚合组织的领域层
│   ├── xxx/                                  # 聚合根目录
│   │   ├── models/
│   │   │   ├── xxx.entity.ts                # 实体（聚合根）
│   │   │   └── xxx.entity.spec.ts           # 实体单元测试
│   │   ├── events/
│   │   │   ├── xxx-created.event.ts         # 领域事件
│   │   │   ├── xxx-updated.event.ts
│   │   │   └── xxx-deleted.event.ts
│   │   ├── services/
│   │   │   ├── xxx-domain.service.ts        # 领域服务
│   │   │   └── xxx-domain.service.spec.ts   # 领域服务单元测试
│   │   └── value-objects/
│   │       ├── xxx-status.vo.ts             # 值对象
│   │       └── xxx-status.vo.spec.ts        # 值对象单元测试
│   └── yyy/                                  # 另一个聚合
│       └── ...
├── repositories/                              # 按聚合组织的仓储层
│   ├── xxx.repository.ts                     # 仓储接口
│   ├── xxx.repository.impl.ts                # 仓储实现（继承 BaseRepository）
│   ├── xxx.repository.spec.ts                # 仓储单元测试
│   ├── yyy.repository.ts
│   └── yyy.repository.impl.ts
├── dto/                                       # 数据传输对象
│   ├── xxx.dto.ts                           # 实体 DTO
│   ├── create-xxx.dto.ts
│   ├── update-xxx.dto.ts
│   ├── list-xxx.dto.ts
│   └── ...
└── xxx.module.ts                             # 模块定义
```

### 关键组织原则

1. **按聚合组织**: domain/ 和 controllers/ 按聚合根划分
2. **应用服务分层**:
   - `xxx.service.ts`: 通用应用服务，提供细粒度的原子操作方法
   - `commands/queries/`: 处理复杂的、特殊的业务用例
3. **模块命名**: 可以使用简单名称（如 `material`）或复合词（如 `material-mng`）
4. **单一职责**: 每个文件和类有明确的单一职责
5. **测试就近原则**: 单元测试文件与被测试文件放在同一目录
6. **统一仓储**: 所有仓储实现继承 `BaseRepository`

## 分层架构设计

### 架构分层

```
┌─────────────────────────┐
│   Controller 层         │ ← HTTP 请求处理，参数校验
├─────────────────────────┤
│   应用服务层             │ ← 事务协调、业务流程编排
│   (CommandHandler)      │   跨聚合业务逻辑
├─────────────────────────┤
│   领域层                │ ← 核心业务逻辑、业务规则
│   (Domain Entities)     │   领域事件、值对象
├─────────────────────────┤
│   Repository 层         │ ← 数据持久化抽象
└─────────────────────────┘
```

### 各层职责

#### Controller 层
- 处理 HTTP 请求和响应
- 使用 ValidationPipe 进行格式校验
- 提取用户身份信息
- 调用应用服务层

#### 应用服务层 (Service)
- 实现业务流程协调
- 处理事务边界
- 跨聚合的业务逻辑
- 发布和处理领域事件
- 信任已校验的输入数据

#### 领域层 (Domain)
- 包含业务实体、值对象
- 实现核心业务规则和不变量
- 发布领域事件
- 领域服务处理跨实体逻辑

#### Repository 层
- 提供数据访问抽象
- 使用领域实体作为接口
- 隔离数据库实现细节

### 校验策略

- **Controller 层**: 依赖 NestJS ValidationPipe 进行格式校验和类型转换
- **Service 层**: 专注业务逻辑协调，不重复格式校验
- **Domain 层**: 执行业务规则校验和领域不变量检查

## API 设计规范

### 设计风格

- **非 RESTful 风格**: 所有 API 端点统一使用 POST 方法
- **命名约定**: 路由路径包含具体的操作名称
  ```
  POST /api/v1/subscription/updateSubscription
  POST /api/v1/credit/getCreditBalance
  ```
- **参数传递**: 所有参数通过请求体传递
- **响应格式**: 统一的 JSON 响应格式
- **状态码**: 使用 `@HttpCode(200)` 统一返回 200 状态码

### 路由组织

- **按聚合划分**: `/api/v1/{aggregate}/{operation}`
- **完整业务语义**: 使用完整描述的方法名，放弃 RESTful 约定
- **模块前缀**: 每个业务模块有独立的路由空间

### API 文档规范

- **中文文档**: 使用中文的 summary 和 description
- **完整参数**: 详细描述请求参数和响应格式
- **示例代码**: 提供请求和响应示例

## 命名约定

### 文件和目录命名

- **文件和目录**: kebab-case
  ```
  order-management/
  user-profile.controller.ts
  create-order.handler.ts
  ```

### 代码命名

- **类名和接口**: PascalCase
  ```typescript
  class OrderService {}
  interface UserRepository {}
  class CreateOrderDto {}
  ```

- **变量和方法**: camelCase
  ```typescript
  const orderTotal = 100;
  const isValidUser = true;
  function getUserById(id: string) {}
  ```

- **常量**: SCREAMING_SNAKE_CASE
  ```typescript
  const MAX_ORDER_ITEMS = 100;
  const DEFAULT_PAGE_SIZE = 20;
  ```

### 字段命名风格

- **代码内部**: 统一使用 camelCase 格式
- **数据库字段**: 保持 snake_case 风格，通过 ORM 映射到 camelCase

### 文件后缀约定

- **实体**: `*.entity.ts`
- **值对象**: `*.vo.ts`
- **数据传输对象**: `*.dto.ts`
- **领域服务**: `*-domain.service.ts`
- **领域事件**: `*.event.ts`
- **命令**: `*.command.ts`
- **查询**: `*.query.ts`
- **处理器**: `*.handler.ts`

## 编码规范

### TypeScript 规范

#### 类型定义

推荐使用字符串字面量类型，避免 TypeScript enum：

```typescript
// 推荐方式
export const OrderStatus = {
  DRAFT: 'draft',
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  SHIPPED: 'shipped',
} as const;

export type OrderStatus = typeof OrderStatus[keyof typeof OrderStatus];

// 使用
const order = { status: OrderStatus.DRAFT };
```

#### 异步处理

- **异步函数**: 直接 return，不要 return await
  ```typescript
  // 推荐
  async function getUser(id: string): Promise<User> {
    return userRepository.findById(id);
  }

  // 避免
  async function getUser(id: string): Promise<User> {
    return await userRepository.findById(id);
  }
  ```

#### null 和 undefined

- **优先使用**: `?` 可选属性
- **其次使用**: `undefined`
- **避免混用**: `undefined` 和 `null`

```typescript
// 推荐
interface User {
  id: string;
  email?: string;  // 可选属性
  profile: UserProfile | undefined;
}
```

### 文案和注释规范

- **所有文案使用英文**: 面向用户的文案、错误消息等
- **所有注释使用中文**: 代码注释、文档说明等

```typescript
// 计算订单总金额，包含税费
function calculateOrderTotal(order: Order): number {
  // 业务逻辑实现
  return order.items.reduce((sum, item) => sum + item.price, 0);
}

// 错误消息使用英文
throw new BadRequestException('Invalid order status');
```

### 其他规范

- **不使用 barrel exports**: 避免 index.ts 文件，参考 [NestJS Issue](https://github.com/nestjs/nest/issues/1181#issuecomment-430197191)
- **包管理器**: 统一使用 pnpm

## 业务开发模式

### 查询方法命名规范

#### 动词语义区分

- **get**: 期望资源存在，不存在时抛异常
  ```typescript
  async getUserById(id: string): Promise<User> // 不存在抛异常
  ```

- **find**: 资源可能不存在，不存在时返回 null
  ```typescript
  async findUserById(id: string): Promise<User | null> // 可能返回 null
  ```

- **list**: 列表查询，返回数组
  ```typescript
  async listUsers(): Promise<User[]>
  async listUsersByRole(role: string): Promise<User[]>
  ```

- **search**: 真正的搜索技术（全文索引、语义搜索）
  ```typescript
  async searchProductsByKeyword(keyword: string): Promise<Product[]>
  ```

#### 分页查询区分

- **非分页**: `listUsers` → 返回 `User[]`
- **分页**: `listUsersPaged` → 返回 `PageResult<User>`

#### 复杂查询命名

- **参数对象模式**: 避免超长方法名
  ```typescript
  async listUsers(criteria: UserSearchCriteria): Promise<User[]>
  ```

- **业务场景命名**: 反映具体业务场景
  ```typescript
  async listActiveUsersInDepartment(department: string): Promise<User[]>
  ```

### 期望成功与可能失败

#### 传统约定
- **Repository**: `findById` → `User | null`
- **Service**: `getById` → `User` (期望存在，失败抛异常)

#### 其他操作
- **期望成功**: `deleteOrder` → 失败时抛异常
- **可能失败**: `tryDeleteOrder` → 返回 `boolean`

### 应用服务层设计

应用服务层采用分层设计，区分通用服务和特殊业务用例：

#### 通用应用服务 (xxx.service.ts)
- **职责**: 提供细粒度的、原子性的业务操作
- **特点**: 方法简单、直接、可复用
- **适用场景**: CRUD 操作、单一实体操作、其他模块调用

```typescript
// 示例：素材管理服务
@Injectable()
export class MaterialService {
  // 细粒度方法，适合其他模块调用
  async createNote(userId: string, data: CreateNoteData): Promise<Note> {
    // 单一职责：创建笔记
  }

  async findNoteById(id: string): Promise<Note | null> {
    // 单一职责：查找笔记
  }

  async deleteNote(id: string): Promise<void> {
    // 单一职责：删除笔记
  }
}
```

#### 命令处理器 (commands/)
- **职责**: 处理复杂的、特殊的业务用例
- **特点**: 涉及多步骤、事务边界、业务规则校验
- **适用场景**: 复杂业务流程、跨聚合操作、特殊业务规则

```typescript
// 示例：复杂的笔记创建流程
@CommandHandler(CreateNoteWithWorkflowCommand)
export class CreateNoteWithWorkflowHandler {
  async execute(command: CreateNoteWithWorkflowCommand): Promise<void> {
    // 1. 业务规则校验
    // 2. 跨聚合操作
    // 3. 事件发布
    // 4. 事务管理
  }
}
```

#### 查询处理器 (queries/)
- **职责**: 处理复杂的查询场景
- **特点**: 涉及多表关联、复杂筛选、数据聚合
- **适用场景**: 复杂报表、数据统计、多维度查询

```typescript
// 示例：复杂的笔记数据统计
@QueryHandler(GetNoteAnalyticsQuery)
export class GetNoteAnalyticsHandler {
  async execute(query: GetNoteAnalyticsQuery): Promise<NoteAnalytics> {
    // 复杂的数据聚合和统计逻辑
  }
}
```

### 模块间通信策略

- **简单查询**: 可以直接调用其他模块的 Service (通用应用服务)
- **复杂业务流程**: 通过 Domain Events 进行异步处理
- **原则**: 简单查询用直接调用，复杂流程用事件驱动

示例：
```typescript
// 直接调用其他模块的通用服务
@Injectable()
export class OrderService {
  constructor(
    private readonly materialService: MaterialService, // 直接注入
  ) {}

  async createOrder(userId: string, materialId: string) {
    // 调用素材模块的细粒度方法
    const material = await this.materialService.findMaterialById(materialId);
    // ...
  }
}
```

### 模块对外接口设计

- **只暴露应用服务**: 不暴露领域模型和仓储
- **使用 DTO 传输**: 模块间数据传输使用 DTO
- **方法粒度设计**:
  - **粗粒度方法**: 完整的业务用例，面向最终用户
  - **细粒度方法**: 单一原子操作，面向其他模块调用
- **避免紧耦合**: 不能绕过业务逻辑直接访问数据层

## 异常处理策略

### 核心原则

1. **统一使用 NestJS 内置异常**:
   - `BadRequestException`
   - `UnauthorizedException`
   - `ForbiddenException`
   - `NotFoundException`
   - `ConflictException`
   - `InternalServerErrorException`

2. **让异常冒泡**: 不在每层进行 try-catch，让异常向上传播

3. **框架统一处理**: 由 NestJS 内置 Exception Filter 自动处理

4. **简化异常体系**: 避免自定义异常类和复杂转换逻辑

### 使用示例

```typescript
// 在 Service 层直接抛出标准异常
async getUserById(id: string): Promise<User> {
  const user = await this.userRepository.findById(id);
  if (!user) {
    throw new NotFoundException('User not found');
  }
  return user;
}
```

## 事务管理

### BaseRepository 和 @Transactional 装饰器

项目中所有的 Repository 实现都统一继承 `BaseRepository`，配合 `@Transactional` 装饰器实现优雅的事务管理。

#### Repository 继承结构

```typescript
// 仓储实现，继承 BaseRepository
@Injectable()
export class XxxRepository extends BaseRepository {

  async findById(id: string): Promise<Xxx | undefined> {
    ...
  }

  async save(xxx: Xxx): Promise<Xxx> {
    ...
  }
}
```

#### 事务装饰器使用

```typescript
@Injectable()
export class XxxService {
  constructor(
    private readonly xxxRepository: XxxRepository,
  ) {}

  // 自动事务管理
  @Transactional()
  async doSomethingInTransaction(params: XxxParams) {
    ...
  }

}
```

#### 事务管理特点

1. **声明式事务**: 使用 `@Transactional()` 装饰器声明事务边界
2. **自动回滚**: 方法内抛出异常时自动回滚事务
3. **数据一致性**: 保证业务操作的原子性

## 测试规范

### 测试文件组织

#### 单元测试
单元测试文件与被测试文件放在同一目录下：
```
src/modules/xxx/
├── controllers/
│   ├── xxx.controller.ts
│   └── xxx.controller.spec.ts     # 控制器单元测试
├── services/
│   ├── xxx.service.ts
│   └── xxx.service.spec.ts         # 服务单元测试
└── domain/xxx/models/
    ├── xxx.entity.ts
    └── xxx.entity.spec.ts          # 实体单元测试
```

#### E2E 测试
端到端测试放在项目根目录的 test/ 目录：
```
test/
├── auth.e2e-spec.ts               # 认证相关 E2E 测试
├── material.e2e-spec.ts           # 素材管理 E2E 测试
└── helpers/                       # 测试辅助工具
    ├── test-helper.ts
    └── mock-data.ts
```

### 测试运行命令

```bash
# E2E 测试
pnpm test:e2e xxx

# 单元测试
pnpm test xxx

# 覆盖率测试
pnpm test:cov
```

## 权限和安全

### 权限架构

- **IAM 模块**: 身份认证、用户管理、空间管理和权限控制
- **权限层次**: 系统级权限 > 空间级权限 > 实体级权限
- **认证流程**: AuthGuard (认证) → Controller (提取用户信息) → Service (业务逻辑)
- **API 设计**: 采用 `/spaces/:spaceId/resources` 的多租户路由结构

### 上下文传递策略

- **默认显式传递**: Controller 通过装饰器提取用户信息，显式传递给 Service 层
- **CLS 特殊场景**: 仅在横切关注点（审计、日志、监控）使用隐式上下文
- **测试友好**: 显式传递确保方法签名清晰，易于单元测试

## 开发工具和命令

### 开发环境

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建
pnpm build

# 类型检查
pnpm type-check

# 代码格式化
pnpm biome format

# 代码检查
pnpm biome check
```

### 代码质量

```bash
# 运行所有检查
pnpm lint
pnpm type-check
pnpm test
```

---

本文档是 YouAPI 项目开发的核心参考，所有开发工作都应遵循这些标准和规范。如有疑问或需要更新，请及时与团队讨论。
