# 积分系统设计文档

## 概述

本文档描述了 YouAPI 积分系统的最小可行设计方案，基于 DDD (领域驱动设计) 原则，重点关注业务领域建模和关键业务流程。

## 相关文档

- [Token 到 Credit 换算设计](./TOKEN-TO-CREDITS-CONVERSION.md) - 详细的Token换算机制和定价策略
- [积分系统测试用例](./CREDIT-SYSTEM-TESTS.md) - 完整的E2E测试场景和验证方案

# 核心设计原则

- 积分按月重置，清空旧积分，按新一期产品等级发放新积分
- 产品等级 productTier 分 free, pro, max
- 用 Stripe 和 Apple 来管理付费订阅
- 用 Stripe 的 Subscription Schedule API 来对齐 Apple 的订阅变更策略
  - 升级：立即生效，重置账单锚点时间
  - 降级：到期生效，不重置账单锚点时间
  - 同级变更时长：到期生效，不重置账单锚点时间
- 以下所说的所有时间，在代码实现中使用 Date 类型，在数据库中存储为 timestamp
- 付费订阅用 Stripe 的 billingCycleAnchor 和 Apple 的 originalPurchaseDate 来作为基本不变的锚点，用来推算每月重置时间
- 免费则用用户注册的时间，或取消付费订阅并最终生效的时间来当作每月几号重置的锚点
- 对于付费的月度订阅，使用 Stripe 和 Apple 的事件来驱动积分的重置，以免没有按照新的产品等级来发放
  - 每次续期后的新一轮月度周期是 Stripe 的 [currentPeriodStart, currentPeriodEnd] 和 Apple 的 [purchaseDate, expiresDate]
  - 重置积分时，更新 CreditAccount 的 resetAt 为 currentPeriodStart/purchaseDate，以免重复执行
- 对于付费的年度订阅
  - 在在有续期事件的月份，用 Stripe 和 Apple 的事件来驱动积分的重置
    - 事件驱动重置积分时，更新 CreditAccount 的 resetAt 为 currentPeriodStart/purchaseDate，以免重复执行
  - 在没有续期事件的月份，用定时任务来重置积分
    - 用锚点时间来推算每个月的准确的重置时间，执行定时任务时，更新 CreditAccount 的 resetAt 为推算出的重置时间，以免重复执行
- 对于免费的情况，用定时任务来重置积分
  - 用锚点时间来推算每个月的准确的重置时间，执行定时任务时，检查 productTier + resetAt 组合判断是否需要重置，避免重复执行
- 对于需要自己根据锚点时间推算重置时间的情况，推算的逻辑是：尽量用每个月的同一天，如果这个月没有这一天，则取最接近的那一天。例如：
  - 如果锚点时间是 2024-01-02T00:15:15，则 2 月底的重置时间是 2024-02-02T00:15:15
  - 如果锚点时间是 2024-01-30T00:15:15，则 2 月底的重置时间是 2024-02-28T00:15:15（或闰年的 2024-02-29T00:15:15），3 月份的重置时间是 2024-03-30T00:15:15
- 用 select by update 查询 CreditAccount 来防止并发更新
- 防重机制：通过比较 productTier 和 resetAt 字段组合来判断是否需要重置，避免重复执行
- 异常处理：
  - 创建订阅时，如果直接扣款失败，则走到 checkout session 的分支
  - 创建订阅时可能会有过渡的 incomplete 状态，Subscription 应该返回 hostedInvoiceUrl 以便用户直接付款
  - 更新订阅时，可能会有过渡的 incomplete 状态，或者有 pending_udpates，Subscription 应该返回 hostedInvoiceUrl 以便用户直接付款
  - 总结：
    - 用户实际生效的产品等级以 CreditAccount.productTier 为准，不要以 Subscription.productTier 为准
    - Subscription 可能会有一些中间状态，确保其真正付款之后，才去同步并重置 CreditAccount

## 待解决问题

- alipay 的问题
  - pending_if_incompelte 报错：You cannot set `payment_behavior` to `pending_if_incomplete` when the default payment method is `alipay`.
  - 在支付 invoice 时选 alipay 报错：When confirming a PaymentIntent with a `alipay` PaymentMethod and `setup_future_usage`, `mandate_data` is required.

- 定时任务的实现和测试
  - 需要锁机制以避免重复执行
  - 需要一种模拟时钟的方式来快速推进一个月

- space 中的 subscription 平滑迁移
- 处理苹果内购
- 迁移 Stripe 新加坡
- 解决涨价问题

## 聚合职责分离
- **Space**: 租户边界标识
- **Subscription**: 商业订阅关系管理（仅付费用户）
- **CreditAccount**: 积分余额管理、消耗控制、重置调度（所有用户）
- **CreditTransaction**: 积分变动记录和审计追踪

## YAGNI 原则 (You Aren't Gonna Need It)
- **只实现当前确实需要的功能** - 不为假想的未来需求过度设计
- **最小可行设计** - 从最简单的实现开始，根据实际需求演进
- **避免过早优化** - 先保证功能正确，再考虑性能和扩展性
- **删除未使用的代码** - 定期清理预留但未使用的字段和方法

## 不同级别的月度积分

```typescript
const TIER_CREDITS = {
  FREE: 2000,      // 免费用户：2,000 积分/月
  PRO: 20000,      // 专业版：20,000 积分/月
  MAX: 200000      // 旗舰版：200,000 积分/月
};
```

## 文档设计原则
- **图形为主**：设计文档以 mermaid 图形为核心表达方式，清晰呈现系统架构和交互流程
- **代码最小化**：避免冗长的代码示例，仅在必要时提供关键片段说明设计意图
- **类图简化**：对于创建对象的静态 create 方法，在类图中不使用对象参数模式，在实际实现中使用对象参数模式
- **访问器省略**：在类图和时序图中省略简单的 getter/setter 访问，保留核心业务判断方法
- **专注核心**：突出核心业务逻辑和关键决策点，避免实现细节干扰设计理解

---

# 领域层设计

## 领域模型

### 核心聚合关系图

```mermaid
classDiagram
    class Space["Space 工作空间"] {
        +id: string // 工作空间唯一标识
        +creatorId: string // 创建者用户ID
        +createdAt: Date // 创建时间
        +create(creatorId: string)$ Space // 静态工厂方法创建工作空间
    }

    class Subscription["Subscription 订阅聚合"] {
        +id: string // 订阅唯一标识
        +spaceId: string // 所属工作空间ID
        +createdAt: Date // 订阅创建时间
        -productTier: ProductTier // 产品等级：专业旗舰（不包含免费）
        -billingInterval: BillingInterval // 计费周期：月年
        -status: SubscriptionStatus // 订阅状态
        -renewCycleAnchor: Date // 续期周期锚点
        -currentPeriodStart: Date // 当前周期开始时间
        -currentPeriodEnd: Date // 当前周期结束时间
        -cancelAtPeriodEnd: boolean // 是否在周期结束时取消
        -renewChange?: RenewChange // 续期时的变更计划
        -provider: SubscriptionProvider // 订阅服务提供商：Stripe或Apple
        -metadata?: SubscriptionMetadata // 外部系统元数据
        -updatedAt: Date // 更新时间
        +createFromStripe(spaceId: string, stripeData: object)$ Subscription // 静态方法从Stripe订阅创建
        +createFromAppleTransaction(spaceId: string, transactionInfo: object)$ Subscription // 静态方法从Apple交易创建
        +syncFromStripe(stripeData: object): void // 从Stripe同步状态变化
        +syncFromAppleTransaction(transactionInfo: object, renewalInfo: object): void // 从Apple同步状态变化
        +scheduleCancel(): void // 计划取消订阅（在周期结束时生效）
        +isActive(): boolean // 检查订阅是否活跃
    }

    class CreditAccount["CreditAccount 积分账户聚合"] {
        +id: string // 积分账户唯一标识
        +spaceId: string // 所属工作空间ID：业务主键，永久关联
        +createdAt: Date // 创建时间
        -monthlyBalance: number // 月度积分余额
        -productTier: string // 当前权益等级：free/pro/max
        -refreshCycleAnchor: Date // 刷新周期锚点时间
        -currentPeriodStart: Date // 当前积分周期开始时间
        -currentPeriodEnd: Date // 当前积分周期结束时间
        -updatedAt: Date // 更新时间
        +createForFreeUser(spaceId: string)$ CreditAccount, CreditTransaction // 静态方法创建免费用户积分账户，返回账户和初始交易
        +consume(amount: number, reason: string, metadata?: object): CreditTransaction // 消耗积分并返回交易记录
        +refresh(): CreditTransaction[] // 自动推算的刷新（定时任务用）
        +refresh(periodStart: Date, periodEnd: Date): CreditTransaction[] // 指定周期的刷新（事件驱动用）
        +reset(productTier: string, refreshCycleAnchor: Date, periodStart: Date, periodEnd: Date): CreditTransaction[] // 重置（升级、anchor变化时用）
        +hasSufficientBalance(amount: number): boolean // 检查积分余额是否充足
        +getBalance(): number // 获取当前积分余额
    }

    class CreditTransaction["CreditTransaction 积分交易聚合"] {
        +id: string // 交易记录唯一标识
        +accountId: string // 所属积分账户ID
        +spaceId: string // 所属工作空间ID
        +type: TransactionType // 交易类型：发放消耗废弃
        +amount: number // 变动数量：正数增加负数消耗
        +balanceBefore: number // 交易前余额
        +balanceAfter: number // 交易后余额
        +reason: string // 交易原因说明
        +metadata?: object // 交易详细信息：Token使用等
        +createdAt: Date // 交易创建时间
        +grant(params: object)$ CreditTransaction // 静态方法创建积分发放交易
        +consume(params: object)$ CreditTransaction // 静态方法创建积分消耗交易
        +forfeit(params: object)$ CreditTransaction // 静态方法创建积分废弃交易
    }

    class ProductTier["ProductTier 产品等级"] {
        <<enumeration>>
        FREE // 免费版：CreditAccount使用，Subscription不使用
        PRO // 专业版
        MAX // 旗舰版
    }

    class BillingInterval["BillingInterval 计费周期"] {
        <<enumeration>>
        MONTHLY // 月度计费
        YEARLY // 年度计费
    }

    class SubscriptionStatus["SubscriptionStatus 订阅状态"] {
        <<enumeration>>
        ACTIVE // 活跃
        CANCELED // 已取消
        PAST_DUE // 逾期
        INCOMPLETE // 未完成
        INCOMPLETE_EXPIRED // 未完成已过期
    }

    class SubscriptionProvider["SubscriptionProvider 订阅提供商"] {
        <<enumeration>>
        STRIPE // Stripe支付
        APPLE // 苹果内购
    }

    class TransactionType["TransactionType 交易类型"] {
        <<enumeration>>
        GRANT // 积分发放
        CONSUME // 积分消耗
        FORFEIT // 积分废弃
    }

    class RenewChange["RenewChange 续期变更"] {
        +productTier?: ProductTier // 变更后的产品等级
        +billingInterval?: BillingInterval // 变更后的计费周期
    }


    Space *-- Subscription : "1-0..n：付费用户才有订阅"
    Space *-- CreditAccount : "1-1：每个空间都有积分账户"
    Space o-- CreditTransaction : "1-n：通过spaceId关联"
    CreditAccount o-- CreditTransaction : "1-n：通过accountId关联"

    Subscription --> ProductTier : "仅 PRO, MAX"
    Subscription --> BillingInterval
    Subscription --> SubscriptionStatus
    Subscription --> SubscriptionProvider
    CreditAccount --> ProductTier : "free, pro, max (字符串)"
    CreditTransaction --> TransactionType
    Subscription --> RenewChange : "可选的续期变更计划"
```

### 核心实现模式

#### 领域实体访问控制
- **公开只读属性**：使用 `public readonly` 访问模式
- **私有可变属性**：使用 `private _xxx` + getter + 业务方法模式
- **原生 getter 语法**：`get balance(): number { return this._monthlyBalance; }`

#### 防重机制设计
- **自动刷新防重**：推算时间 > `currentPeriodEnd`
- **指定周期刷新防重**：`periodStart > currentPeriodEnd`
- **重置防重**：新锚点或新等级不同于当前值
- **并发控制**：使用 `SELECT FOR UPDATE` 锁定账户

#### 业务方法模式
```typescript
// 自动推算的刷新（定时任务用）
refresh(): CreditTransaction[]

// 指定周期的刷新（事件驱动用）
refresh(periodStart: Date, periodEnd: Date): CreditTransaction[]

// 重置（升级、anchor变化时用）
reset(productTier: string, refreshCycleAnchor: Date, periodStart: Date, periodEnd: Date): CreditTransaction[]
```

## 应用层设计

### 分层架构设计

```
┌─────────────────────────┐
│   Controller 层         │ ← HTTP 请求处理
├─────────────────────────┤
│   应用服务层             │ ← 事务协调、持久化、外部API
│   (CommandHandler)      │   跨聚合业务逻辑
├─────────────────────────┤
│   聚合层                │ ← 单聚合业务逻辑
│   (Subscription/Credit)  │
├─────────────────────────┤
│   Repository 层         │ ← 数据持久化
└─────────────────────────┘
```

### 核心业务流程

#### 事件驱动积分处理
1. 接收 Stripe/Apple 订阅事件（续期、升级、降级）
2. 使用 `getBySpaceIdForUpdate` 锁定 CreditAccount
3. 根据事件类型调用：
   - 续期：`refresh(periodStart, periodEnd)` 
   - 升级/降级：`reset(newTier, newAnchor, periodStart, periodEnd)`
4. 内部防重检查避免重复操作
5. 发布领域事件

#### 定时任务积分刷新
1. 查询需要刷新的账户：`WHERE current_period_end <= NOW()`
2. 调用 `refresh()` 自动推算新周期并刷新积分
3. 内部防重检查确保幂等性

## 领域事件

基于 YAGNI 原则，只定义当前必需的核心事件：

- **SubscriptionUpgradedEvent**：订阅升级/降级事件
- **CreditsRefreshedEvent**：积分正常刷新事件
- **CreditCycleResetEvent**：积分周期重置事件  
- **CreditsConsumedEvent**：积分消耗事件

## 核心设计特点

### 职责分离清晰
- **Space**: 租户边界标识，不含业务逻辑
- **Subscription**: 付费订阅管理和外部系统集成
- **CreditAccount**: 积分余额管理、消耗控制、重置调度
- **CommandHandler**: 跨聚合业务流程协调

### 防重机制完善
- **周期边界检查**：通过 `currentPeriodEnd` 判断是否需要刷新
- **锚点变化检查**：通过 `refreshCycleAnchor` 判断是否需要重置
- **SELECT FOR UPDATE**：数据库锁机制防止并发冲突
- **事务原子性**：积分刷新/重置操作在单个事务中完成

### YAGNI 原则贯彻
- **最小化字段**：只保留当前业务确实需要的属性
- **简化方法**：去除未使用的复杂逻辑
- **精简事件**：只定义核心必需的领域事件

## 积分不足拦截机制

### 设计目标

在业务处理前进行积分余额预检查，提早发现积分不足情况，避免进入复杂业务逻辑后才发现问题。

### 装饰器设计

```typescript
@CheckCredits() // 无参数装饰器，检查余额 > 0
async handleRequest() {
  // 业务逻辑
}
```

### 核心流程

1. **获取 spaceId**：
   - 先从 CLS 中读取缓存的 spaceId
   - 如果不存在，通过 userId 从 SpaceRepository 查询
   - 查询后缓存到 CLS 中供后续使用

2. **检查积分余额**：
   - 通过 spaceId 查询 CreditAccount
   - 检查 `monthlyBalance > 0`
   - 余额不足时抛出 `InsufficientCreditsException`

3. **异常处理策略**：
   - 所有异常直接向上抛出，不做包装转换
   - 包括：CLS 无 userId、数据库查询失败等

### 实现特点

- **轻量级检查**：只检查余额是否大于0，不做精确积分计算
- **缓存优化**：同一请求中缓存 spaceId，避免重复查询
- **早期拦截**：在事务开启前检查，避免无意义的资源消耗
- **简单异常**：异常信息包含当前余额和产品等级，便于前端处理

### 装饰器组合

```typescript
@CheckCredits()    // 余额预检查
@Transactional()   // 事务管理
async processRequest() {
  // 业务逻辑
}
```

执行顺序：余额检查 → 事务开启 → 业务处理

---

# Controller 接口设计

## API 接口概览

基于业务场景和测试用例，实际实现的核心接口按 Controller 分类：

### SubscriptionController - 订阅管理

| 接口 | 功能 | 用户类型 | 变更方式 | 路径 |
|------|------|----------|----------|------|
| `findSubscription` | 查询当前用户订阅信息 | 所有用户 | 查询接口 | `POST /api/v1/subscription/findSubscription` |
| `createSubscription` | 创建付费订阅（免费转付费） | 免费用户 | 主动订阅 | `POST /api/v1/subscription/createSubscription` |
| `updateSubscription` | 变更付费订阅配置（升级/降级/周期变更） | 付费用户 | 主动变更 | `POST /api/v1/subscription/updateSubscription` |
| `cancelSubscription` | 取消付费订阅 | 付费用户 | 主动取消 | `POST /api/v1/subscription/cancelSubscription` |
| `createBillingPortalSession` | 创建 Stripe Billing Portal 会话 | 付费用户 | 外部管理 | `POST /api/v1/subscription/createBillingPortalSession` |
| `verifyTransaction` | 验证 Apple 内购交易 | Apple 用户 | 主动验证 | `POST /api/v1/subscription/verifyTransaction` |
| `handleStripeWebhook` | 处理 Stripe Webhook 事件 | 系统接口 | 事件驱动 | `POST /webhook/v1/stripe` |
| `handleAppleNotification` | 处理 Apple App Store 通知 | 系统接口 | 事件驱动 | `POST /webhook/v1/apple` |
| `handleAppleSandboxNotification` | 处理 Apple 沙盒通知 | 系统接口 | 事件驱动 | `POST /webhook/v1/apple-sandbox` |
| `deleteSubscriptionForTest` | 删除订阅（测试用） | 开发测试 | 测试清理 | `POST /api/v1/subscription/deleteSubscriptionForTest` |

### CreditController - 积分管理

| 接口 | 功能 | 用户类型 | 变更方式 | 路径 |
|------|------|----------|----------|------|
| `getCreditBalance` | 获取积分余额 | 所有用户 | 查询接口 | `POST /api/v1/credit/getCreditBalance` |
| `listCreditTransactions` | 查询周期内积分交易记录 | 所有用户 | 查询接口 | `POST /api/v1/credit/listCreditTransactions` |

## 接口设计特点

1. **统一 POST 方法**：所有接口都使用 POST 方法，符合项目的 API 设计风格
2. **中文 API 文档**：使用中文的 summary 和 description
3. **统一响应格式**：使用 `@HttpCode(200)` 统一返回 200 状态码
4. **公开路由标记**：Webhook 接口使用 `@PublicRoute()` 装饰器
5. **原始请求体处理**：Stripe webhook 使用 `RawBodyRequest<Request>` 处理签名验证

---

# 实施方案

## 总体策略

采用**渐进式实施 + 测试驱动**的方式，逐个接口实现并验证，确保每个环节的质量和稳定性。

## 实施流程

### 基本实施循环

对每个接口执行以下循环：

1. **按时序图实现接口**
   - 实现领域实体和聚合方法
   - 实现 Repository 层
   - 实现 Command/Query Handler
   - 实现 Controller 层

2. **编写集成测试**
   - 参考现有 E2E 测试模式：`test/iam/*.e2e-spec.ts`
   - 使用 `TestIamModuleHelper` 进行测试环境设置
   - 覆盖正常流程和异常情况

3. **运行测试验证**
   ```bash
   pnpm test:e2e xxx  # 运行对应的E2E测试
   ```

4. **问题排查和修复**
   - **测试不通过时**：
     - 首先分析是测试逻辑问题还是实现问题
     - 如果判断是测试问题，反馈确认
     - 如果是实现问题，修改实现直到测试通过
   - **确保数据一致性**：验证积分余额、交易记录等数据的正确性

5. **进入下一个接口**

## 接口实施顺序

基于依赖关系和复杂度，建议按以下顺序实施：

### 阶段1：基础查询接口（低风险）
1. **`getCreditBalance`** - 积分余额查询
2. **`listCreditTransactions`** - 积分交易记录查询

### 阶段2：订阅管理接口（中风险）
3. **`createSubscription`** - 创建付费订阅
4. **`updateSubscription`** - 订阅变更
5. **`cancelSubscription`** - 订阅取消

### 阶段3：事件处理接口（高风险）
6. **`handleStripeWebhook`** - Stripe 事件处理
7. **`handleAppleNotification`** - Apple 事件处理

## 代码质量保证

### 1. 实现标准
- 遵循现有的代码风格和架构模式
- 使用 TypeScript 严格模式
- 完善的错误处理和日志记录
- 适当的注释和文档

### 2. 审查清单
每个接口实现后检查：
- [ ] 时序图中的每个步骤都有对应实现
- [ ] 领域事件正确发布和处理
- [ ] 数据库事务边界正确设置
- [ ] 异常情况有适当的处理和回滚
- [ ] 关键操作有日志记录
- [ ] API 文档和注释完整

### 3. 性能验证
- 数据库查询使用正确的索引
- 避免 N+1 查询问题
- 合理的分页和限制
- 响应时间在可接受范围内

## 风险控制

### 1. 功能开关
对重大变更使用功能开关：
```typescript
// 渐进式切换到新的订阅逻辑
if (featureFlags.useNewSubscriptionSystem) {
  return newSubscriptionHandler.execute(command);
} else {
  return legacySubscriptionHandler.execute(command);
}
```

### 2. 回滚策略
- 每个阶段实施前创建数据备份点
- 准备回滚脚本和程序
- 监控关键指标，异常时快速回滚

### 3. 监控和告警
- 关键接口的调用量、成功率、响应时间
- 积分系统的数据一致性监控
- 外部服务调用的失败率监控

这个实施方案确保了稳步推进，每一步都有质量保证，同时保持了足够的灵活性来应对实施过程中发现的问题。