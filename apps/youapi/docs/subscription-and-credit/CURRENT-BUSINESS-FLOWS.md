# YouAPI 现有业务流程时序图分析

基于实际代码实现的订阅和积分管理业务用例完整分析。

## 概述

YouAPI 的订阅积分系统采用 CQRS + 事件驱动架构，通过精心设计的时序图展现了复杂业务场景下的清晰流程控制和状态管理。

## 一、SubscriptionController 业务用例分析

### 1. findSubscription - 查询订阅信息

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as SubscriptionController
    participant QueryBus as QueryBus
    participant Handler as FindSubscriptionHandler
    participant Repository as SubscriptionRepository
    participant DtoService as UserDtoService
    participant DB as 数据库

    Client->>Controller: POST /api/v1/subscription/findSubscription
    Controller->>Controller: getSpaceId() from JWT/CLS
    Controller->>QueryBus: execute(FindSubscriptionQuery)
    QueryBus->>Handler: handle(query)
    Handler->>Repository: findBySpaceId(spaceId)
    Repository->>DB: SELECT subscription WHERE space_id = ?
    DB-->>Repository: subscription data or null
    Repository-->>Handler: Subscription entity or undefined
    
    alt Subscription exists
        Handler->>DtoService: toSubscriptionDto(subscription)
        DtoService-->>Handler: SubscriptionDto
        Handler-->>QueryBus: SubscriptionDto
    else No subscription
        Handler-->>QueryBus: undefined
    end
    
    QueryBus-->>Controller: result
    Controller-->>Client: SubscriptionDto | undefined
```

**业务特点**：
- **无状态查询**：纯数据读取，无业务逻辑变更
- **可选返回**：免费用户返回 `undefined`，付费用户返回订阅详情
- **DTO 转换**：通过 UserDtoService 进行安全的数据转换

### 2. createSubscription - 创建付费订阅

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as SubscriptionController
    participant CommandBus as CommandBus
    participant Handler as CreateSubscriptionHandler
    participant UserRepo as UserRepository
    participant SubRepo as SubscriptionRepository
    participant CustRepo as CustomerRepository
    participant CreditRepo as CreditAccountRepository
    participant TxRepo as CreditTransactionRepository
    participant StripeService as StripeService
    participant Stripe as Stripe API

    Client->>Controller: POST /api/v1/subscription/createSubscription
    Note over Client,Controller: DTO: {productTier, billingInterval}
    Controller->>Controller: getUserId() & getSpaceId()
    Controller->>CommandBus: execute(CreateSubscriptionCommand)
    CommandBus->>Handler: @Transactional() execute(command)
    
    Note over Handler: 1. 验证用户状态
    Handler->>UserRepo: getById(userId)
    Handler->>SubRepo: findBySpaceId(spaceId) 
    
    alt Already has subscription
        Handler-->>Controller: ConflictException
    end
    
    Handler->>CreditRepo: getBySpaceId(spaceId)
    
    Note over Handler: 2. 获取或创建 Stripe Customer
    Handler->>CustRepo: findByUserId(userId)
    
    alt No customer exists
        Handler->>StripeService: createCustomer(email, userId, testClockId)
        StripeService->>Stripe: customers.create()
        Stripe-->>StripeService: stripe customer
        Handler->>CustRepo: save(new Customer)
    end
    
    Note over Handler: 3. 获取价格和创建订阅
    Handler->>StripeService: getPriceLookupKey(productTier, billingInterval)
    Handler->>StripeService: getPriceByLookupKey(lookupKey)
    Handler->>StripeService: createSubscription(customerId, priceId, spaceId)
    StripeService->>Stripe: subscriptions.create()
    
    alt Direct subscription creation successful
        Stripe-->>StripeService: stripe subscription
        Note over Handler: 4. 创建本地订阅记录
        Handler->>Handler: Subscription.createFromStripeSubscription()
        Handler->>SubRepo: save(subscription)
        Handler->>Handler: subscription.commit() - 发布领域事件
        
        Note over Handler: 5. 同步积分账户
        Handler->>CreditRepo: findBySpaceIdForUpdate(spaceId)
        Handler->>Handler: creditAccount.syncFromSubscription()
        Handler->>TxRepo: createMany(transactions)
        Handler->>CreditRepo: save(creditAccount)
        Handler->>Handler: creditAccount.commit()
        
        Handler-->>Controller: CreateSubscriptionResponseDto with subscription
    else Payment required
        StripeService->>Stripe: checkout.sessions.create()
        Stripe-->>StripeService: checkout session
        Handler-->>Controller: CreateSubscriptionResponseDto with redirect URL
    end
    
    Controller-->>Client: Response with subscription or redirect
```

**核心业务逻辑**：
- **状态验证**：确保免费用户才能创建付费订阅，防止重复订阅
- **Customer 生命周期**：自动创建和管理 Stripe Customer 关联关系
- **双阶段支付**：优先尝试直接扣款，失败时提供 Checkout Session
- **即时积分同步**：订阅创建成功后立即同步积分账户状态
- **事务一致性**：整个流程在单个事务中保证数据一致性

### 3. updateSubscription - 更新订阅配置

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as SubscriptionController
    participant CommandBus as CommandBus
    participant Handler as UpdateSubscriptionHandler
    participant SubRepo as SubscriptionRepository
    participant UserRepo as UserRepository
    participant StripeService as StripeService
    participant SubService as SubscriptionService
    participant Stripe as Stripe API

    Client->>Controller: POST /api/v1/subscription/updateSubscription
    Note over Client,Controller: DTO: {productTier, billingInterval}
    Controller->>CommandBus: execute(UpdateSubscriptionCommand)
    CommandBus->>Handler: execute(command)
    
    Note over Handler: 1. 获取当前状态
    Handler->>SubRepo: getBySpaceId(spaceId)
    Handler->>UserRepo: getById(userId)
    Handler->>StripeService: getSubscription(stripeSubscriptionId)
    StripeService->>Stripe: subscriptions.retrieve()
    
    Note over Handler: 2. 解析当前产品配置
    Handler->>Handler: Subscription.parseFromPriceKey()
    
    alt 目标配置与当前相同
        alt Has scheduled changes
            Handler->>StripeService: releaseSubscriptionSchedule()
            Handler->>SubService: syncFromStripeSubscription()
        else Has cancel_at_period_end
            Handler->>StripeService: doNotCancelAtPeriodEnd()
            Handler->>SubService: syncFromStripeSubscription()
        else No changes needed
            Handler-->>Controller: Current subscription info
        end
    else 配置需要变更
        Handler->>StripeService: getPriceLookupKey() & getPriceByLookupKey()
        
        alt 升级 (PRO->MAX)
            Note over Handler: 立即生效，重置计费周期
            alt Has schedule
                Handler->>StripeService: releaseSubscriptionSchedule()
            end
            Handler->>StripeService: upgrade(subscriptionId, newPriceId)
            StripeService->>Stripe: subscriptions.update() with proration
            Handler->>SubService: syncFromStripeSubscription()
            Note over SubService: 同步积分账户，重置积分周期
        else 降级或周期变更
            Note over Handler: 延期生效，不影响当前周期
            alt Has cancel_at_period_end
                Handler->>StripeService: doNotCancelAtPeriodEnd()
            end
            Handler->>StripeService: scheduleUpdateSubscriptionAtPeriodEnd()
            StripeService->>Stripe: subscription_schedules.create()
            Handler->>SubService: syncFromStripeSubscription()
            Note over SubService: 更新 renewChange 字段，积分账户不变
        end
    end
    
    Handler-->>Controller: UpdateSubscriptionResponseDto
    Controller-->>Client: Response with updated subscription
```

**升级降级策略**：
- **升级策略**：立即生效并重置计费周期锚点，用户立即获得新等级积分
- **降级策略**：延期到当前周期结束生效，使用 Stripe Schedule API
- **状态清理**：智能处理现有的 schedule 和 cancel_at_period_end 冲突
- **积分同步时机**：升级立即同步，降级在实际生效时同步

### 4. cancelSubscription - 取消订阅

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as SubscriptionController
    participant CommandBus as CommandBus
    participant Handler as CancelSubscriptionHandler
    participant SubRepo as SubscriptionRepository
    participant StripeService as StripeService
    participant Stripe as Stripe API

    Client->>Controller: POST /api/v1/subscription/cancelSubscription
    Controller->>CommandBus: execute(CancelSubscriptionCommand)
    CommandBus->>Handler: execute(command)
    
    Handler->>SubRepo: getBySpaceId(spaceId)
    Handler->>Handler: subscription.getStripeSubscriptionId()
    Handler->>StripeService: cancelAtPeriodEnd(subscriptionId)
    StripeService->>Stripe: subscriptions.update({cancel_at_period_end: true})
    Stripe-->>StripeService: updated subscription
    
    Handler->>Handler: subscription.scheduleCancel()
    Note over Handler: 更新 cancelAtPeriodEnd = true, 发布事件
    Handler->>SubRepo: save(subscription)
    Handler->>Handler: subscription.commit()
    
    Handler-->>Controller: SubscriptionDto
    Controller-->>Client: Canceled subscription info
```

**取消策略**：
- **延期取消**：设置在当前周期结束时取消，用户继续享受服务到期
- **状态同步**：同时更新 Stripe 和本地状态保持一致性
- **用户友好**：允许用户在周期结束前继续使用已付费服务

### 5. handleStripeWebhook - 处理 Stripe Webhook

```mermaid
sequenceDiagram
    participant Stripe as Stripe
    participant Controller as SubscriptionController
    participant EventBus as EventBus
    participant EventHandler as StripeEventHandler
    participant SubService as SubscriptionService
    participant SubRepo as SubscriptionRepository
    participant CreditRepo as CreditAccountRepository
    participant TxRepo as CreditTransactionRepository

    Stripe->>Controller: POST /webhook/v1/stripe
    Note over Stripe,Controller: Raw body + stripe-signature header
    
    Controller->>Controller: Verify webhook signature
    alt Signature invalid
        Controller-->>Stripe: 400 BadRequestException
    end
    
    Controller->>EventBus: publish(StripeEvent)
    Note over Controller: 异步处理，立即返回200
    Controller-->>Stripe: 200 OK
    
    EventBus->>EventHandler: handle(StripeEvent)
    
    alt customer.subscription.* events
        EventHandler->>StripeService: getSubscription(subscriptionId)
        EventHandler->>SubService: syncFromStripeSubscription()
        Note over SubService: @Transactional 同步订阅和积分账户
        
        SubService->>SubRepo: findBySpaceIdForUpdate()
        
        alt Subscription canceled/expired
            SubService->>SubRepo: deleteById()
            SubService->>CreditRepo: getBySpaceIdForUpdate()
            SubService->>SubService: creditAccount.resetToFree()
            SubService->>TxRepo: createMany(transactions)
            SubService->>CreditRepo: save(creditAccount)
        else Subscription updated
            SubService->>SubService: subscription.syncFromStripeSubscription()
            SubService->>SubRepo: save(subscription)
            SubService->>CreditRepo: getBySpaceIdForUpdate()
            SubService->>SubService: creditAccount.syncFromSubscription()
            SubService->>TxRepo: createMany(transactions)
            SubService->>CreditRepo: save(creditAccount)
        end
        
    else test_helpers.test_clock.ready
        EventHandler->>CreditRepo: findByTestClockIdForUpdate()
        EventHandler->>EventHandler: creditAccount.refreshIfNeeded()
        EventHandler->>TxRepo: createMany(transactions)
        EventHandler->>CreditRepo: save(creditAccount)
        
    else customer.deleted
        EventHandler->>SubRepo: deleteById(customerId)
    end
```

**Webhook 处理模式**：
- **异步响应**：立即返回 200 状态码，避免 Stripe 重试机制
- **签名验证**：确保请求来源的安全性和完整性
- **事件分类处理**：不同事件类型采用专门的处理逻辑
- **状态同步策略**：订阅状态变更时自动同步积分账户

### 6. verifyTransaction - 验证 Apple 内购交易

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as SubscriptionController
    participant CommandBus as CommandBus
    participant Handler as VerifyTransactionHandler
    participant AppleService as Apple App Store API
    participant SubRepo as SubscriptionRepository
    participant CreditRepo as CreditAccountRepository

    Client->>Controller: POST /api/v1/subscription/verifyTransaction
    Note over Client,Controller: {transactionId, environment}
    Controller->>CommandBus: execute(VerifyTransactionCommand)
    CommandBus->>Handler: execute(command)
    
    Handler->>AppleService: verifyTransaction(transactionId, environment)
    AppleService-->>Handler: transactionInfo & renewalInfo
    
    Handler->>Handler: Validate transaction belongs to user
    alt Invalid transaction
        Handler-->>Controller: BadRequestException
    end
    
    Handler->>SubRepo: findBySpaceIdForUpdate(spaceId)
    
    alt No existing subscription
        Handler->>Handler: Subscription.createFromAppleTransaction()
        Handler->>SubRepo: save(subscription)
    else Existing subscription
        Handler->>Handler: subscription.syncFromAppleTransaction()
        Handler->>SubRepo: save(subscription)
    end
    
    Handler->>CreditRepo: getBySpaceIdForUpdate(spaceId)
    Handler->>Handler: creditAccount.syncFromSubscription()
    Handler->>CreditRepo: save(creditAccount)
    
    Handler-->>Controller: UserWithPreferenceSpaceDto
    Controller-->>Client: Updated user info
```

**Apple 集成特点**：
- **交易验证**：通过 Apple App Store Server API 验证交易真实性
- **用户归属验证**：通过 appAccountToken 确保交易属于当前用户
- **订阅生命周期**：支持创建新订阅或更新现有订阅状态
- **即时积分同步**：验证成功后立即同步积分账户状态

## 二、CreditController 业务用例分析

### 1. getCreditBalance - 获取积分余额

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as CreditController
    participant QueryBus as QueryBus
    participant Handler as GetCreditBalanceHandler
    participant Repository as CreditAccountRepository
    participant DtoService as UserDtoService

    Client->>Controller: POST /api/v1/credit/getCreditBalance
    Controller->>Controller: getSpaceId()
    Controller->>QueryBus: execute(GetCreditBalanceQuery)
    QueryBus->>Handler: execute(query)
    
    Handler->>Repository: getBySpaceId(spaceId)
    Repository-->>Handler: CreditAccount entity
    Handler->>DtoService: toCreditAccountDto(creditAccount)
    DtoService-->>Handler: CreditAccountDto
    
    Handler-->>QueryBus: CreditAccountDto
    QueryBus-->>Controller: result
    Controller-->>Client: CreditAccountDto
```

**查询特点**：
- **无副作用操作**：纯数据读取，不修改任何状态
- **必定存在数据**：所有用户（包括免费用户）都有积分账户
- **实时余额查询**：返回当前准确的积分余额和周期信息

### 2. listCreditTransactions - 查询积分交易记录

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as CreditController
    participant QueryBus as QueryBus
    participant Handler as ListCreditTransactionsHandler
    participant Repository as CreditTransactionRepository

    Client->>Controller: POST /api/v1/credit/listCreditTransactions
    Note over Client,Controller: {transactionType?, limit?, offset?}
    Controller->>Controller: getSpaceId()
    Controller->>QueryBus: execute(ListCreditTransactionsQuery)
    QueryBus->>Handler: execute(query)
    
    Handler->>Repository: findBySpaceIdInCurrentPeriod(params)
    Note over Repository: WHERE space_id = ? AND created_at >= currentPeriodStart
    Repository-->>Handler: CreditTransaction[]
    
    Handler-->>QueryBus: ListCreditTransactionsDto
    QueryBus-->>Controller: result
    Controller-->>Client: Transaction list with pagination
```

**查询设计**：
- **当前周期限定**：仅显示当前积分周期内的交易记录
- **类型筛选支持**：可按 GRANT/CONSUME/FORFEIT 类型筛选
- **分页查询**：支持 limit/offset 参数控制数据量

### 3. consumeCredits - 消费积分

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as CreditController
    participant CommandBus as CommandBus
    participant Handler as ConsumeCreditsHandler
    participant CheckDecorator as @CheckCredits
    participant Repository as CreditAccountRepository
    participant TxRepository as CreditTransactionRepository

    Client->>Controller: POST /api/v1/credit/consumeCredits
    Note over Client,Controller: {amount: number, reason: string, metadata?}
    Controller->>CommandBus: execute(ConsumeCreditsCommand)
    CommandBus->>Handler: @CheckCredits() @Transactional() execute()
    
    Note over Handler: 1. 预检查积分余额
    Handler->>CheckDecorator: Check balance > 0
    CheckDecorator->>CheckDecorator: getSpaceId from CLS cache
    CheckDecorator->>Repository: getBySpaceId(spaceId)
    alt Insufficient credits
        CheckDecorator-->>Handler: InsufficientCreditsException
    end
    
    Note over Handler: 2. 事务处理
    Handler->>Repository: getBySpaceIdForUpdate(spaceId)
    Note over Repository: SELECT FOR UPDATE 防并发
    Handler->>Handler: creditAccount.consume(amount, reason, metadata)
    Note over Handler: 聚合内业务逻辑：扣减余额，创建交易记录，发布事件
    
    Handler->>Repository: save(creditAccount)
    Handler->>TxRepository: save(consumeTransaction)
    Handler->>Handler: creditAccount.commit() & transaction.commit()
    
    Handler-->>Controller: ConsumeCreditsResponseDto
    Controller-->>Client: Updated account + transaction record
```

**积分消费机制**：
- **预检查装饰器**：事务前轻量级检查，避免无效的资源消耗
- **并发控制**：SELECT FOR UPDATE 确保积分扣减的原子性
- **聚合封装**：业务逻辑完全封装在 CreditAccount 聚合内部
- **审计追踪**：每次消费都创建不可变的交易记录

## 三、跨聚合业务流程模式

### 1. 订阅积分同步模式

```mermaid
graph TD
    A[Subscription聚合变更] -->|同步触发| B[CreditAccount聚合同步]
    C[外部事件] -->|事件驱动| A
    A -->|发布领域事件| D[EventBus]
    D -->|异步处理| E[其他业务模块]
    
    F[SubscriptionService] -->|跨聚合协调| A
    F -->|跨聚合协调| B
    G["@Transactional"] -->|事务边界| F
```

**同步策略**：
- **即时同步**：订阅状态变更立即触发积分账户同步
- **服务层协调**：SubscriptionService 负责跨聚合的状态协调
- **事务一致性**：整个同步过程在单个事务中完成
- **事件解耦**：通过领域事件实现模块间的松耦合

### 2. 外部系统集成模式

```mermaid
sequenceDiagram
    participant External as 外部系统
    participant Webhook as Webhook Endpoint
    participant EventBus as 内部事件总线
    participant Handler as 事件处理器
    participant Aggregates as 领域聚合

    External->>Webhook: HTTP Notification
    Webhook->>Webhook: 验证签名/权限
    Webhook->>EventBus: 发布内部事件
    Webhook-->>External: 200 OK (立即响应)
    
    EventBus->>Handler: 异步处理事件
    Handler->>Aggregates: 同步状态变更
    Handler->>Handler: 发布领域事件
```

**集成特点**：
- **异步响应**：Webhook 立即返回，避免外部系统超时重试
- **安全验证**：签名验证确保请求来源的可信性
- **内部事件转换**：将外部事件转换为内部领域事件
- **状态最终一致性**：通过异步处理保证最终状态一致

### 3. 并发控制和防重机制

```mermaid
graph TD
    A[请求到达] --> B{检查幂等性}
    B -->|已处理| C[返回已有结果]
    B -->|未处理| D[获取行锁]
    D --> E[检查业务条件]
    E -->|条件满足| F[执行业务逻辑]
    E -->|条件不满足| G[返回错误]
    F --> H[提交事务]
    H --> I[发布事件]
```

**防重策略**：
- **行级锁**：SELECT FOR UPDATE 防止并发修改
- **幂等检查**：业务层面的重复操作检查
- **时间戳防重**：基于时间窗口的防重机制
- **状态机验证**：确保状态转换的合法性

## 四、可复用业务流程模式

### 1. 标准 CQRS 处理模式

```typescript
// Command 处理模式
@CommandHandler(SomeCommand)
export class SomeCommandHandler {
  @Transactional()
  async execute(command: SomeCommand): Promise<SomeResponse> {
    // 1. 获取聚合（加锁）
    const aggregate = await this.repository.getByIdForUpdate(id);
    
    // 2. 执行业务逻辑
    const result = aggregate.performBusinessLogic(command.params);
    
    // 3. 保存状态变更
    await this.repository.save(aggregate);
    
    // 4. 发布事件
    aggregate.commit();
    
    return result;
  }
}
```

### 2. 外部系统同步模式

```typescript
// Webhook 处理模式
@EventsHandler(ExternalSystemEvent)
export class ExternalEventHandler {
  async handle(event: ExternalSystemEvent) {
    // 1. 验证事件来源和内容
    await this.validateEvent(event);
    
    // 2. 获取相关聚合（事务处理）
    const aggregate = await this.repository.getByIdForUpdate(event.entityId);
    
    // 3. 同步外部状态到聚合
    aggregate.syncFromExternalSystem(event.data);
    
    // 4. 触发相关聚合的状态更新
    await this.coordinateRelatedAggregates(aggregate);
    
    // 5. 保存并发布事件
    await this.repository.save(aggregate);
    aggregate.commit();
  }
}
```

### 3. 聚合协调服务模式

```typescript
// 跨聚合协调模式
@Injectable()
export class CoordinationService {
  @Transactional()
  async coordinateAggregates(primaryId: string, change: Change) {
    // 1. 获取主聚合（加锁）
    const primary = await this.primaryRepo.getByIdForUpdate(primaryId);
    
    // 2. 获取相关聚合（加锁）
    const related = await this.relatedRepo.getByIdForUpdate(relatedId);
    
    // 3. 执行协调逻辑
    const primaryResult = primary.processChange(change);
    const relatedResult = related.syncWithPrimary(primaryResult);
    
    // 4. 批量保存
    await Promise.all([
      this.primaryRepo.save(primary),
      this.relatedRepo.save(related)
    ]);
    
    // 5. 发布协调事件
    primary.commit();
    related.commit();
    
    return { primaryResult, relatedResult };
  }
}
```

## 总结

YouAPI 的业务流程设计展现了成熟的企业级架构特征：

### 优势特点
1. **清晰的职责分离**：Controller-Service-Repository 各层职责明确
2. **完善的并发控制**：通过数据库锁和业务防重确保数据一致性  
3. **优雅的外部集成**：异步 Webhook 处理和状态同步机制
4. **强大的事件驱动**：完整的领域事件体系支持模块解耦

### 可复用模式
1. **标准 CQRS 模式**：Command/Query 分离和统一的处理流程
2. **聚合协调模式**：跨聚合的事务一致性保证机制
3. **外部系统集成模式**：安全可靠的 Webhook 处理框架
4. **并发控制模式**：多层次的并发安全保护机制

这些业务流程模式为 Apple 内购集成提供了坚实的架构基础和可参考的实现模式。