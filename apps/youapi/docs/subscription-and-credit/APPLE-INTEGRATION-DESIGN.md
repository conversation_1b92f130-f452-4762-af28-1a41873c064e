# Apple 内购集成技术设计文档

基于现有 Stripe 集成架构的 Apple 内购完整集成方案。

## 概述

本文档基于 `CURRENT-ARCHITECTURE-ANALYSIS.md` 和 `CURRENT-BUSINESS-FLOWS.md` 的架构分析结果，设计 Apple 内购与现有订阅积分系统的集成方案。采用与 Stripe 对等的技术架构，确保多平台订阅管理的一致性和可维护性。

## 设计目标

1. **架构一致性**：与现有 Stripe 集成保持相同的设计模式和代码结构
2. **业务对等性**：Apple 订阅功能与 Stripe 订阅功能完全对等
3. **数据一致性**：确保跨平台订阅状态和积分账户的最终一致性
4. **可扩展性**：为未来可能的其他支付平台预留扩展空间

## 核心设计原则

### 1. 复用现有架构模式
- **聚合根设计**：复用 `Subscription` 和 `CreditAccount` 聚合
- **CQRS 模式**：复用现有 Command/Query Handler 架构
- **事件驱动**：复用现有领域事件和处理机制
- **事务管理**：复用现有 `@Transactional()` 装饰器

### 2. 对等性设计原则
- **API 对等**：Apple 相关接口与 Stripe 接口功能对等
- **状态映射对等**：Apple 订阅状态映射逻辑与 Stripe 保持一致
- **事件处理对等**：Apple 通知处理逻辑与 Stripe Webhook 处理对等
- **积分同步对等**：Apple 订阅触发的积分同步与 Stripe 保持一致

---

# 一、Apple 订阅状态映射设计

## Apple 订阅状态分析

基于 Apple App Store Server API 的交易和续期信息，设计状态映射方案：

### Apple 原生状态定义

```typescript
// Apple 交易状态 (JWSTransactionDecodedPayload.transactionReason)
enum AppleTransactionReason {
  PURCHASE = "PURCHASE",           // 首次购买
  RENEWAL = "RENEWAL",             // 自动续期
  UPGRADE = "UPGRADE",             // 升级
  DOWNGRADE = "DOWNGRADE",         // 降级
  REFUND = "REFUND",               // 退款
}

// Apple 续期状态 (JWSRenewalInfoDecodedPayload.autoRenewStatus)  
enum AppleAutoRenewStatus {
  ON = 1,     // 自动续期开启
  OFF = 0,    // 自动续期关闭
}

// Apple 订阅状态 (JWSRenewalInfoDecodedPayload.expirationIntent)
enum AppleExpirationIntent {
  CANCELLED = 1,           // 用户取消
  BILLING_ERROR = 2,       // 计费错误
  DECLINED_PRICE_INCREASE = 3,  // 拒绝价格上涨
  PRODUCT_UNAVAILABLE = 4, // 产品不可用
  UNKNOWN = 5,             // 未知原因
}
```

## 状态映射规则设计

### 核心映射逻辑

基于现有 Stripe 状态映射模式，设计 Apple 到系统状态的映射：

```mermaid
stateDiagram-v2
    [*] --> APPLE_ACTIVE: 首次购买/续期成功
    [*] --> APPLE_EXPIRED: 交易过期
    
    APPLE_ACTIVE --> SYSTEM_ACTIVE: 映射规则1
    APPLE_EXPIRED --> SYSTEM_PAST_DUE: 映射规则2
    APPLE_REFUNDED --> SYSTEM_DELETED: 映射规则3
    
    SYSTEM_ACTIVE --> CreditSync: 触发积分同步
    SYSTEM_PAST_DUE --> CreditReset: 重置为免费
    SYSTEM_DELETED --> CreditReset: 重置为免费
```

### 详细映射表

| Apple 状态组合 | 系统状态 | 积分处理 | 说明 |
|----------------|----------|----------|------|
| 交易有效 + 自动续期开启 | `ACTIVE` | 立即同步 | 正常订阅状态 |
| 交易有效 + 自动续期关闭 | `ACTIVE` | 立即同步 | 订阅即将到期但当前有效 |
| 交易过期 + 计费错误 | `PAST_DUE` | 保持现状 | 类似 Stripe 的 past_due |
| 交易过期 + 用户取消 | 删除订阅 | 重置为免费 | 类似 Stripe 的取消流程 |
| 退款交易 | 删除订阅 | 重置为免费 | 立即生效 |

### 实现方案

```typescript
class AppleSubscriptionStateMapper {
  /**
   * 将 Apple 交易和续期信息映射为系统订阅状态
   * 参考 Stripe 的状态映射逻辑
   */
  static mapToSystemStatus(
    transactionInfo: JWSTransactionDecodedPayload,
    renewalInfo?: JWSRenewalInfoDecodedPayload
  ): SubscriptionStatus | 'DELETE' {
    const now = Date.now();
    const expiresDate = transactionInfo.expiresDate;
    
    // 退款处理 - 立即删除
    if (transactionInfo.transactionReason === 'REFUND') {
      return 'DELETE';
    }
    
    // 交易仍有效
    if (expiresDate && expiresDate > now) {
      return SubscriptionStatus.ACTIVE;
    }
    
    // 交易已过期
    if (!renewalInfo) {
      return 'DELETE'; // 无续期信息，删除订阅
    }
    
    // 根据过期原因决定状态
    switch (renewalInfo.expirationIntent) {
      case AppleExpirationIntent.BILLING_ERROR:
        return SubscriptionStatus.PAST_DUE; // 计费问题，类似 Stripe
      case AppleExpirationIntent.CANCELLED:
      default:
        return 'DELETE'; // 其他原因都删除订阅
    }
  }
}
```

---

# 二、Apple 通知处理架构设计

## 设计目标

基于现有 Stripe Webhook 处理架构，设计对等的 Apple 通知处理机制。

## 通知处理流程设计

### 整体架构对比

```mermaid
graph TD
    subgraph "Stripe 架构 (现有)"
        SW[Stripe Webhook] --> SE[StripeEvent]
        SE --> SEH[StripeEventHandler]
        SEH --> SS[SubscriptionService]
    end
    
    subgraph "Apple 架构 (设计)"
        AN[Apple Notification] --> AE[AppleEvent] 
        AE --> AEH[AppleEventHandler]
        AEH --> AS[SubscriptionService]
    end
    
    subgraph "共享服务层"
        SS --> SA[Subscription Aggregate]
        AS --> SA
        SA --> CA[CreditAccount Aggregate]
    end
```

### Apple 通知处理流程

```mermaid
sequenceDiagram
    participant Apple as Apple App Store
    participant Controller as SubscriptionController  
    participant EventBus as EventBus
    participant Handler as AppleEventHandler
    participant SubService as SubscriptionService
    participant Mapper as StateMapper
    participant SubRepo as SubscriptionRepository
    participant CreditRepo as CreditAccountRepository
    
    Apple->>Controller: POST /webhook/v1/apple
    Note over Apple,Controller: signedPayload + 环境信息
    
    Controller->>Controller: 验证 Apple 签名
    alt 签名无效
        Controller-->>Apple: 400 BadRequestException
    end
    
    Controller->>EventBus: publish(AppleNotificationEvent)
    Note over Controller: 异步处理，立即返回200
    Controller-->>Apple: 200 OK
    
    EventBus->>Handler: handle(AppleNotificationEvent)
    
    alt SUBSCRIBED | DID_RENEW | DID_CHANGE_RENEWAL_STATUS
        Handler->>Mapper: mapToSystemStatus(transactionInfo, renewalInfo)
        
        alt 映射结果为 DELETE
            Handler->>SubRepo: findByAppleTransactionId()
            Handler->>SubRepo: deleteById()
            Handler->>CreditRepo: getBySpaceIdForUpdate()
            Handler->>Handler: creditAccount.resetToFree()
            Handler->>CreditRepo: save(creditAccount)
        else 映射结果为有效状态
            Handler->>SubRepo: findByAppleTransactionId()
            alt 订阅不存在
                Handler->>Handler: Subscription.createFromAppleTransaction()
                Handler->>SubRepo: save(subscription)
            else 订阅存在
                Handler->>Handler: subscription.syncFromAppleTransaction()
                Handler->>SubRepo: save(subscription)
            end
            
            Handler->>CreditRepo: getBySpaceIdForUpdate()
            Handler->>Handler: creditAccount.syncFromSubscription()
            Handler->>CreditRepo: save(creditAccount)
        end
        
    else EXPIRED | REFUND
        Handler->>SubRepo: findByAppleTransactionId()
        Handler->>SubRepo: deleteById()
        Handler->>CreditRepo: getBySpaceIdForUpdate()
        Handler->>Handler: creditAccount.resetToFree()
        Handler->>CreditRepo: save(creditAccount)
    end
```

## 核心组件设计

### 1. AppleEventHandler 实现

```typescript
@EventsHandler(AppleNotificationEvent)
export class AppleEventHandler implements IEventHandler<AppleNotificationEvent> {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly creditAccountRepository: CreditAccountRepository,
  ) {}

  @Transactional()
  async handle(event: AppleNotificationEvent): Promise<void> {
    const { notificationType, data } = event;
    
    switch (notificationType) {
      case 'SUBSCRIBED':
      case 'DID_RENEW':  
      case 'DID_CHANGE_RENEWAL_STATUS':
        await this.handleSubscriptionChange(data);
        break;
        
      case 'EXPIRED':
      case 'REFUND':
        await this.handleSubscriptionTermination(data);
        break;
        
      default:
        this.logger.warn(`Unhandled Apple notification type: ${notificationType}`);
    }
  }

  private async handleSubscriptionChange(data: AppleNotificationData): Promise<void> {
    // 实现类似 Stripe 的订阅变更处理逻辑
    // 复用现有的 SubscriptionService 方法
  }

  private async handleSubscriptionTermination(data: AppleNotificationData): Promise<void> {
    // 实现类似 Stripe 的订阅终止处理逻辑
    // 复用现有的积分重置机制
  }
}
```

### 2. Subscription 聚合扩展

```typescript
export class Subscription extends AggregateRoot {
  // 现有字段和方法...

  /**
   * 从 Apple 交易信息创建订阅 - 对标 createFromStripeSubscription
   */
  static createFromAppleTransaction(
    spaceId: string,
    transactionInfo: JWSTransactionDecodedPayload,
    renewalInfo?: JWSRenewalInfoDecodedPayload
  ): Subscription {
    // 解析 Apple 产品 ID 到系统产品层级
    const { productTier, billingInterval } = this.parseFromAppleProductId(
      transactionInfo.productId
    );
    
    // 映射 Apple 状态到系统状态
    const status = AppleSubscriptionStateMapper.mapToSystemStatus(
      transactionInfo, 
      renewalInfo
    );
    
    if (status === 'DELETE') {
      throw new BadRequestException('Cannot create subscription from invalid Apple transaction');
    }
    
    const subscription = new Subscription({
      id: uuidv7(),
      spaceId,
      createdAt: new Date(transactionInfo.originalPurchaseDate),
      updatedAt: new Date(),
      productTier,
      billingInterval,
      status,
      renewCycleAnchor: new Date(transactionInfo.originalPurchaseDate),
      currentPeriodStart: new Date(transactionInfo.purchaseDate),
      currentPeriodEnd: transactionInfo.expiresDate ? new Date(transactionInfo.expiresDate) : undefined,
      cancelAtPeriodEnd: renewalInfo?.autoRenewStatus === AppleAutoRenewStatus.OFF,
      provider: SubscriptionProvider.APPLE,
      metadata: {
        transactionInfo,
        renewalInfo,
      } as AppleSubscriptionMetadata,
    });
    
    subscription.apply(new SubscriptionCreatedEvent(subscription.id, spaceId, productTier));
    subscription._isNew = true;
    return subscription;
  }

  /**
   * 从 Apple 交易信息同步订阅状态 - 对标 syncFromStripeSubscription
   */
  syncFromAppleTransaction(
    transactionInfo: JWSTransactionDecodedPayload,
    renewalInfo?: JWSRenewalInfoDecodedPayload
  ): void {
    const newStatus = AppleSubscriptionStateMapper.mapToSystemStatus(
      transactionInfo,
      renewalInfo
    );
    
    if (newStatus === 'DELETE') {
      // 这种情况应该通过删除订阅来处理，而不是更新状态
      throw new BadRequestException('Subscription should be deleted, not updated');
    }
    
    // 更新订阅状态和周期信息
    this._status = newStatus;
    this._currentPeriodStart = new Date(transactionInfo.purchaseDate);
    this._currentPeriodEnd = transactionInfo.expiresDate ? new Date(transactionInfo.expiresDate) : undefined;
    this._cancelAtPeriodEnd = renewalInfo?.autoRenewStatus === AppleAutoRenewStatus.OFF;
    this._metadata = {
      transactionInfo,
      renewalInfo,
    } as AppleSubscriptionMetadata;
    this._updatedAt = new Date();
    
    this.apply(new SubscriptionUpdatedEvent(this.id, this.spaceId, this._status));
  }

  /**
   * 解析 Apple 产品 ID 到系统产品配置 - 对标 parseFromPriceKey
   */
  static parseFromAppleProductId(productId: string): {
    productTier: SubscriptionProductTier;
    billingInterval: BillingInterval;
  } {
    // 根据 Apple 产品 ID 映射到系统产品配置
    switch (productId) {
      case AppleProductId.YOUMIND_PRO_MONTHLY:
      case TestAppleProductId.YOUMIND_PRO_MONTHLY:
        return {
          productTier: SubscriptionProductTier.PRO,
          billingInterval: BillingInterval.MONTHLY,
        };
        
      case AppleProductId.YOUMIND_PRO_YEARLY:
      case TestAppleProductId.YOUMIND_PRO_YEARLY:
        return {
          productTier: SubscriptionProductTier.PRO,
          billingInterval: BillingInterval.YEARLY,
        };
        
      case AppleProductId.YOUMIND_MAX_MONTHLY:
      case TestAppleProductId.YOUMIND_MAX_MONTHLY:
        return {
          productTier: SubscriptionProductTier.MAX,
          billingInterval: BillingInterval.MONTHLY,
        };
        
      case AppleProductId.YOUMIND_MAX_YEARLY:
      case TestAppleProductId.YOUMIND_MAX_YEARLY:
        return {
          productTier: SubscriptionProductTier.MAX,
          billingInterval: BillingInterval.YEARLY,
        };
        
      default:
        throw new BadRequestException(`Unknown Apple product ID: ${productId}`);
    }
  }
}
```

---

# 三、积分同步机制设计

## 复用现有机制

基于现有 `CreditAccount.syncFromSubscription()` 方法，Apple 订阅的积分同步无需额外设计，完全复用现有机制。

## 触发时机

与 Stripe 保持完全一致：

1. **订阅创建时**：创建新的积分账户或同步现有账户
2. **订阅更新时**：根据产品等级和周期变化同步积分  
3. **订阅删除时**：重置为免费用户积分

## 防重机制

完全复用现有的防重机制：
- `SELECT FOR UPDATE` 锁定积分账户
- 基于 `productTier + refreshCycleAnchor` 的幂等性检查
- 事务边界保证原子性

---

# 四、多平台订阅冲突处理策略

## 冲突场景分析

用户可能在不同平台同时拥有订阅，需要设计冲突处理策略：

### 场景1：用户同时拥有 Stripe 和 Apple 订阅

```mermaid
graph TD
    A[用户同时拥有两个平台订阅] --> B{比较订阅优先级}
    B -->|Apple 订阅级别更高| C[停用 Stripe 订阅]
    B -->|Stripe 订阅级别更高| D[忽略 Apple 订阅]
    B -->|同级别| E[按时间优先原则]
    C --> F[同步高级别订阅的积分]
    D --> F
    E --> F
```

### 场景2：订阅切换处理

```mermaid
sequenceDiagram
    participant User as 用户
    participant Stripe as Stripe 订阅
    participant Apple as Apple 订阅
    participant System as 系统
    participant Credit as 积分账户
    
    User->>Apple: 创建 Apple 订阅
    Apple->>System: 通知订阅创建
    System->>System: 检查现有 Stripe 订阅
    
    alt Stripe 订阅存在且级别更高
        System->>System: 忽略 Apple 订阅，保持 Stripe
        System-->>Apple: 记录但不生效
    else Apple 订阅级别更高或时间更新
        System->>Stripe: 标记 Stripe 订阅为冲突状态
        System->>Credit: 同步 Apple 订阅积分
        System->>User: 通知订阅变更
    end
```

## 冲突处理策略

### 1. 优先级规则

```typescript
enum SubscriptionPriority {
  MAX_MONTHLY = 4,
  MAX_YEARLY = 3, 
  PRO_MONTHLY = 2,
  PRO_YEARLY = 1,
  FREE = 0,
}

class SubscriptionConflictResolver {
  /**
   * 解决多平台订阅冲突
   */
  static resolveConflict(
    stripeSubscription?: Subscription,
    appleSubscription?: Subscription
  ): Subscription | null {
    if (!stripeSubscription && !appleSubscription) {
      return null;
    }
    
    if (!stripeSubscription) return appleSubscription;
    if (!appleSubscription) return stripeSubscription;
    
    // 比较优先级
    const stripePriority = this.calculatePriority(stripeSubscription);
    const applePriority = this.calculatePriority(appleSubscription);
    
    if (stripePriority > applePriority) {
      return stripeSubscription;
    } else if (applePriority > stripePriority) {
      return appleSubscription;
    } else {
      // 同级别，选择更新的订阅
      return stripeSubscription.updatedAt > appleSubscription.updatedAt 
        ? stripeSubscription 
        : appleSubscription;
    }
  }
  
  private static calculatePriority(subscription: Subscription): number {
    if (subscription.productTier === SubscriptionProductTier.MAX) {
      return subscription.billingInterval === BillingInterval.MONTHLY ? 4 : 3;
    } else {
      return subscription.billingInterval === BillingInterval.MONTHLY ? 2 : 1;
    }
  }
}
```

### 2. 数据库设计扩展

为支持多平台订阅管理，需要对数据库进行小幅扩展：

```sql
-- 在 subscriptions 表中添加冲突状态字段
ALTER TABLE subscriptions ADD COLUMN conflict_status VARCHAR(20) DEFAULT 'active';
-- 可能值：'active', 'conflicted', 'superseded'

-- 确保每个 space 最多只有一个 active 订阅
CREATE UNIQUE INDEX idx_subscriptions_space_active 
ON subscriptions(space_id) 
WHERE conflict_status = 'active';
```

---

# 五、技术实现要点

## 1. 签名验证

```typescript
@Controller('webhook')
export class WebhookController {
  @Post('v1/apple')
  @PublicRoute()
  async handleAppleNotification(
    @Req() request: RawBodyRequest<Request>,
  ): Promise<void> {
    const signedPayload = request.body.toString();
    
    // 验证 Apple 签名
    const isValid = await this.appleService.verifyNotificationSignature(signedPayload);
    if (!isValid) {
      throw new BadRequestException('Invalid Apple notification signature');
    }
    
    // 解析通知内容
    const notification = await this.appleService.parseNotification(signedPayload);
    
    // 发布内部事件（异步处理）
    await this.eventBus.publish(new AppleNotificationEvent(notification));
  }
}
```

## 2. 环境隔离

```typescript
@Injectable()
export class AppleService {
  constructor(private readonly configService: ConfigService) {
    // 根据环境自动选择 Apple 环境
    const environment = this.configService.get('APPLE_ENVIRONMENT');
    this.isProduction = environment === 'production';
  }
  
  getProductIdMapping(): Record<string, {productTier: string, billingInterval: string}> {
    return this.isProduction ? AppleProductId : TestAppleProductId;
  }
}
```

## 3. 错误处理

```typescript
class AppleIntegrationError extends Error {
  constructor(
    message: string,
    public readonly appleError?: any,
    public readonly transactionId?: string
  ) {
    super(message);
  }
}

@EventsHandler(AppleNotificationEvent)
export class AppleEventHandler {
  async handle(event: AppleNotificationEvent): Promise<void> {
    try {
      await this.processAppleNotification(event);
    } catch (error) {
      this.logger.error('Apple notification processing failed', {
        error: error.message,
        notificationType: event.notificationType,
        transactionId: event.data.transactionInfo?.originalTransactionId,
      });
      
      // 根据错误类型决定是否重试
      if (this.isRetryableError(error)) {
        throw error; // 让事件总线重试
      }
      
      // 记录不可重试的错误但不抛出异常
      await this.recordFailedNotification(event, error);
    }
  }
}
```

---

# 六、总结

## 技术方案特点

1. **架构一致性**：完全复用现有 Stripe 集成的架构模式
2. **业务对等性**：Apple 功能与 Stripe 功能完全对等
3. **代码复用性**：最大化复用现有的业务逻辑和基础设施
4. **可维护性**：统一的错误处理、日志记录和监控机制

## 实施风险

1. **Apple 通知延迟**：可能导致积分同步延迟，需要补偿机制
2. **多平台冲突**：需要完善的冲突检测和解决机制
3. **状态映射复杂性**：Apple 状态体系与 Stripe 存在差异，需要精确映射

## 下一步计划

1. **实现状态映射器**：完成 `AppleSubscriptionStateMapper` 的详细实现
2. **扩展 Subscription 聚合**：添加 Apple 相关的静态工厂方法
3. **实现事件处理器**：完成 `AppleEventHandler` 的核心逻辑
4. **设计冲突解决器**：实现多平台订阅冲突的自动处理

这个技术方案确保了 Apple 集成与现有架构的无缝融合，为后续的实施提供了清晰的技术路线图。