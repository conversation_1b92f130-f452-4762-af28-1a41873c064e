# 积分系统测试用例

## 概述

本文档包含 YouAPI 积分系统的完整 E2E 测试用例，基于真实业务场景设计，验证积分系统的完整业务逻辑。

## 相关文档

- [积分系统设计文档](./CREDIT-SYSTEM-DESIGN.md) - 核心设计原则和架构
- [Token 到 Credit 换算设计](./TOKEN-TO-CREDITS-CONVERSION.md) - Token换算机制

---

# 免费用户场景测试

## 测试1：免费用户初始化

**测试目标**：验证免费用户创建时的积分账户初始化

**前置条件**：
- 用户刚注册，时间为 `2024-01-15T10:30:00.000Z`
- 系统中不存在该用户的积分账户

**操作步骤**：
1. 调用用户初始化接口
2. 创建 Space 和 CreditAccount

**预期结果**：
- `CreditAccount.monthlyBalance = 2000`
- `CreditAccount.productTier = 'FREE'`
- `CreditAccount.resetAnchor = new Date('2024-01-15T10:30:00.000Z')`
- `CreditAccount.resetAt = new Date('2024-01-15T10:30:00.000Z')`
- 生成 1条 `CreditTransaction`：
  - `type = 'GRANT'`
  - `amount = 2000`
  - `reason = 'Initial credits for free user - Reset: free-*************'`

**验证点**：
- 积分余额正确初始化
- 重置锚点设置正确
- 交易记录完整准确

## 测试2：免费用户月度重置

**测试目标**：验证免费用户的月度积分重置机制

**前置条件**：
- 用户注册时间为 `2024-01-15T10:30:00.000Z`
- 当前时间为 `2024-02-15T10:30:00.000Z`
- 用户当前余额为 `1500` 积分

**操作步骤**：
1. 定时任务执行积分重置
2. 基于 `resetAnchor` 计算重置时间
3. 执行重置操作

**预期结果**：
- `CreditAccount.monthlyBalance = 2000`
- `CreditAccount.resetAt = new Date('2024-02-15T10:30:00.000Z')`
- 新增 2条 `CreditTransaction`：
  - FORFEIT: `amount = -1500`, `reason = 'Forfeit old credits - Reset: free-*************'`
  - GRANT: `amount = 2000`, `reason = 'Grant 2000 credits - Reset: free-*************'`

**验证点**：
- 旧积分正确废弃
- 新积分正确发放
- 重置时间精确计算

## 测试3：免费用户月末边界情况

**测试目标**：验证月末边界日期的重置时间计算

**前置条件**：
- 用户注册时间为 `2024-01-31T10:30:00.000Z`（1月31日）
- 当前到达 2月份重置时间

**操作步骤**：
1. 定时任务计算 2月份的重置时间
2. 执行重置操作

**预期结果**：
- 重置时间计算正确：`2024-02-29T10:30:00.000Z`（闰年2月29日）
- 或非闰年为：`2024-02-28T10:30:00.000Z`
- `CreditAccount.resetAt` 更新为计算出的时间

**验证点**：
- 月末边界时间计算正确
- 闰年/平年处理正确
- 重置逻辑正常执行

---

# 付费用户场景测试

## 测试4：免费用户首次付费订阅

**测试目标**：验证免费用户转为付费用户的完整流程

**前置条件**：
- 免费用户已存在，`balance = 1500`
- Stripe 订阅数据：
  - `productTier = 'PRO'`
  - `billingInterval = 'MONTHLY'`
  - `billingCycleAnchor = new Date('2024-01-15T10:30:00.000Z')`
  - `currentPeriodStart = new Date('2024-01-15T10:30:00.000Z')`

**操作步骤**：
1. 处理 Stripe `subscription.created` webhook
2. 创建本地订阅记录
3. 重置积分账户

**预期结果**：
- 创建 `Subscription`：
  - `productTier = 'PRO'`
  - `billingInterval = 'MONTHLY'`
  - `currentPeriodStart = new Date('2024-01-15T10:30:00.000Z')`
  - `provider = 'STRIPE'`
- 更新 `CreditAccount`：
  - `monthlyBalance = 20000`
  - `productTier = 'PRO'`
  - `resetAnchor = new Date('2024-01-15T10:30:00.000Z')`
  - `resetAt = new Date('2024-01-15T10:30:00.000Z')`
- 新增 2条 `CreditTransaction`（废弃旧积分 + 发放新积分）

**验证点**：
- 订阅记录正确创建
- 积分等级正确升级
- 锚点时间正确重置

## 测试5：订阅升级（立即生效）

**测试目标**：验证订阅升级的立即生效机制

**前置条件**：
- 用户有 PRO 月度订阅，`balance = 15000`
- Stripe 升级到 MAX，新的 `billingCycleAnchor = new Date('2024-01-16T10:30:00.000Z')`

**操作步骤**：
1. 处理 Stripe `subscription.updated` webhook
2. 同步订阅升级状态
3. 重置积分账户

**预期结果**：
- 更新 `Subscription`：`productTier = 'MAX'`
- 更新 `CreditAccount`：
  - `monthlyBalance = 200000`
  - `productTier = 'MAX'`
  - `resetAnchor = new Date('2024-01-16T10:30:00.000Z')`（重置锚点）
  - `resetAt = new Date('2024-01-16T10:30:00.000Z')`
- 新增积分重置交易记录

**验证点**：
- 升级立即生效
- 锚点时间正确重置
- 积分立即重置为新等级

## 测试6：订阅降级（续期时生效）

**测试目标**：验证订阅降级的延期生效机制

**前置条件**：
- 用户有 MAX 月度订阅
- 用户申请降级到 PRO

**操作步骤**：
1. 用户申请降级到 PRO
2. Stripe 创建 Subscription Schedule
3. 设置延期变更

**预期结果**：
- `Subscription.renewChange.productTier = 'PRO'`
- `Subscription.cancelAtPeriodEnd = false`
- 当前 `balance` 和 `productTier` 不变（等续期时才生效）

**验证点**：
- 降级设置正确保存
- 当前服务不受影响
- 变更计划正确记录

## 测试7：订阅取消（续期时生效）

**测试目标**：验证订阅取消的延期生效机制

**前置条件**：
- 用户有 PRO 月度订阅
- 用户申请取消订阅

**操作步骤**：
1. 用户申请取消订阅
2. 设置取消标志

**预期结果**：
- `Subscription.cancelAtPeriodEnd = true`
- `Subscription.renewChange = undefined`
- 当前订阅状态不变

**验证点**：
- 取消标志正确设置
- 当前服务继续提供
- 等待周期结束生效

## 测试8：变更计费周期（续期时生效）

**测试目标**：验证计费周期变更的延期生效机制

**前置条件**：
- 用户有 PRO 月度订阅
- 用户变更为年付

**操作步骤**：
1. 用户申请变更为年付
2. 创建变更计划

**预期结果**：
- `Subscription.renewChange.billingInterval = 'YEARLY'`
- `Subscription.renewChange.productTier = undefined`
- 当前 `billingInterval` 不变

**验证点**：
- 周期变更正确记录
- 当前计费不受影响
- 变更计划包含正确信息

## 测试9：月度订阅续期成功

**测试目标**：验证月度订阅正常续期的积分重置

**前置条件**：
- 用户有 PRO 月度订阅，有 `renewChange.productTier = 'FREE'`
- `currentPeriodEnd` 已到期
- 续期付款成功

**操作步骤**：
1. 处理 Stripe `invoice.payment_succeeded` webhook
2. 应用变更计划
3. 重置积分

**预期结果**：
- 更新 `Subscription`：
  - `productTier = 'FREE'`（应用 renewChange）
  - 更新 `currentPeriodStart/End`
  - `renewChange = undefined`
- 更新 `CreditAccount`：
  - `monthlyBalance = 2000`
  - `productTier = 'FREE'`
  - `resetAt = newPeriodStart`
- 新增积分重置交易记录

**验证点**：
- 变更计划正确应用
- 积分正确重置
- 交易记录完整

## 测试10：月度订阅续期失败

**测试目标**：验证订阅续期失败的处理机制

**前置条件**：
- 用户有 PRO 月度订阅
- 续期付款失败

**操作步骤**：
1. 处理 Stripe `invoice.payment_failed` webhook
2. 更新订阅状态

**预期结果**：
- 更新 `Subscription.status = 'PAST_DUE'`
- `CreditAccount` 不变（不重置积分）
- 无新的 `CreditTransaction`

**验证点**：
- 订阅状态正确更新
- 积分账户保持不变
- 不产生错误的重置

## 测试11：逾期订阅恢复付款

**测试目标**：验证逾期订阅恢复付款后的处理

**前置条件**：
- 订阅状态为 `PAST_DUE`
- 用户补缴费用成功

**操作步骤**：
1. 处理 Stripe `invoice.payment_succeeded` webhook
2. 恢复订阅状态
3. 重置积分

**预期结果**：
- 更新 `Subscription.status = 'ACTIVE'`
- 重置积分（使用恢复时间作为 resetKey）
- 新增积分重置交易记录

**验证点**：
- 订阅状态正确恢复
- 积分正确重置
- 服务正常恢复

## 测试12：年度订阅月度重置（非续期月）

**测试目标**：验证年度订阅的月度积分重置机制

**前置条件**：
- 用户有 PRO 年度订阅，订阅开始时间为 `2024-01-15`
- 当前时间为 `2024-03-15`（非续期月）

**操作步骤**：
1. 定时任务执行
2. 计算月度重置时间
3. 执行积分重置

**预期结果**：
- 使用订阅的 `billingAnchor` 计算重置时间
- `CreditAccount.resetAt = new Date('2024-03-15T10:30:00.000Z')`
- 新增积分重置交易记录

**验证点**：
- 重置时间计算正确
- 积分按月重置
- 不影响年度计费周期

## 测试13：年度订阅续期（续期月）

**测试目标**：验证年度订阅续期月的事件驱动重置

**前置条件**：
- 用户有 PRO 年度订阅，订阅开始时间为 `2024-01-15`
- 当前时间为 `2025-01-15`（续期月）

**操作步骤**：
1. 处理 Stripe `subscription.updated` webhook
2. 事件驱动积分重置

**预期结果**：
- 事件驱动重置优先于定时任务
- 使用 `currentPeriodStart` 作为 resetKey
- 不会被定时任务重复重置

**验证点**：
- 事件驱动优先级正确
- 防重机制有效
- 数据一致性保证

## 测试14：付费订阅到期取消

**测试目标**：验证付费订阅到期取消的处理

**前置条件**：
- 用户有 PRO 订阅，`cancelAtPeriodEnd = true`
- `currentPeriodEnd` 已到期

**操作步骤**：
1. 处理 Stripe `subscription.canceled` webhook
2. 删除订阅记录
3. 重置为免费状态

**预期结果**：
- 删除 `Subscription` 记录
- 更新 `CreditAccount`：
  - `productTier = 'FREE'`
  - `resetAnchor = cancellationTime`
  - 重置为免费用户积分额度
- 新增积分重置交易记录

**验证点**：
- 订阅记录正确删除
- 恢复免费用户状态
- 积分正确重置

---

# 边界情况测试

## 测试15：重复事件防重

**测试目标**：验证重复事件的防重机制

**前置条件**：
- 用户刚完成积分重置，`resetAt = new Date('2024-01-15T10:30:00.000Z')`
- 相同的 webhook 事件重复到达

**操作步骤**：
1. 处理第一次事件（已完成）
2. 处理重复的相同事件

**预期结果**：
- `CreditAccount.needsReset()` 返回 `false`
- 不执行重置，不新增交易记录
- 幂等性保证正确

**验证点**：
- 防重机制正确工作
- 数据不重复变更
- 性能影响最小

## 测试16：并发重置处理

**测试目标**：验证并发重置请求的数据一致性

**前置条件**：
- 年度订阅用户
- 同时有事件和定时任务尝试重置

**操作步骤**：
1. 事件处理和定时任务并发执行
2. 使用数据库锁机制

**预期结果**：
- 只有一个重置成功（通过 `SELECT FOR UPDATE` 锁定）
- 最终只有一套重置交易记录
- 数据一致性得到保证

**验证点**：
- 数据库锁机制有效
- 并发安全性保证
- 最终数据正确性

---

# 积分消耗测试

## 测试17：Claude Token 消耗

**测试目标**：验证 Claude 模型 token 到积分的换算和消耗

**前置条件**：
- 用户有 PRO 订阅，余额 `20000` 积分
- Claude 4 Sonnet 使用情况：
  - `input_tokens: 1000`
  - `output_tokens: 500`
  - `cache_read_input_tokens: 200`

**操作步骤**：
1. 发布 `ClaudeTokensUsedEvent`
2. 处理器计算积分消耗
3. 执行积分扣减

**预期结果**：
- 计算消耗：`1000/1000*3 + 500/1000*15 + 200/1000*0.3 = 10.56 → 11` 积分
- `CreditAccount.monthlyBalance = 19989`
- 新增 `CreditTransaction`：
  - `type = 'CONSUME'`
  - `amount = 11`
  - `metadata` 包含详细的 token 使用信息

**验证点**：
- Token 换算精确计算
- 积分正确扣减
- 元数据完整记录

## 测试18：积分不足拦截

**测试目标**：验证积分不足时的拦截机制

### 子测试18.1：@CheckCredits 装饰器拦截

**前置条件**：
- 用户余额 `0` 积分
- 调用带有 `@CheckCredits()` 装饰器的方法

**操作步骤**：
1. 在 Handler 方法上添加 `@CheckCredits()` 装饰器
2. 调用该方法

**预期结果**：
- 抛出 `InsufficientCreditsException`
- HTTP 状态码：`402 Payment Required`
- 异常响应格式：
```json
{
  "message": "Credits exhausted",
  "error": "Payment Required", 
  "statusCode": 402,
  "details": {
    "available": 0,
    "currentTier": "FREE",
    "required": null
  }
}
```
- 业务逻辑未执行

### 子测试18.2：CreditAccount.consume() 方法拦截

**前置条件**：
- 用户余额 `50` 积分
- 尝试消耗 `100` 积分

**操作步骤**：
1. 调用 `creditAccount.consume({ amount: 100, reason: 'test', metadata: {} })`
2. 检查拦截机制

**预期结果**：
- 抛出 `InsufficientCreditsException`
- HTTP 状态码：`402 Payment Required`
- 异常响应格式：
```json
{
  "message": "Insufficient credits. Required: 100, Available: 50",
  "error": "Payment Required",
  "statusCode": 402,
  "details": {
    "available": 50,
    "currentTier": "FREE", 
    "required": 100
  }
}
```
- 积分余额不变

**验证点**：
- 余额检查正确执行
- 异常信息完整准确
- 数据完整性保护
- HTTP 状态码符合业务语义

## 测试19：并发积分消耗

**测试目标**：验证并发积分消耗的数据一致性

**前置条件**：
- 用户余额 `1000` 积分
- 同时有 10 个请求各消耗 `100` 积分

**操作步骤**：
1. 并发执行 10 个消耗请求
2. 使用数据库锁机制

**预期结果**：
- 最多成功 10 个请求（正好用完）
- 或成功 N 个请求，其余因余额不足失败
- 最终余额和交易记录完全一致

**验证点**：
- 并发安全性保证
- 数据一致性正确
- 无超额消耗情况

---

# 查询接口测试

## 测试20：获取积分余额

**测试目标**：验证积分余额查询接口

**前置条件**：
- 用户有 PRO 订阅，余额 `15000` 积分

**操作步骤**：
1. 调用 `getCreditBalance` 接口

**预期结果**：
```json
{
  "balance": 15000,
  "productTier": "PRO",
  "resetAnchor": "2024-01-15T10:30:00.000Z",
  "resetAt": "2024-02-15T10:30:00.000Z"
}
```

**验证点**：
- 余额信息准确
- 等级信息正确
- 重置时间完整

## 测试21：查询积分交易记录

**测试目标**：验证积分交易记录查询功能

**前置条件**：
- 用户当前周期有多笔交易记录
- 包含 GRANT、CONSUME、FORFEIT 类型

**操作步骤**：
1. 调用 `listCreditTransactions` 接口
2. 传入筛选和分页参数

**预期结果**：
- 返回当前周期的交易记录
- 按时间倒序排列
- 支持类型筛选和分页
- 包含完整的交易详情

**验证点**：
- 查询范围正确（当前周期）
- 排序和分页正确
- 数据完整性验证

---

# 测试执行指南

## 测试环境设置

### 1. 数据库准备
```typescript
// 每个测试套件独立的数据准备
describe('CreditSystem E2E Tests', () => {
  let helper: TestIamModuleHelper;
  let testUser: User;
  let testSpace: Space;

  beforeAll(async () => {
    helper = new TestIamModuleHelper();
    await helper.init();
  });

  beforeEach(async () => {
    // 为每个测试准备独立的数据
    testUser = await helper.createTestUser();
    testSpace = await helper.createTestSpace(testUser.id);
  });

  afterEach(async () => {
    // 清理测试数据
    await helper.cleanupTestData(testUser.id);
  });

  afterAll(async () => {
    await helper.destroy();
  });
});
```

### 2. 外部服务模拟
```typescript
// Mock Stripe API 调用
const mockStripeService = {
  createSubscription: jest.fn(),
  updateSubscription: jest.fn(),
  cancelSubscription: jest.fn(),
};

// 设置不同的响应场景
mockStripeService.createSubscription
  .mockResolvedValueOnce(successSubscription)
  .mockRejectedValueOnce(new Error('Payment failed'));
```

### 3. 时间模拟
```typescript
// 模拟时间推进
jest.useFakeTimers();
jest.setSystemTime(new Date('2024-01-15T10:30:00.000Z'));

// 推进一个月
jest.setSystemTime(new Date('2024-02-15T10:30:00.000Z'));
```

## 测试执行命令

### 运行全部测试
```bash
pnpm test:e2e credit-system
```

### 运行特定类型测试
```bash
# 只运行免费用户测试
pnpm test:e2e credit-system --testNamePattern="免费用户"

# 只运行付费用户测试  
pnpm test:e2e credit-system --testNamePattern="付费用户"

# 只运行边界情况测试
pnpm test:e2e credit-system --testNamePattern="边界情况"
```

### 调试模式运行
```bash
pnpm test:e2e credit-system --verbose --detectOpenHandles
```

## 测试质量标准

### 1. 覆盖率要求
- 行覆盖率：≥ 90%
- 分支覆盖率：≥ 85%
- 函数覆盖率：≥ 95%

### 2. 性能要求
- 单个测试执行时间：< 5秒
- 全套测试执行时间：< 5分钟
- 数据库操作延迟：< 100ms

### 3. 数据一致性验证
每个测试必须验证：
- 积分余额的准确性
- 交易记录的完整性
- 时间戳的正确性
- 业务状态的一致性

### 4. 错误处理验证
必须测试：
- 异常情况的正确处理
- 错误信息的完整性
- 数据回滚的正确性
- 系统稳定性保证

这些测试用例覆盖了积分系统的所有核心业务场景，确保系统行为符合设计预期，为生产环境部署提供可靠的质量保证。