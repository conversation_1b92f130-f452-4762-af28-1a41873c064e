# Build stage
FROM node:22-alpine AS builder

# Accept build argument for environment
ARG YOUMIND_ENV=production

# Install build dependencies
RUN apk add --no-cache python3 make g++ vips-dev

# Enable pnpm
RUN corepack enable

WORKDIR /app

# Copy everything and let .dockerignore handle filtering
COPY . .

# Install dependencies with cache mount
RUN --mount=type=cache,target=/pnpm/store \
    pnpm config set store-dir /pnpm/store && \
    pnpm install --frozen-lockfile --shamefully-hoist --ignore-scripts --filter=youapi...

# Build the application and its dependencies using Turborepo
ENV YOUMIND_ENV=$YOUMIND_ENV
RUN pnpm turbo build --filter=youapi...

# Production stage
FROM node:22-alpine

# Install runtime dependencies needed for sharp and health check
RUN apk add --no-cache curl vips

WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy built application and necessary files from builder
COPY --from=builder --chown=nodejs:nodejs /app/apps/youapi/dist ./apps/youapi/dist
COPY --from=builder --chown=nodejs:nodejs /app/apps/youapi/package.json ./apps/youapi/package.json
COPY --from=builder --chown=nodejs:nodejs /app/package.json ./package.json
COPY --from=builder --chown=nodejs:nodejs /app/pnpm-workspace.yaml ./pnpm-workspace.yaml

# Copy workspace packages that might be needed at runtime
COPY --from=builder --chown=nodejs:nodejs /app/packages ./packages

# Copy node_modules from builder (preserving symlinks)
COPY --from=builder --chown=nodejs:nodejs /app/node_modules ./node_modules

# Copy youapi-specific node_modules (includes workspace symlinks)
COPY --from=builder --chown=nodejs:nodejs /app/apps/youapi/node_modules ./apps/youapi/node_modules

# Switch to non-root user
USER nodejs

ENV NODE_ENV=production \
    PORT=4000

EXPOSE 4000

HEALTHCHECK --interval=30s --timeout=5s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:4000/api/healthz || exit 1

# Run from the built output
CMD ["node", "apps/youapi/dist/main.js"]
