import { LangfuseSpanClient } from 'langfuse-core';
import { Message, MessageAtReferenceTypeEnum } from '@/common/types';
import { AtReference } from './chat.types';
import { SnipSearchResultItem, ThoughtSearchResultItem } from './search.types';

export type ChunkResult = {
  id: string;
  type: string;
  original_field: string;
  chunk_index: number;
  chunk: string;
  updated_at: string;
  distance: number;
  document?: Partial<SnipSearchResultItem | ThoughtSearchResultItem>;
  title?: string;
};

export type ContextBuildingChunk = Pick<ChunkResult, 'id' | 'type' | 'chunk' | 'distance'> & {
  tokenUsed: number;
  title?: string;
};

export enum SourceTypeEnum {
  SNIP = 'snip',
  CHAT = 'chat',
  BOARD = 'board',
  BOARD_GROUP = 'board_group',
  THOUGHT = 'thought',
}

export type AtReferenceForContextBuilding =
  | AtReference
  | {
      entity_type: 'library';
    };

export type MessageAnalyzedContextType = 'full' | 'relevant' | null;
export interface MessageAnalysis {
  needsContext: boolean;
  searchKeywords: string;
  contextType: MessageAnalyzedContextType;
}

export interface ContainerContentItem {
  type: MessageAtReferenceTypeEnum;
  title: string;
}

export enum ContextProviderType {
  SNIP = 'snip',
  CHAT = 'chat',
  BOARD = 'board',
  BOARD_GROUP = 'board_group',
  THOUGHT = 'thought',
  BOARD_STRUCTURE = 'board_structure',
  WEBPAGE = 'webpage',
  LIBRARY = 'library',
}

/**
 * 统一上下文提供者接口
 *
 * 对应原有的 BaseContextBuilder 能力
 */
export interface IContextProvider {
  /**
   * 获取上下文，同时获取上下文项列表和上下文字符串
   */
  getContexts(
    entityId: string,
    options?: ContextOptions,
  ): Promise<{ contextItems: IContextItem[]; contextString: string }>;

  /**
   * 获取上下文项列表，让调用者拥有自决定拼接子项的能力
   */
  getContextItems(entityId: string, options?: ContextOptions): Promise<IContextItem[]>;

  /**
   * 获取上下文字符串（拼接好的 String, 可以直接用于 LLM 输入）
   */
  getContextString(entityId: string, options?: ContextOptions): Promise<string>;

  /**
   * 获取所有子项的 Meta 信息，比如 List Board 的输出结果
   */
  getContextSubItems(
    userId: string,
    entityId: string,
    options?: ContextOptions,
  ): Promise<IContextSubItem[]>;

  /**
   * 获取Magic Creation上下文
   */
  getMagicCreationContext(
    entityId: string,
    options?: MagicCreationOptions,
  ): Promise<MagicCreationContext>;

  /**
   * 获取提供者类型标识
   */
  getProviderType(): string;
}

export interface IContextItem {
  id: string;
  type: string;
  title: string;
  content: string;
  alias?: string[];
  url?: string;
  extra?: Record<string, unknown>;
}

// TODO: jialiang determine which feature is required for sub items
export interface IContextSubItem extends IContextItem {
  // parent_id?: string;
  // parent_type?: string;
}

/**
 * 上下文获取选项
 */
export interface ContextOptions {
  /**
   * 用户 ID（权限控制）
   */
  userId: string;

  /**
   * 空间 ID（数据隔离）
   */
  spaceId?: string;

  /**
   * 最大 token 数限制
   */
  maxTokens?: number;

  /**
   * 内容获取方法
   */
  method?: 'slice' | 'sample';

  /**
   * Langfuse 追踪
   */
  span?: LangfuseSpanClient;

  /**
   * @ 引用上下文（聊天场景使用）
   */
  at_references_context?: any;

  /**
   * 扩展字段，每一个 provider 需要的参数是不一样的
   */
  extra?: Record<string, any>;
}

/**
 * Magic Creation选项
 */
export interface MagicCreationOptions extends ContextOptions {
  // TODO: may add some magic creation options here.
}

/**
 * 聊天上下文结构
 */
export interface ChatContext {
  image_urls: string[];
  sources?: Array<ChatContext>; // 支持嵌套
  fields: {
    url: string;
    type: string; // "board" | "snip" | "thought" | "chat" | "webpage"
    title: string;
    aliases?: string[];
    related_chunks?: string;
    [key: string]: any; // 其他特定字段
  };
}

/**
 * Magic Creation上下文
 */
export interface MagicCreationContext {
  url: string;
  title: string;
  content: string;
}

/**
 * 上下文元数据
 */
export interface ContextMetadata {
  id: string;
  type: string;
  title?: string;
  size: number;
  tokenCount?: number;
  createdAt: Date;
  updatedAt?: Date;
  tags?: string[];
  version?: string;
}

/**
 * 上下文请求
 */
export interface ContextProviderRequest {
  /**
   * 类型
   */
  providerType: ContextProviderType;

  /**
   * 上下文 ID
   */
  contextId: string;

  /**
   * 获取选项
   */
  options?: ContextOptions;
}

export interface BatchContextOptions {
  /**
   * 上下文请求列表
   */
  contextRequests: ContextProviderRequest[];

  /**
   * 最大 token 数限制
   */
  maxTokens?: number;

  /**
   * 内容获取方法
   */
  method?: 'slice' | 'sample';
}

/**
 * Request for building context
 */
export interface ContextBuildingFromReferencesRequest {
  /**
   * Chat messages
   */
  messages: Message[];

  /**
   * At references to resolve
   */
  at_references: AtReferenceForContextBuilding[];

  /**
   * Available token budget for context
   */
  availableTokens: number;

  /**
   * User ID for permission checks
   */
  userId: string;

  /**
   * Langfuse trace or span
   */
  trace?: LangfuseSpanClient;

  /**
   * Pre-computed message analysis
   * If provided, the analysis step will be skipped
   */
  analysis?: MessageAnalysis;

  /**
   * currently used for web search
   */
  quickMode?: boolean;
}

export enum ContextBuildingStrategy {
  NO_CONTEXT = 'no_context',
  FULL_CONTENT = 'full_content',
  FULL_CONTENT_SAMPLING = 'full_content_sampling',
  SEMANTIC_SEARCH = 'semantic_search',
}

/**
 * 上下文构建结果
 */
export interface ContextBuildingResult {
  atReferencesRetrieved: Array<{
    atReferenceId: string;
    atReferenceType: string;
    chunks: Array<ContextBuildingChunk>;
  }>;
  searchKeywords?: string;
  totalTokensUsed: number;
  strategy: ContextBuildingStrategy;
}

/**
 * 数据源
 */
export interface Source {
  type: string; // 实体类型
  id: string; // 实体ID
}

/**
 * 检索到的内容
 */
export interface RetrievedContent {
  atReferenceId: string;
  atReferenceType: string;
  content: string;
  tokenCount: number;
  chunks: ContextBuildingChunk[];
  searchKeyword?: string;
}
/**
 * 引用评估结果
 */
export interface ReferenceAssessment {
  isComplex: boolean;
  estimatedTokens: number;
  title?: string;
}

/**
 * 检索选项
 */
export interface RetrieveOptions {
  span?: LangfuseSpanClient;
  filters?: Record<string, any>;
}

/**
 * 搜索选项
 */
export interface SearchOptions {
  limit?: number;
  offset?: number;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  userId?: string;
  spaceId?: string;
}
