/**
 * NestJS 认证守卫
 * 从 youapp/src/middleware.ts 迁移而来的认证逻辑
 * 适配到 NestJS Guard 模式
 */

import {
  type CanActivate,
  type ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { tryGetCurrentUserIdAndToken } from '@repo/server-common';
import type { Request, Response } from 'express';
import { PUBLIC_ROUTE } from '../decorators/public.decorator';
import { YouapiClsService } from '../services/cls.service';
import { type SystemConfig } from '../types';

// TODO: 这些路由在迁移时标记为 @Public()
// 开放访问的路由
const _OPEN_ROUTES = [
  '/api/v1/auth/validateOTPToken',
  '/api/v1/globalMaterials/save',
  '/api/v1/globalMaterials/saveByUrl',
  '/api/revalidate',
];

const _NOAUTH_API_ROUTES = [
  '/api/v1/auth/validateOTPToken',
  '/api/v1/cron/checkTrial',
  '/api/v1/cron/checkTimeoutSnips',
  '/api/v1/cron/functionKeepalive',
  '/api/v1/user/setUserTimeZoneIfNotSet',
];

const _NO_ONBOARD_API_ROUTES: string[] = [
  // TODO: 从 youapp 中找到这个常量的定义
];

@Injectable()
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(
    private reflector: Reflector,
    private readonly configService: ConfigService<SystemConfig>,
    private readonly clsService: YouapiClsService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    const isPublic = this.reflector.getAllAndOverride<boolean>(PUBLIC_ROUTE, [
      context.getHandler(),
      context.getClass(),
    ]);
    // 检查是否是公开路由
    if (isPublic) {
      return true;
    }

    // 检查定时任务认证
    const isCronRequest =
      request.headers.authorization === `Bearer ${this.configService.getOrThrow('CRON_SECRET')}`;

    if (isCronRequest) {
      return true;
    }

    // 检查用户认证
    // 使用增强版的 tryGetCurrentUserIdAndToken 来同时获取 userId 和 token
    const { userId, accessToken } = await tryGetCurrentUserIdAndToken({
      getHeader: (name: string) => {
        const value = request.headers[name];
        if (Array.isArray(value)) {
          return value[0];
        }
        return value;
      },
      getCookie: (name: string) => {
        return request.cookies[name];
      },
      setCookie: (name: string, value: string, options: any) => {
        this.logger.debug(`[AuthGuard] setCookie: ${name}, ${value}, ${JSON.stringify(options)}`);
        if (!response.headersSent) {
          response.cookie(name, value, options);
        } else {
          this.logger.warn(
            `[AuthGuard] headersSent, cannot set cookie: ${name}, ${value}, ${JSON.stringify(options)}`,
          );
        }
      },
      removeCookie: (name: string) => {
        this.logger.debug(`[AuthGuard] removeCookie: ${name}`);
        if (!response.headersSent) {
          response.clearCookie(name);
        } else {
          this.logger.warn(`[AuthGuard] headersSent, cannot remove cookie: ${name}`);
        }
      },
    });

    if (!userId) {
      throw new UnauthorizedException();
    }

    // pino logger 会自动记录 userId
    // this.logger.log(`userId: ${userId}`);
    // 设置用户信息到 CLS
    this.clsService.setUserId(userId);

    // 缓存 access token 到 CLS，避免 AuthService 重复调用 getSession
    if (accessToken) {
      this.clsService.setAccessToken(accessToken);
    }

    return true;
  }
}
