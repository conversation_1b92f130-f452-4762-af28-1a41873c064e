import React from 'react';

export default function EmailTemplateLayout({
  children,
  imageAlign = 'left',
  style = {},
}: {
  children: React.ReactNode;
  imageAlign?: 'center' | 'left';
  style?: React.CSSProperties;
}) {
  return (
    <div style={{ width: '440px', margin: '0 auto', background: '#fff', ...style }}>
      <a href="https://youmind.ai" target="_blank" rel="noopener">
        <img
          alt="" // 刻意留空以免在收件箱列表看到这个 alt 文字
          style={{
            width: '118px',
            height: '20px',
            display: 'block',
            margin: '28px 0 70px',
            marginLeft: imageAlign === 'center' ? 'auto' : '0',
            marginRight: imageAlign === 'center' ? 'auto' : undefined,
          }}
          src="https://cdn.gooo.ai/assets/youmind2x.png"
        />
      </a>
      <div
        style={{
          font: "500 14px/20px 'SF Pro', 'Inter', sans-serif",
          color: 'rgba(0, 0, 0, 0.88)',
        }}
      >
        {children}
      </div>
    </div>
  );
}
