import React from 'react';

import EmailTemplateLayout from './layout';

interface WelcomeEmailTemplateProps {
  name: string;
}

export const WelcomeEmailTemplate: React.FC<Readonly<WelcomeEmailTemplateProps>> = ({ name }) => {
  const renderDivider = () => {
    return (
      <p
        style={{
          textAlign: 'center',
          fontSize: '22px',
          fontWeight: '800',
          color: '#00000099',
          marginTop: '24px',
          marginBottom: '24px',
        }}
      >
        · · ·
      </p>
    );
  };

  const renderStartButton = () => {
    return (
      <p>
        <a href="https://youmind.ai" style={{ textDecoration: 'none' }}>
          <button
            style={{
              width: '160px',
              height: '40px',
              margin: '12px auto 0 auto',
              display: 'block',
              textAlign: 'center',
              background: 'rgba(0, 0, 0, 0.88)',
              borderRadius: '100px',
              color: '#fff',
              fontSize: '16px',
              fontWeight: '500',
              marginTop: '24px',
              cursor: 'pointer',
              border: 'none',
            }}
          >
            Start now
          </button>
        </a>
      </p>
    );
  };

  const textStyle = {
    color: '#000000E0',
    fontSize: '16px',
    lineHeight: '24px',
    fontWeight: '400',
  };

  return (
    <EmailTemplateLayout imageAlign="center" style={{ width: '560px' }}>
      <p style={textStyle}>👋 Hi {name},</p>
      <p style={textStyle}>Welcome to YouMind, your space to write something good.</p>
      <p style={textStyle}>
        We believe everyone has creativity within them, just waiting to be discovered and nurtured.
        As your creative partner, we&apos;re here to unlock your unique potential and turn your
        ideas into reality.
      </p>
      <p style={textStyle}>Your journey starts now. Let&apos;s create together.</p>

      {renderStartButton()}
      {renderDivider()}

      <h2
        style={{
          ...textStyle,
          marginTop: '8px',
          fontSize: '20px',
          fontWeight: '600',
        }}
      >
        Save your materials
      </h2>
      <img
        src="https://cdn.gooo.ai/assets/mail-save2.png"
        alt="welcome"
        style={{
          width: '100%',
          height: 'auto',
        }}
      />
      <p style={textStyle}>
        With the <strong>browser extension</strong>, you can save articles, videos, podcasts and
        other webpages in one place. You can also upload your PDFs, audio files, and images
        directly.
      </p>
      <a
        href="https://chromewebstore.google.com/detail/youmind-ask-and-save-anyt/cnnenlbocdcjnmpkkbbdgjfejinfffjc"
        target="_blank"
        style={{
          fontSize: '16px',
        }}
        rel="noopener"
      >
        Get extension
      </a>

      {renderDivider()}

      <h2
        style={{
          ...textStyle,
          marginTop: '8px',
          fontSize: '20px',
          fontWeight: '600',
        }}
      >
        Dive deep with AI
      </h2>
      <img
        src="https://cdn.gooo.ai/assets/mail-deep-ai2.png"
        alt="welcome"
        style={{
          width: '100%',
          height: 'auto',
        }}
      />
      <p style={textStyle}>
        In YouMind, every material comes with its own AI partner, ready at any time. With AI, you
        can quickly get an overview of articles, generate transcriptions, and explore insights
        through AI chats.
      </p>

      {renderDivider()}

      <h2
        style={{
          ...textStyle,
          marginTop: '8px',
          fontSize: '20px',
          fontWeight: '600',
        }}
      >
        Organize and create with Board
      </h2>
      <img
        src="https://cdn.gooo.ai/assets/mail-board.png"
        alt="welcome"
        style={{
          width: '100%',
          height: 'auto',
        }}
      />
      <p style={textStyle}>
        You can organize diverse materials into one Board. Once your materials are in one place,
        YouMind becomes your specialized AI partner, helping you search related content, craft
        drafts, and enhance every creation that matters to you.
      </p>

      {renderStartButton()}

      <p style={{ marginTop: '48px', ...textStyle }}>
        Warmly, <br />
        The YouMind Team
      </p>
    </EmailTemplateLayout>
  );
};
