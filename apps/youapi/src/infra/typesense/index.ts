/**
 * Typesense Infrastructure Service - Typesense 搜索引擎服务
 * 处理全文搜索和文档管理
 *
 * Migrated from:
 * - youapp/src/infra/typesense/index.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { getServicesEnv } from '@repo/common';
import { Client, Errors } from 'typesense';
import type { CollectionCreateSchema } from 'typesense/lib/Typesense/Collections';
import type { DocumentSchema } from 'typesense/lib/Typesense/Documents';

import {
  type SnipSearchDocument,
  type ThoughtSearchDocument,
  type TypesenseAction,
  TypesenseCollection,
} from '@/domain/search/definition';
import { getCollectionNameWithEnv } from '@/domain/search/utils';

@Injectable()
export class TypesenseService {
  private readonly logger = new Logger(TypesenseService.name);
  client: Client;
  bulkClient: Client | null;

  constructor() {
    this.client = new Client({
      nodes: [
        {
          host: '7hxypkv6ira1ogtnp-1.a1.typesense.net',
          port: 443,
          protocol: 'https',
        },
      ],
      apiKey: process.env.TYPESENSE_ADMIN_API_KEY!,
      connectionTimeoutSeconds: 5,
    });
    this.bulkClient = null;
  }

  createCollection(schema: CollectionCreateSchema, env = getServicesEnv()) {
    const { name } = schema;
    const nameWithEnv = getCollectionNameWithEnv(name as TypesenseCollection, env);
    return this.client.collections().create({
      ...schema,
      name: nameWithEnv,
    });
  }

  dropCollection(name: TypesenseCollection, env = getServicesEnv()) {
    const nameWithEnv = getCollectionNameWithEnv(name, env);
    return this.client.collections(nameWithEnv).delete();
  }

  retrieveCollection(name: TypesenseCollection, env = getServicesEnv()) {
    const nameWithEnv = getCollectionNameWithEnv(name, env);
    return this.client.collections(nameWithEnv).retrieve();
  }

  listCollections() {
    return this.client.collections().retrieve();
  }

  performMultiSearch<T extends DocumentSchema[]>(
    ...args: Parameters<typeof this.client.multiSearch.perform>
  ) {
    const [queries, commonParams] = args;
    const searchesWithEnv = queries.searches.map((query) => ({
      ...query,
      collection: getCollectionNameWithEnv(query.collection as TypesenseCollection),
    }));
    return this.client.multiSearch.perform<T>(
      {
        searches: searchesWithEnv,
      },
      commonParams,
    );
  }

  syncDocuments(
    collection: TypesenseCollection,
    documents: DocumentSchema[],
    action: TypesenseAction,
    env = getServicesEnv(),
  ) {
    const collectionWithEnv = getCollectionNameWithEnv(collection, env);
    return this.client.collections(collectionWithEnv).documents().import(documents, {
      action,
    });
  }

  bulkUpsertDocuments(
    collection: TypesenseCollection,
    documents: DocumentSchema[],
    env = getServicesEnv(),
  ) {
    if (documents.length === 0) {
      return;
    }

    if (!this.bulkClient) {
      // use a new client for a larger timeout
      this.bulkClient = new Client({
        nodes: [
          {
            host: '7hxypkv6ira1ogtnp-1.a1.typesense.net',
            port: 443,
            protocol: 'https',
          },
        ],
        apiKey: process.env.TYPESENSE_ADMIN_API_KEY!,
        connectionTimeoutSeconds: 60,
      });
    }

    const collectionWithEnv = getCollectionNameWithEnv(collection, env);
    return this.bulkClient
      .collections(collectionWithEnv)
      .documents()
      .import(documents, {
        action: 'emplace',
        return_doc: false,
      })
      .then((result) => {
        const succeed = result.filter((item) => item.success);
        const failed = result.filter((item) => !item.success);
        this.logger.log(
          `[search] bulk upsert ${collection} success / fail / total: ${succeed.length} / ${failed.length} / ${result.length}`,
        );
      })
      .catch((error) => {
        if (error instanceof Errors.ImportError) {
          this.logger.warn('Import error occurred', error.importResults);
        } else {
          this.logger.warn(
            `[search] bulk upsert ${documents.length} documents to ${collection} failed`,
            error,
          );
        }
      });
  }

  deleteDocuments(collection: TypesenseCollection, entityIds: string[], env = getServicesEnv()) {
    const collectionWithEnv = getCollectionNameWithEnv(collection, env);
    return this.client
      .collections(collectionWithEnv)
      .documents()
      .delete({ filter_by: `id: [${entityIds.join(',')}]` })
      .then(() => {
        this.logger.log(
          `[search] delete documents ${entityIds.join(',')} from ${collection} success`,
        );
      })
      .catch((error) => {
        if (!(error instanceof Errors.ObjectNotFound)) {
          this.logger.warn(
            `[search] delete documents ${entityIds.join(',')} from ${collection} failed`,
            error,
          );
        }
      });
  }

  fetchSnips(entityIds: string[]) {
    const collectionWithEnv = getCollectionNameWithEnv(TypesenseCollection.SNIPS, getServicesEnv());
    return this.client
      .collections(collectionWithEnv)
      .documents()
      .search({
        q: '*',
        filter_by: `id: [${entityIds.join(',')}]`,
        per_page: entityIds.length,
      });
  }

  async getLatestUpdatedAt(collection: TypesenseCollection, env = getServicesEnv()) {
    const collectionWithEnv = getCollectionNameWithEnv(collection, env);
    const result = await this.client
      .collections<SnipSearchDocument | ThoughtSearchDocument>(collectionWithEnv)
      .documents()
      .search({
        q: '*',
        per_page: 1,
      });
    if (!result.hits || result.hits.length === 0) {
      return null;
    }
    return result.hits[0].document.updated_at;
  }
}
