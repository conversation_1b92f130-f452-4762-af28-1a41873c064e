/**
 * Create Board Tool Service - 创建板块工具服务
 * 创建新的板块
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/create_board.ts
 * - apps/youapi/src/domain/chat/tool_call/create-board.service.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LangfuseSpanClient } from 'langfuse-core';
import z from 'zod';
import { ChatOriginTypeEnum, ToolNames } from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { UpdateChatOriginCommand } from '@/modules/chat/services/commands/update-chat-origin.command';
import { CreateBoardCommand } from '@/modules/material-mng/services/commands/board/create-board.command';
import { BaseToolService } from './base.service';

// Icon names as strings - backend shouldn't import React components
const AVAILABLE_ICONS = [
  'Archive',
  'Atom',
  'Audio',
  'Bar<PERSON>hart',
  'Battery',
  'Beach',
  'Bell',
  'Bike',
  'Birthd',
  'Book',
  'Bored',
  'Bot',
  'Box',
  'Bug',
  'Calendar',
  'Camera',
  'Car',
  'Cat',
  'Chat',
  'Cheers',
  'Chip',
  'ChristmasTree',
  'Clock',
  'Cloud',
  'Compass',
  'Computer',
  'Cone',
  'Confetti',
  'Database',
  'Dev',
  'Diamond',
  'Dog',
  'Drink',
  'Earth',
  'Education',
  'Family',
  'Film',
  'Fire',
  'Flag',
  'Flight',
  'Folder',
  'Footprint',
  'ForkAndKnife',
  'Game',
  'Ghost',
  'Gift',
  'Glasses',
  'Handle',
  'Home',
  'Hourglass',
  'Image',
  'Ink',
  'Joy',
  'Key',
  'Landmark',
  'LightBulb',
  'Lightning',
  'Link',
  'List',
  'Lock',
  'Love',
  'Magic',
  'Mail',
  'Money',
  'Monitor',
  'Mountain',
  'News',
  'Nut',
  'Operaglass',
  'Page',
  'Path',
  'Pencil',
  'Pills',
  'Pin',
  'Planet',
  'Playlist',
  'Podcast',
  'Purse',
  'Raining',
  'Sad',
  'Scissors',
  'Setting',
  'Ship',
  'Sun',
  'Tag',
  'Tea',
  'Terminal',
  'Test',
  'ThumbsUp',
  'Tree',
  'Trophy',
  'Umbrella',
  'Video',
  'Wifi',
  'Work',
  'Yoga',
] as const;
export const PALETTE = [
  '--foreground',
  '--function-gray',
  '--function-link',
  '--function-mint',
  '--function-green',
  '--function-indigo',
  '--function-purple',
  '--function-pink',
  '--function-red',
  '--function-orange',
  '--function-yellow',
  '--function-brown',
] as const;

// 定义创建板块参数的类型
const CreateBoardParameters = z.object({
  title: z.string().describe('The title of the board to create'),
  description: z.string().optional().describe('The description of the board'),
  icon_name: z
    .enum(AVAILABLE_ICONS)
    .describe('The icon for the board. Must be one of the available icons.'),
  icon_color: z
    .enum(PALETTE)
    .describe('The color for the board. Must be one of the available colors.'),
});

type CreateBoardParameters = z.infer<typeof CreateBoardParameters>;

const CreateBoardDescription = `Create a new board in YouMind.
Use this tool when the user explicitly asks to create a new board or when you need to organize content into a new board.
The board will be created in the current workspace and can be used to organize thoughts, notes, and other content.`;

@Injectable()
export class CreateBoardService extends BaseToolService {
  private readonly logger = new Logger(CreateBoardService.name);

  readonly toolName: ToolNames = ToolNames.CREATE_BOARD;
  readonly toolDescription = CreateBoardDescription;
  readonly toolParameters = CreateBoardParameters;
  readonly toolExecutionTimeout = 10000;

  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
  ) {
    super(queryBus, commandBus);
  }

  async execute(
    parameters: ToolFunctionParameters,
    _span: LangfuseSpanClient,
  ): Promise<ToolCallResult> {
    const { userId, parsedParams, chat } = parameters;
    const { title, description, icon_name, icon_color } = parsedParams;

    if (!title) throw new Error('Invalid title');
    if (!icon_name) throw new Error('Invalid icon name');
    if (!icon_color) throw new Error('Invalid icon color');
    const spaceId = await this.getSpaceId(userId);

    const command = new CreateBoardCommand(
      userId,
      spaceId,
      title,
      description || '',
      { name: icon_name, color: icon_color },
      'normal',
    );
    const board = await this.commandBus.execute(command);

    // 使用命令更新聊天来源
    const updateChatOriginCommand = new UpdateChatOriginCommand(chat.id, {
      type: ChatOriginTypeEnum.BOARD,
      id: board.id,
    });
    await this.commandBus.execute(updateChatOriginCommand);

    return {
      response: `board was created successfully with id: ${board.id}`,
      result: {
        board_icon: {
          name: icon_name,
          color: icon_color,
        },
        name: board.name,
        board_id: board.id,
      },
    };
  }
}
