/**
 * Google Search Tool Service - 谷歌搜索工具服务
 * 在互联网上搜索内容，支持网页、视频、学术论文和本地事件
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/google_search.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import z from 'zod';
import { InvalidArguments } from '@/common/errors';
import {
  type CompletionStreamChunk,
  CompletionStreamModeEnum,
  type CompletionStreamReplaceChunk,
  ContextProviderType,
  StreamChunkUnion,
  ToolCallResult,
  ToolFunctionParameters,
  ToolNames,
} from '@/common/types';
import { francToIso6391 } from '@/common/utils';
import { ContextManager } from '@/modules/ai/contexts/context.manager';
import { TextRunnerService } from '@/modules/ai/runners/service/text-runner.service';
import { BaseToolService } from './base.service';

interface LanguageDetectionResult {
  text: string;
  declaredLang: string;
  francDetected?: string;
  aiDetected?: string;
  finalLang: string;
  corrected: boolean;
  method: 'declared' | 'ai';
}

// 定义搜索参数的类型
const GoogleSearchParameters = z.object({
  query: z
    .string()
    .describe("Search query, under 30 words, in the same language as the user's input."),
  type: z
    .enum(['webpage', 'video', 'events', 'scholar'])
    .describe(
      `Type of content to search: webpage (default), video, scholar, or event. Advanced search operators are supported for non-webpage searches (video, scholar, events), e.g., exact match ("climate change effects"), negation (-politics), or site-specific searches (site:*.edu).`,
    ),
  language: z
    .string()
    .describe(
      `ISO 639-1 code of the query language (e.g., 'zh', 'en'). For Traditional Chinese queries, use 'zh-Hant'.`,
    ),
  multilingual_queries: z
    .array(
      z.object({
        q: z.string().describe('Search query in a different language'),
        lang: z
          .string()
          .describe(
            `ISO 639-1 code of the query language (e.g., 'zh', 'en'). For Traditional Chinese queries, use 'zh-Hant'.`,
          ),
      }),
    )
    .optional()
    .describe(
      `Optional array of queries in languages other than the user's input language; use when it improves search results or when the original query mentions places, people, events, or entities whose primary language context differs from the query language (e.g., adding an English query for "特朗普最新对华政策")`,
    ),
  location: z
    .string()
    .optional()
    .describe(`Required for local events. Must be in English (e.g., 'New York', 'Tokyo').`),
  event_filter: z
    .string()
    .optional()
    .describe(
      "Time and type filter for events. Supports date filters: 'date:today' (Today's Events), 'date:tomorrow' (Tomorrow's Events), 'date:week' (This Week's Events), 'date:weekend' (This Weekend's Events), 'date:next_week' (Next Week's Events), 'date:month' (This Month's Events), 'date:next_month' (Next Month's Events). Event type filter: 'event_type:Virtual-Event' (Online Events). You can mix different filters by separating them with a comma, e.g., 'event_type:Virtual-Event,date:today' for Today's Online Events.",
    ),
  freshness: z
    .string()
    .optional()
    .describe(
      "Filters search results by when they were discovered. Supports 'pd' (within 24 hours), 'pw' (within 7 days), 'pm' (within 31 days), 'py' (within 365 days), or date range format 'YYYY-MM-DDtoYYYY-MM-DD' (e.g., '2022-04-01to2022-07-30').",
    ),
});

type GoogleSearchParameters = z.infer<typeof GoogleSearchParameters>;

const GoogleSearchDescription = `Find content on the Internet. This tool can search webpages, videos, scholarly papers, and local events. Queries should be concise (under 30 words) and must not be similar across multiple calls. To construct effective queries:
- Use diverse and specific keywords while sticking to the main topic.
- Use multilingual_queries when it significantly improves search results or when the original query mentions places, people, events, or entities whose primary language context differs from the query language (e.g., adding an English query for "Trump China Policies").
- For queries with timeframes, include absolute dates (e.g., "climate change impact on agriculture in 2025") instead of relative terms like "tomorrow" or "latest".
- Include related queries that may affect answers, such as checking weather for event planning.
- Advanced search operators are supported for non-webpage searches (video, scholar, events), e.g., exact match ("climate change effects"), negation (-politics), or site-specific searches (site:*.edu).`;

@Injectable()
export class GoogleSearchService extends BaseToolService {
  private readonly logger = new Logger(GoogleSearchService.name);

  readonly toolName: ToolNames = ToolNames.GOOGLE_SEARCH;
  readonly toolDescription = GoogleSearchDescription;
  readonly toolParameters = GoogleSearchParameters;
  readonly toolExecutionTimeout = 60000;

  readonly maxCalls = 8;

  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
    private readonly contextManager: ContextManager,
    private readonly textRunnerService: TextRunnerService,
  ) {
    super(queryBus, commandBus);
  }

  /**
   * 处理多语言查询
   */
  public extractMultilingualQueries(
    queries?: Array<{ q: string; lang: string }> | unknown,
  ): { q: string; lang: string }[] {
    if (!queries || !Array.isArray(queries)) return [];

    try {
      return queries
        .map((item) => ({
          q: item?.q || '',
          lang: item?.lang || 'en',
        }))
        .filter((q) => !!q.q);
    } catch (error) {
      this.logger.error(`extractMultilingualQueries error: ${error}`);
      return [];
    }
  }

  /**
   * 批量验证和纠正语言代码
   */
  private async validateAndCorrectLanguages(
    textLangPairs: Array<{ text: string; declaredLang: string }>,
    span: LangfuseSpanClient,
  ): Promise<LanguageDetectionResult[]> {
    try {
      // 使用 francToIso6391 批量检测
      const francResults = textLangPairs.map(({ text, declaredLang }) => {
        const francDetected = francToIso6391(text);
        return {
          text,
          declaredLang,
          francDetected,
          needsAiCheck: francDetected !== declaredLang && declaredLang !== 'zh-Hant',
        };
      });

      // 收集需要 AI 检测的文本
      const textsNeedingAiCheck = francResults
        .filter((item) => item.needsAiCheck)
        .map((item) => item.text);

      let aiResults: string[] = [];
      if (textsNeedingAiCheck.length > 0) {
        span.update({
          name: 'ai-language-detection-call',
          input: {
            texts_needing_check: textsNeedingAiCheck.length,
            texts: textsNeedingAiCheck,
          },
        });

        const now = Date.now();
        const result = await this.textRunnerService
          .getDetectLanguageRunner({
            texts: textsNeedingAiCheck,
          })
          .generateOnce();

        aiResults = result.text
          .split('\n')
          .filter((r) => r.trim() !== '')
          .map((r) => r.trim());

        this.logger.log('ai-language-detection-result', {
          ai_detected_languages: aiResults,
          ai_raw_result: result.text,
        });

        if (aiResults.some((r) => r === 'unknown')) {
          this.logger.warn('ai detect language unknown', {
            texts: textsNeedingAiCheck,
            ai_detected_languages: aiResults,
            ai_raw_result: result.text,
          });

          span.update({
            name: 'ai-language-detection-unknown',
          });
        }

        span.update({
          name: 'ai-language-detection-result',
          output: {
            ai_detected_languages: aiResults,
            time_cost: Date.now() - now,
          },
        });
      }

      // 创建从文本到AI检测结果的映射
      const aiResultsMap = new Map<string, string>();
      textsNeedingAiCheck.forEach((text, index) => {
        aiResultsMap.set(text, aiResults[index]);
      });

      // 将检测结果映射回原始数组
      const detectionResults: LanguageDetectionResult[] = francResults.map((item) => {
        const result: LanguageDetectionResult = {
          text: item.text,
          declaredLang: item.declaredLang,
          francDetected: item.francDetected,
          finalLang:
            item.declaredLang === 'zh-Hant'
              ? item.declaredLang
              : item.francDetected || item.declaredLang,
          corrected: false,
          method: 'declared',
        };

        if (!item.needsAiCheck) {
          result.method = 'declared';
          return result;
        }

        const aiDetected = aiResultsMap.get(item.text);
        result.aiDetected = aiDetected;

        if (aiDetected && aiDetected !== 'unknown') {
          result.finalLang = aiDetected;
          result.corrected = true;
          result.method = 'ai';
          this.logger.log(
            `Language corrected: declared=${item.declaredLang}, franc=${item.francDetected}, ai=${aiDetected}, using=${aiDetected}`,
          );
        } else {
          result.method = 'declared';
          this.logger.log(
            `Language detection uncertain: declared=${item.declaredLang}, franc=${item.francDetected}, ai=${aiDetected}, using=${item.declaredLang}`,
          );
        }

        return result;
      });

      span.update({
        name: 'language-detection-summary',
        output: {
          results: detectionResults,
        },
      });

      return detectionResults;
    } catch (error) {
      span.update({
        name: 'language-detection-error',
        input: {
          error: error?.toString(),
          texts_count: textLangPairs.length,
        },
      });

      this.logger.error('Language validation error:', error);
      return textLangPairs.map((item) => ({
        text: item.text,
        declaredLang: item.declaredLang,
        finalLang: item.declaredLang,
        corrected: false,
        method: 'declared' as const,
      }));
    }
  }

  public formatStringResult(results: any[]): string {
    const template = '[${title}](${url})\n${content}\n\n';
    return results
      .map((result) =>
        template
          .replace('${title}', result.title)
          .replace('${url}', result.url)
          .replace('${content}', result.content),
      )
      .join('\n');
  }

  public async execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { userId, chat, parsedParams, completionBlock } = parameters;
    const spaceId = await this.getSpaceId(userId);
    const { query, location, event_filter, language, multilingual_queries, freshness } =
      parsedParams;

    let type = (parsedParams.type || 'webpage') as 'webpage' | 'video' | 'events' | 'scholar';

    const typeEnum = ['webpage', 'video', 'events', 'scholar'];
    if (!typeEnum.includes(type)) {
      type = 'webpage';
    }

    // 处理多语言查询
    const parsedMultilingualQueries = this.extractMultilingualQueries(multilingual_queries);

    // 收集所有需要验证的文本和语言对
    const textLangPairs = [
      { text: query, declaredLang: language },
      ...parsedMultilingualQueries.map((item) => ({
        text: item.q,
        declaredLang: item.lang,
      })),
    ];

    // 一次性验证和纠正所有语言代码
    const detectionResults = await this.validateAndCorrectLanguages(textLangPairs, span);

    // 分离出主查询和多语言查询的纠正结果
    const correctedLanguage = detectionResults[0].finalLang;
    const correctedMultilingualQueries = parsedMultilingualQueries.map((item, index) => ({
      q: item.q,
      lang: detectionResults[index + 1].finalLang,
    }));

    this.logger.log(`corrected language: ${correctedLanguage}`);
    this.logger.log(
      `corrected multilingual queries: ${JSON.stringify(correctedMultilingualQueries)}`,
    );

    if (!query) {
      this.logger.error(`query is not provided: ${chat.id}`);
      throw new InvalidArguments('missing query');
    }

    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: parameters.completionBlock.id,
      data: {
        multilingual_queries: correctedMultilingualQueries,
      },
      path: 'tool_result',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    const results = await this.contextManager.getContextItems({
      providerType: ContextProviderType.WEBPAGE,
      contextId: chat.id,
      options: {
        method: 'slice',
        span: span,
        userId: userId,
        spaceId: spaceId,
        extra: {
          query,
          type,
          language: correctedLanguage,
          multilingual_queries: correctedMultilingualQueries,
          location,
          event_filter,
          freshness,
        },
      },
    });

    if (!results.length) {
      span.update({
        name: 'no-internet-result-found',
        input: {
          query,
          userId,
        },
      });
      return {
        response: 'No Internet search result found, please try again with a different query',
        result: { results: [] },
      };
    }

    // 初始化空数组
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: [],
      path: 'tool_result.results',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);
    // 然后逐条添加结果
    for (let index = 0; index < results.length; index++) {
      subject.next({
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: completionBlock.id,
        data: results[index],
        path: `tool_result.results[${index}]`,
      } as CompletionStreamReplaceChunk<StreamChunkUnion>);
    }

    const stringResult = this.formatStringResult(results);
    return {
      response: stringResult,
      result: {
        searchResult: results,
      },
    };
  }
}
