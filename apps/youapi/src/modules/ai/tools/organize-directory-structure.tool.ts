/**
 * Organize Directory Structure Tool Service - 整理目录结构工具服务
 * 重新组织看板中的内容目录结构
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/organize_directory_structure.ts
 * - apps/youapi/src/domain/chat/tool_call/organize-directory-structure.service.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { jsonrepair } from 'jsonrepair';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import z from 'zod';
import type {
  CompletionStreamChunk,
  StreamChunkUnion,
  ToolCallResult,
  ToolFunctionParameters,
} from '@/common/types';
import { ToolNames } from '@/common/types';
import { OrganizeDirectoryStructureCommand } from '../../material-mng/services/commands/board/organize-directory-structure.command';
import { BaseToolService } from './base.service';

// 定义目录结构项的类型
const SnipDirectoryItem = z.object({
  entity_type: z.literal('snip'),
  entity_id: z.string(),
});

const ThoughtDirectoryItem = z.object({
  entity_type: z.literal('thought'),
  entity_id: z.string(),
});

const GroupDirectoryItem = z.object({
  entity_type: z.literal('board_group'),
  entity_id: z.string().optional(),
  name: z.string(),
  children: z.array(z.union([SnipDirectoryItem, ThoughtDirectoryItem])),
});

const DirectoryItem = z.union([GroupDirectoryItem, SnipDirectoryItem, ThoughtDirectoryItem]);

const OrganizeDirectoryStructureParameters = z.object({
  new_directory_structure: z.array(DirectoryItem).describe(
    `An array describing the new directory structure. Each item must be one of the following types:

- Group: { "entity_type": "board_group", "entity_id"?: string, "name": string, "children": (Snip | Thought)[] }
- Snip: { "entity_type": "snip", "entity_id": string }
- Thought: { "entity_type": "thought", "entity_id": string }

Groups must not be nested; only a single level of groups is allowed. For group children, only snip and thought types are allowed. If the id of a group is omitted, it indicates that a new group should be created.

Example:
[
  {
    "entity_type": "board_group",
    "name": "Drafts",
    "children": [
      { "entity_type": "thought", "entity_id": "id1" }
    ]
  },
  {
    "entity_type": "board_group",
    "name": "New Group",
    "children": [
      { "entity_type": "snip", "entity_id": "id2" }
    ]
  },
  {
    "entity_type": "board_group",
    "name": "Existing Group",
    "entity_id": "group123", // means use existing group
    "children": [
      { "entity_type": "snip", "entity_id": "id3" }
    ]
  },
  { "entity_type": "snip", "entity_id": "id4" }
]`,
  ),
});

type OrganizeDirectoryStructureParameters = z.infer<typeof OrganizeDirectoryStructureParameters>;

const OrganizeDirectoryStructureDescription =
  'Organize the content of a board by specifying a new directory structure, grouping and ordering items such as snips and thoughts as needed. Always place all thoughts in a group named "Drafts" that appears as the first item in the structure.';

@Injectable()
export class OrganizeDirectoryStructureService extends BaseToolService {
  private readonly logger = new Logger(OrganizeDirectoryStructureService.name);

  readonly toolName: ToolNames = ToolNames.ORGANIZE_DIRECTORY_STRUCTURE;
  readonly toolDescription = OrganizeDirectoryStructureDescription;
  readonly toolParameters = OrganizeDirectoryStructureParameters;
  readonly toolExecutionTimeout = 10000; // 10 seconds for directory organization
  readonly maxCalls = 1;

  async execute(
    parameters: ToolFunctionParameters,
    _span: LangfuseSpanClient,
    _subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { parsedParams, chat } = parameters;
    const boardId = chat.getBoardId();
    if (!boardId) throw new Error('Board ID is required');

    // Parse the directory structure with fallback to jsonrepair
    let directoryStructure: any[];
    if (typeof parsedParams.new_directory_structure === 'string') {
      try {
        directoryStructure = JSON.parse(parsedParams.new_directory_structure);
      } catch {
        // Fallback to jsonrepair for malformed JSON
        const repairedJson = jsonrepair(parsedParams.new_directory_structure);
        directoryStructure = JSON.parse(repairedJson);
      }
    } else {
      directoryStructure = parsedParams.new_directory_structure;
    }

    // Execute the organize directory structure command
    const command = new OrganizeDirectoryStructureCommand(boardId, directoryStructure);
    await this.commandBus.execute(command);

    return {
      response:
        'The organize_directory_structure tool call was successful. The directory structure has been organized.',
    };
  }
}
