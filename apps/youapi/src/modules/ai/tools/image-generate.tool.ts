/**
 * Image Generate Tool Service - 图像生成工具服务
 * 提供图像生成和编辑功能，支持多种尺寸、风格和背景配置
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/image_generate.ts
 * - apps/youapi/src/domain/chat/tool_call/image-generate.service.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import z from 'zod';
import type { CompletionStreamChunk } from '@/common/types';
import {
  CompletionStreamModeEnum,
  CompletionStreamReplaceChunk,
  MessageAtReferenceTypeEnum,
  MessageImageGenerateResultSchema,
  StreamChunkUnion,
  ToolNames,
} from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { CreateUploadFileDto } from '../../material-mng/dto/snip/file.dto';
import { CreateImageCommand } from '../../material-mng/services/commands/snip/create-image.command';
import { GetImageUrlsFromSnipsQuery } from '../../material-mng/services/queries/snip/get-image-urls-from-snips.query';
import { ImageRunner } from '../runners/image';
import { BaseToolService } from './base.service';

const size_map = {
  auto: 'auto' as const,
  square: '1024x1024' as const,
  portrait: '1024x1536' as const,
  landscape: '1536x1024' as const,
};

// 定义图像生成参数的类型
const ImageGenerateParameters = z.object({
  prompt: z.string().describe('The prompt to generate a image for'),
  title: z.string().describe('The title of the image.'),
  source_image_urls: z
    .array(z.string().describe('The source image url'))
    .optional()
    .describe('One or more source images'),
  size: z
    .enum(['auto', 'square', 'portrait', 'landscape'])
    .default('auto')
    .describe('The size of the image to generate'),
  background: z
    .enum(['transparent', 'opaque', 'auto'])
    .default('auto')
    .describe('The background of the image to generate'),
  parent_board_group_id: z.string().optional().describe('Optional. Board group ID'),
});

type ImageGenerateParameters = z.infer<typeof ImageGenerateParameters>;

const ImageGenerateDescription = `use this tool to generate or edit an image given one or more source images in PNG format. Create an image snip if explicitly requested by the user.`;

@Injectable()
export class ImageGenerateService extends BaseToolService {
  private readonly logger = new Logger(ImageGenerateService.name);

  readonly toolName: ToolNames = ToolNames.IMAGE_GENERATE;
  readonly toolDescription = ImageGenerateDescription;
  readonly toolParameters = ImageGenerateParameters;
  readonly toolExecutionTimeout = 120000; // 2 minutes for image generation
  readonly maxCalls = 3;

  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
  ) {
    super(queryBus, commandBus);
  }

  async execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { parsedParams, userId, chat, completionBlock } = parameters;
    const {
      prompt,
      title = 'Image',
      size = 'landscape',
      background = 'auto',
      source_image_urls: parsed_source_image_urls,
      parent_board_group_id: common_parent_board_group_id,
    } = parsedParams;
    const common_board_id = chat.boardId;

    if (!prompt) {
      return {
        response: 'Invalid prompt',
      };
    }

    const createSnip = chat.isInsideNewBoardWorkflow();
    const lastUserMessage = chat.getLastUserMessage();

    const toolData = lastUserMessage?.tools?.[ToolNames.IMAGE_GENERATE];
    const style = toolData?.style;
    const quality = toolData?.quality || 'medium';

    let source_image_urls = parsed_source_image_urls as unknown as string[];
    const references = (lastUserMessage?.atReferences || []).filter(
      ({ entityType }) => entityType === MessageAtReferenceTypeEnum.SNIP,
    );
    if (references.length) {
      // 使用新的查询处理器获取图片URLs
      const entityIds = references.map(({ entityId }) => entityId);
      const reference_image_urls = await this.queryBus.execute(
        new GetImageUrlsFromSnipsQuery(entityIds),
      );

      if (reference_image_urls.length > 0) {
        source_image_urls = reference_image_urls;
      }
    }
    source_image_urls = source_image_urls.map((url) => {
      if (!url.includes('cdn.gooo.ai')) return url;
      return `${url.replace(/@.*$/, '').replace(/\.(png|jpg|jpeg|webp|svg)$/, '')}@chat`;
    });

    const imageRunner = new ImageRunner();
    let imageResult: any;
    let image_url: string;

    if (!source_image_urls || source_image_urls.length === 0) {
      // Generate image mode
      const genResult = await imageRunner.generateImage({
        prompt: style ? `${prompt} Use the following style: ${style}` : prompt,
        size: size_map[size as keyof typeof size_map] || '1024x1024',
        quality: quality as 'low' | 'medium' | 'high' | 'auto',
        background: background as 'transparent' | 'opaque' | 'auto',
        n: 1,
      });
      imageResult = genResult;
      image_url = genResult.imageUrl;
    } else {
      // Edit image mode
      // Edit image mode - no mask handling in main tool function (matches youapp)
      const editResult = await imageRunner.editImage(
        source_image_urls[0],
        prompt,
        size_map[size as keyof typeof size_map] || '1024x1024',
        '[]', // Empty masks array - matches youapp behavior
        quality as 'low' | 'medium' | 'high' | 'auto',
      );
      imageResult = editResult;
      image_url = editResult.imageUrl;
    }

    const result: z.infer<typeof MessageImageGenerateResultSchema> = {
      image_urls: [image_url],
      blurhash: imageResult.blurhash,
      width: imageResult.width,
      height: imageResult.height,
      quality,
      history: [
        {
          image_url,
          prompt,
        },
      ],
    };

    if (createSnip) {
      const childSpan = span.span({
        name: 'create-image-snip',
        input: {
          image_url,
          user_id: userId,
          board_id: common_board_id,
          parent_board_group_id: common_parent_board_group_id,
        },
      });

      try {
        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: completionBlock.id,
          data: {
            status: 'processing',
          },
          path: 'tool_result.snip',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>);

        const snipResult = (await this.createImage({
          file: {
            name: title,
            hash: imageResult.hash,
            is_public: true,
          },
          title,
          board_id: common_board_id,
          parent_board_group_id: common_parent_board_group_id,
        })) as any;

        if (snipResult) {
          childSpan.event({
            name: 'create-image-snip-success',
            input: { url: image_url, user_id: userId, snip_id: snipResult?.id },
            output: snipResult,
          });

          const successResult = {
            status: 'success',
            board_id:
              common_board_id ||
              (snipResult?.board_ids && snipResult.board_ids.length > 0
                ? snipResult.board_ids[0]
                : null), // 如果没有提供 common_board_id, 则使用 snip 返回的第一个 board_id
            vo: snipResult,
          };

          result.snip = successResult;
          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: completionBlock.id,
            data: successResult,
            path: 'tool_result.snip',
          } as CompletionStreamReplaceChunk<StreamChunkUnion>);

          childSpan.end();
        }
      } catch (error) {
        childSpan.event({
          name: 'create-image-snip-failed',
          input: {
            image_url,
            user_id: userId,
            error: error instanceof Error ? (error as any)?.name : String(error),
          },
        });
        childSpan.end({
          level: 'ERROR' as any,
          statusMessage: (error as Error).message,
        });
        result.snip = {
          status: 'failed',
        };

        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: completionBlock.id,
          data: {
            status: 'failed',
          },
          path: 'tool_result.snip',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>);
      }
    }

    return {
      response: `Image finished generating, it can be accessed at ${image_url}. Use this URL only as input for other tool calls. This URL will be presented using customized UI component, don't repeat this URL in your response. Doing so will be penalized.`,
      result,
    };
  }

  private async createImage(params: {
    file: { name: string; hash: string; is_public: boolean };
    title: string;
    board_id: string;
    parent_board_group_id?: string;
  }): Promise<any> {
    const fileDto: CreateUploadFileDto = {
      name: params.file.name,
      hash: params.file.hash,
      isPublic: params.file.is_public,
    };

    const command = new CreateImageCommand(
      undefined, // space id will be determined in handler
      params.board_id, // creatorId - using board_id as proxy for user_id
      params.title,
      undefined, // extra
      fileDto,
      undefined, // webpage
      params.board_id,
      params.parent_board_group_id,
      undefined, // chatId
    );

    const imageDto = await this.commandBus.execute(command);
    return {
      id: imageDto.id,
      board_ids: imageDto.boardIds || [params.board_id],
    };
  }
}
