/**
 * Library Search Tool Service - 库搜索工具服务
 * 在YouMind中搜索用户处理的所有材料
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/library_search.ts
 * - apps/youapi/src/domain/chat/tool_call/library-search.service.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import z from 'zod';
import {
  ChatModeEnum,
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  ContextProviderType,
  StreamChunkUnion,
  ToolNames,
} from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';

import { ContextManager } from '../contexts/context.manager';
import { BaseToolService } from './base.service';

// 定义库搜索参数的类型
const LibrarySearchParameters = z.object({
  query: z
    .string()
    .describe(
      'A high quality search query to be used to semantically search in YouMind, convert relative date to concrete date if applicable',
    ),
});

type LibrarySearchParameters = z.infer<typeof LibrarySearchParameters>;

const LibrarySearchDescription = `Search all materials processed by current user in YouMind.
Use this tool only if the user ask to search all the content in YouMind, with clear instructions like "search all boards", "search my library", "search everything", etc,
or stated that use "library search" tool.
If users' query includes "@youmind", you should prioritize using this tool.`;

@Injectable()
export class LibrarySearchService extends BaseToolService {
  private readonly logger = new Logger(LibrarySearchService.name);

  readonly toolName: ToolNames = ToolNames.LIBRARY_SEARCH;
  readonly toolDescription = LibrarySearchDescription;
  readonly toolParameters = LibrarySearchParameters;
  readonly toolExecutionTimeout = 60000; // 1 minute for library search
  readonly toolMaxTokens = 20000;
  readonly maxCalls = 3;

  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
    private readonly contextManager: ContextManager,
  ) {
    super(queryBus, commandBus);
  }

  async execute(
    parameters: ToolFunctionParameters,
    _span: LangfuseSpanClient,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { userId, chat, parsedParams, completionBlock } = parameters;
    const spaceId = await this.getSpaceId(userId);
    const { query } = parsedParams;

    const isNewBoardChat = chat.mode === ChatModeEnum.NEW_BOARD;

    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: 'Searching in YouMind',
      path: 'tool_result.statusMessage',
    } as CompletionStreamChunk<StreamChunkUnion>);

    const contextResult = await this.contextManager.getContexts({
      providerType: ContextProviderType.LIBRARY,
      contextId: 'library',
      options: {
        maxTokens: this.toolMaxTokens,
        method: 'slice',
        userId,
        spaceId,
        extra: {
          query,
          contextType: 'relevant', // semantic search only
        },
      },
    });

    if (contextResult.contextItems.length === 0) {
      return {
        result: {
          query,
          results: [],
          statusMessage: 'No related content found in YouMind',
        },
        response: 'No related content found in YouMind',
      };
    }

    // 发送搜索结果到流
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: contextResult.contextItems,
      path: 'tool_result.results',
    } as CompletionStreamChunk<StreamChunkUnion>);

    const response = `Found ${contextResult.contextItems.length} relevant items in your YouMind library${
      isNewBoardChat ? ', choose wisely to save to board by calling create_snip_by_url tool' : ''
    }: \n\n ${contextResult.contextString}`;

    return {
      response,
      result: {
        query: query,
        results: contextResult.contextItems,
        statusMessage: 'Searched in YouMind',
      },
    };
  }
}
