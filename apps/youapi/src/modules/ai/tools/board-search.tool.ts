/**
 * Board Search Tool Service - Board搜索工具服务
  @Jialiang 改造
  - Board Search 只需要负责当前 Board 的搜索。如果是加入很多 Board 的情况。让模型判断调用很多次 Board Search 工具就好了。
  - 改造思想: 工具调用不关心具体的业务逻辑，尽量只负责执行工具调用，越少的 IF ELSE 越好。
*/

import { Injectable, Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import z from 'zod';
import {
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  CompletionStreamReplaceChunk,
  ContextProviderType,
  IContextItem,
  StreamChunkUnion,
  ToolNames,
} from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { ContextManager } from '@/modules/ai/contexts';
import { BoardDto } from '@/modules/material-mng/dto/board.dto';
import { GetBoardQuery } from '@/modules/material-mng/services/queries/board/get-board.query';
import { BaseToolService } from './base.service';

// 定义板块搜索参数的类型
const BoardSearchParameters = z.object({
  contextType: z
    .enum(['full', 'relevant'])
    .describe(
      "Choose 'full' for a broad overview of a board item or the entire board; choose 'relevant' when only a focused excerpt is needed.",
    ),
  query: z
    .string()
    .describe(
      "Semantic search string. Required for 'relevant'; leave empty ('') when contextType is 'full'.",
    ),
});

type BoardSearchParameters = z.infer<typeof BoardSearchParameters>;

const BoardSearchToolResultSchema = z.object({
  query: z.string(),
  results: z.array(z.any()),
  contextString: z.string().optional(),
});

const BoardSearchDescription = `Use this tool only if the user's request can be answered with content already present in the current board in by board directory structure,
or users' query explicitly mentions the board (e.g. "@[@BOARD_NAME](id:BOARD_ID;type:board)") or board group (e.g. "@[@BOARD_GROUP_NAME](id:BOARD_GROUP_ID;type:board_group)")
and you need extra information from those boards or board groups to fulfill the user's request.

1. Check the board directory first.
- If none of its \`title\` or \`entity_type\` values are topically related to the user's prompt, do not call the tool.
- If at least one item is relevant, build the \`parameters\` object as described below.

2. \`contextType\` rules
- "relevant" – The user asks for a specific fact, explanation, or narrow slice of information.
- "full" – The user wants a broad survey or summary of an entire board item.

3. \`query\` construction
- For "relevant": distill the user prompt into 3-8 core keywords/phrases; resolve pronouns/back-references; replace relative dates ("yesterday") with concrete dates, match the date time format of the board item if applicable.
- For "full": leave \`query\` empty ("")`;

@Injectable()
export class BoardSearchService extends BaseToolService {
  private readonly logger = new Logger(BoardSearchService.name);

  readonly toolName: ToolNames = ToolNames.BOARD_SEARCH;
  readonly toolDescription = BoardSearchDescription;
  readonly toolParameters = BoardSearchParameters;
  readonly toolOutputSchema = BoardSearchToolResultSchema;
  readonly toolExecutionTimeout = 60000; // 1 minute for board search
  readonly maxTokens = 20000; // 20k tokens is the max tokens for the context manager
  readonly maxCalls = 3;

  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
    protected readonly contextManager: ContextManager,
  ) {
    super(queryBus, commandBus);
  }

  async execute(
    parameters: ToolFunctionParameters,
    _span: LangfuseSpanClient,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { userId, chat, parsedParams, completionBlock } = parameters;
    const currentBoardId = chat.getBoardId();
    const spaceId = await this.getSpaceId(userId);
    const currentBoardDTO: BoardDto = await this.queryBus.execute(
      new GetBoardQuery(userId, spaceId, currentBoardId),
    );
    const currentBoardName = currentBoardDTO.name;

    // contextType 来自 LLM 输出的参数，'full' | 'relevant', 一个代表整个 Board 的搜索，一个代表 Board 中某个 Item 的搜索
    const { query, contextType } = parsedParams;

    // 找到需要执行搜索的 Board 和 Board Group，构造出 ContextProvider 的 Command
    // 查看历史对话中是否 Reference 了相关的 bora
    // const lastUserMessage = chat.getLastUserMessage();
    // const atReferences = lastUserMessage?.atReferences;
    // if (!atReferences) {
    //   return { response: 'No context needed' };
    // }

    // let targetBoardIds: string[] = atReferences
    //   .filter((atReference) => atReference.entityType === MessageAtReferenceTypeEnum.BOARD)
    //   .map((atReference) => atReference.entityId);

    // const targetBoardGroupIds: string[] = atReferences
    //   .filter((atReference) => atReference.entityType === MessageAtReferenceTypeEnum.BOARD_GROUP)
    //   .map((atReference) => atReference.entityId);

    // if (targetBoardIds.length === 0) {
    //   if (!chatBoardId) {
    //     return {
    //       response: 'No board context available - this chat is not associated with any board',
    //     };
    //   }

    //   targetBoardIds = [chatBoardId];
    // }
    // const chatBoardName = chat.getBoardName();
    // const hasOtherBoards = targetBoardIds.filter((boardId) => boardId !== chatBoardId).length > 0;
    // const hasOtherBoardGroups = targetBoardGroupIds.length > 0;
    // const searchCopySuffix = hasOtherBoards || hasOtherBoardGroups ? 'in YouMind' : `board: ${chatBoardName}`;

    // const finalAtReferences = targetBoardIds.map((boardId) => ({
    //   at_name: 'board',
    //   entityType: MessageAtReferenceTypeEnum.BOARD,
    //   entityId: boardId,
    // }));

    // if (targetBoardGroupIds.length > 0) {
    //   finalAtReferences.push(
    //     ...targetBoardGroupIds.map((boardGroupId) => ({
    //       at_name: 'board_group',
    //       entityType: MessageAtReferenceTypeEnum.BOARD_GROUP,
    //       entityId: boardGroupId,
    //     })),
    //   );
    // }

    const searchSuffix = `board: ${currentBoardName}`; // previous name: board

    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: `Searching in ${searchSuffix}`, // TBD: 是否可以加上这个信息。
      path: 'tool_result.statusMessage',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    const contextResult: { contextItems: IContextItem[]; contextString: string } =
      await this.contextManager.getContexts({
        providerType: ContextProviderType.BOARD,
        contextId: currentBoardId,
        options: {
          maxTokens: this.maxTokens ?? 20000,
          method: 'slice',
          userId,
          spaceId,
          extra: {
            query,
            contextType,
          },
        },
      });

    if (contextResult.contextItems.length === 0) {
      return {
        result: {
          query,
          results: [],
          statusMessage: `No related content found ${searchSuffix}`,
        },
        response: `No related content found ${searchSuffix}`,
      };
    }

    return {
      response: `Found ${contextResult.contextItems.length} relevant items in the board. ${contextResult.contextString}`,
      result: {
        query,
        results: contextResult.contextItems,
        statusMessage: `Searched ${searchSuffix}`,
      },
    };
  }
}
