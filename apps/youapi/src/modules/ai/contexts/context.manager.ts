import { Injectable, Logger } from '@nestjs/common';
import { DiscoveryService, ModuleRef } from '@nestjs/core';
import { AtReference, MessageAtReferenceTypeEnum } from '@/common/types';
import {
  BatchContextOptions,
  ContainerContentItem,
  ContextBuildingFromReferencesRequest,
  ContextBuildingResult,
  ContextBuildingStrategy,
  ContextOptions,
  ContextProviderRequest,
  ContextProviderType,
  IContextItem,
  IContextProvider,
  MagicCreationContext,
  RetrievedContent,
} from '@/common/types/context.type';
import { estimateTokens, runConcurrently } from '@/common/utils';
import { LLMMessageAnalyzer } from './analyzer';
import { ContentRetrieverService } from './services/content.retriever';

/**
 * ContextManager - 统一的上下文管理入口
 */
@Injectable()
export class ContextManager {
  protected readonly logger = new Logger(ContextManager.name);

  private readonly providers = new Map<string, IContextProvider>();
  private analyzer: LLMMessageAnalyzer;

  constructor(
    private readonly moduleRef: ModuleRef,
    private readonly discoveryService: DiscoveryService,
    private readonly contentRetriever: ContentRetrieverService,
  ) {
    this.registerProviders();
    // TODO: Temporarily disabled until LLMMessageAnalyzer is implemented
    // this.analyzer = this.moduleRef.get(LLMMessageAnalyzer);
  }

  private registerProviders(): void {
    try {
      this.autoRegisterProviders();
    } catch (error) {
      this.logger.error('Failed to register providers:', error);
    }
  }

  private autoRegisterProviders(): void {
    try {
      // 使用 DiscoveryService 发现所有带有 @ContextProvider 装饰器的类
      const providers = this.discoveryService.getProviders();

      for (const provider of providers) {
        try {
          // 检查是否是 context provider 类
          if (provider.metatype && this.hasContextProviderDecorator(provider.metatype)) {
            // 通过 ModuleRef 获取真正的实例，使用 strict: true 确保获取容器中的实例
            const instance = this.moduleRef.get(provider.metatype, { strict: true });
            if (instance && typeof instance.getProviderType === 'function') {
              const providerType = instance.getProviderType();
              if (providerType) {
                this.providers.set(providerType, instance);
                this.logger.debug(
                  `[ContextManager] Auto-registered context provider: ${providerType} (${provider.metatype.name})`,
                );
              }
            }
          }
        } catch (error) {
          // 忽略单个 provider 的错误，继续处理其他 provider
          this.logger.debug(
            `[ContextManager] Skipped provider ${provider.metatype?.name}: ${error.message}`,
          );
        }
      }
    } catch (error) {
      this.logger.error('Failed to auto-register providers:', error);
    }
  }

  /**
   * 检查类是否有 @ContextProvider 装饰器
   */
  private hasContextProviderDecorator(metatype: any): boolean {
    try {
      const metadata = Reflect.getMetadata('context:provider:type', metatype);
      return !!metadata;
    } catch (error) {
      return false;
    }
  }

  public getProvider(type: string): IContextProvider | undefined {
    const provider = this.providers.get(type);
    if (!provider) {
      this.logger.error(`No provider registered for type: ${type}`);
      return undefined;
    }
    return provider;
  }

  public hasProvider(type: string): boolean {
    return this.providers.has(type);
  }

  public getRegisteredTypes(): string[] {
    return Array.from(this.providers.keys());
  }

  /**
   * 获取上下文重要接口，调用者
   * 1. ToolCall: library search, board search, google search.
   * 2. @Jialiang To be finished.
   * @param contextRequest 上下文请求
   * @returns contextItems: 上下文项列表, contextString: 上下文字符串
   */
  async getContexts(
    contextRequest: ContextProviderRequest,
  ): Promise<{ contextItems: IContextItem[]; contextString: string }> {
    const provider = this.providers.get(contextRequest.providerType);
    if (!provider) {
      const errorMessage = `No provider found for source type: ${contextRequest.providerType}`;
      this.logger.error(errorMessage); // no throw here, just log it.
      return { contextItems: [], contextString: errorMessage };
    }

    const result = await provider.getContexts(contextRequest.contextId, contextRequest.options);
    this.logger.debug(
      `[ContextManager] getContexts ${contextRequest.providerType}: return ${result.contextItems.length} items`,
    );
    this.logger.debug(
      `[ContextManager] getContexts contentString: ${result.contextString.substring(0, 100)}...`,
    );
    return result;
  }

  public async getContextItems(contextRequest: ContextProviderRequest): Promise<IContextItem[]> {
    const provider = this.providers.get(contextRequest.providerType);
    if (!provider) {
      this.logger.error(
        `No provider found for source type: ${contextRequest.providerType}, contextId: ${contextRequest.contextId}`,
      );
      // throw new Error(`No provider found for source type: ${contextRequest.providerType}`);
      return [];
    }

    // TODO add try catch and timeout and logs here
    const result = await provider.getContextItems(
      contextRequest.contextId,
      contextRequest?.options,
    );
    this.logger.debug(
      `[ContextManager] getContextItems ${contextRequest.providerType}: return ${result.length} items`,
    );
    return result;
  }

  public async getProviderContextString(contextRequest: ContextProviderRequest): Promise<string> {
    const provider = this.providers.get(contextRequest.providerType);
    if (!provider) {
      throw new Error(`No provider found for source type: ${contextRequest.providerType}`);
    }
    return await provider.getContextString(contextRequest.contextId, contextRequest.options);
  }

  public async getBatchProviderContextString(
    contextRequests: ContextProviderRequest[],
  ): Promise<string[]> {
    const tasks = contextRequests.map(async (contextRequest) => {
      return await this.getProviderContextString(contextRequest);
    });
    return await Promise.all(tasks);
  }

  /**
   * Magic Creation 批量上下文获取
   */
  public async getBatchMagicCreationContext(
    contextRequests: ContextProviderRequest[],
    _batchOptions?: BatchContextOptions,
  ): Promise<MagicCreationContext[]> {
    // TODO: Jialiang 确认下返回值
    // 并发处理，最多 20 个源
    const tasks = contextRequests.slice(0, 20).map(async (contextRequest) => {
      const provider = this.providers.get(contextRequest.providerType);
      if (!provider) {
        this.logger.warn(`No provider found for source type: ${contextRequest.providerType}`);
        return null;
      }

      try {
        return await provider.getMagicCreationContext(
          contextRequest.contextId,
          contextRequest.options,
        );
      } catch (error) {
        this.logger.error(
          `Failed to get magic creation context for ${contextRequest.contextId}:`,
          error,
        );
        return null;
      }
    });

    // TODO: may add some batch operations here. e.g. token limit, etc.

    const contexts = await Promise.all(tasks);
    return contexts.filter((ctx): ctx is MagicCreationContext => ctx !== null);
  }

  private isSimpleProvider(entityType: MessageAtReferenceTypeEnum) {
    return (
      entityType === MessageAtReferenceTypeEnum.SNIP ||
      entityType === MessageAtReferenceTypeEnum.THOUGHT
    );
  }

  private getSimpleEntityContextProvider(entityType: MessageAtReferenceTypeEnum): IContextProvider {
    const contextProvider = this.providers.get(entityType);

    if (!contextProvider) {
      throw new Error(`Context provider not found for entity type: ${entityType}`);
    }

    return contextProvider;
  }

  private async getFullContentSamples(
    atReferences: AtReference[],
    availableTokens: number,
  ): Promise<RetrievedContent[]> {
    const sampledContents = await runConcurrently(
      atReferences.map((ref) => () => {
        const contextBuilder = this.getSimpleEntityContextProvider(ref.entity_type);
        return contextBuilder.getContextString(ref.entity_id, {
          userId: 'debug-user',
          spaceId: 'debug-space',
          method: 'sample',
          maxTokens: Math.floor(availableTokens / atReferences.length),
        });
      }),
    );

    return sampledContents.map((content, index) => {
      const ref = atReferences[index];
      return {
        atReferenceId: ref.entity_id,
        atReferenceType: ref.entity_type,
        content: content || '',
        tokenCount: estimateTokens(content || ''),
        chunks: [],
      };
    });
  }

  private async fetchContainerContentItems(
    containerId: string,
    entityType: MessageAtReferenceTypeEnum,
  ): Promise<ContainerContentItem[]> {
    console.log(
      `🔍 DEBUG: fetchContainerContentItems called with containerId: ${containerId}, entityType: ${entityType}`,
    );

    try {
      // Get the appropriate provider for the container type
      const provider = this.providers.get(entityType as unknown as ContextProviderType);
      if (!provider) {
        console.log(`⚠️ DEBUG: No provider found for type: ${entityType}`);
        return [];
      }

      // For board containers, get sub-items
      if (entityType === MessageAtReferenceTypeEnum.BOARD && 'getContextSubItems' in provider) {
        console.log(`📋 DEBUG: Getting context sub-items for board: ${containerId}`);
        const subItems = await provider.getContextSubItems('debug-user', containerId, {
          userId: 'debug-user',
          spaceId: 'debug-space',
          maxTokens: 1000,
        });

        return subItems.map((item) => ({
          type: item.type as MessageAtReferenceTypeEnum,
          title: item.title || 'Untitled Item',
        }));
      }

      // For other container types, return basic info
      console.log(`📄 DEBUG: Creating basic container content for type: ${entityType}`);
      return [
        {
          type: entityType,
          title: `Container ${containerId}`,
        },
      ];
    } catch (error) {
      console.error(`❌ DEBUG: Error in fetchContainerContentItems:`, error);
      return [];
    }
  }

  async buildLibraryContext(
    _request: ContextBuildingFromReferencesRequest,
  ): Promise<ContextBuildingResult> {
    const libraryProvider = this.providers.get(ContextProviderType.LIBRARY);
    if (!libraryProvider) {
      throw new Error('Library provider not found');
    }

    // TODO: 实现 library 级别的上下文构建
    return {
      atReferencesRetrieved: [],
      totalTokensUsed: 0,
      strategy: ContextBuildingStrategy.NO_CONTEXT,
    };
  }

  /**
   * Web Search 语义搜索上下文构建
   * TODO: 直接使用 getContextItems 实现就好了
   * /


  /**
   *  Library Only Contexts
   * 现在委托给 LibraryContextProvider 处理
   */
  async buildLibraryOnlyContext(
    request: ContextBuildingFromReferencesRequest,
  ): Promise<ContextBuildingResult> {
    const { searchKeywords, contextType } = request.analysis || {};

    // 如果模型判断需要召回整个 library 里的内容，则无能为力
    if (contextType === 'full' || !searchKeywords || searchKeywords.length === 0) {
      return {
        atReferencesRetrieved: [],
        totalTokensUsed: 0,
        strategy: ContextBuildingStrategy.NO_CONTEXT,
      };
    }

    // 委托给 LibraryContextProvider 处理
    const libraryProvider = this.providers.get(ContextProviderType.LIBRARY);
    if (!libraryProvider) {
      this.logger.error('LibraryContextProvider not found');
      return {
        atReferencesRetrieved: [],
        totalTokensUsed: 0,
        strategy: ContextBuildingStrategy.NO_CONTEXT,
      };
    }

    // 使用统一的处理函数
    return await this.processContextItemsToResult(
      {
        providerType: ContextProviderType.LIBRARY,
        contextId: 'library',
        options: {
          maxTokens: request.availableTokens,
          userId: request.userId,
          extra: {
            searchKeywords,
            contextType,
          },
        },
      },
      ContextBuildingStrategy.SEMANTIC_SEARCH,
      searchKeywords,
    );
  }

  // /**
  //  * Get the full content of an entity
  //  * @param entityId Entity ID
  //  * @param entityType Entity type
  //  * @returns Retrieved content
  //  */
  // private async getFullEntityContent(
  //   entityId: string,
  //   entityType: MessageAtReferenceTypeEnum,
  // ): Promise<RetrievedContent> {
  //   if (!this.isSimpleProvider(entityType)) {
  //     throw new Error(`Unsupported entity type for full content: ${entityType}`);
  //   }

  //   return {
  //     atReferenceId: entityId,
  //     atReferenceType: entityType,
  //     content: '',
  //     tokenCount: 0,
  //     chunks: [],
  //   };
  // }

  /**
   * @ 引用上下文构建
   */
  // async buildReferenceContexts(
  //   request: ContextBuildingFromReferencesRequest,
  // ): Promise<ContextBuildingResult> {
  //   console.log('context manager buildContextFromReferences');
  //   const historyMessages: Message[] = request.messages as Message[];
  //   const trace = request.trace;
  //   const atReferences: AtReferenceForContextBuilding[] = request.at_references;

  //   // 0. 如果是整个 library 级别的召回，走单独的逻辑
  //   const isLibraryLevel = atReferences.every((ref) => ref.entity_type === 'library');
  //   if (isLibraryLevel) {
  //     return await this.buildLibraryContext(request);
  //   }

  //   // solve simple entities (snip, thought) fully
  //   const allSimpleEntities = atReferences.every((ref) => contextUtils.isSimpleEntity(ref));
  //   if (allSimpleEntities) {
  //     // TypeScript now knows that all refs in atReferences are AtReference with simple entity types
  //     const simpleReferences = atReferences as AtReference[];
  //     try {
  //       trace?.event({
  //         name: 'attempting-simple-entities-full-content',
  //         metadata: {
  //           reference_count: atReferences.length,
  //           available_tokens: request.availableTokens,
  //         },
  //       });

  //       // 获取所有简单实体的完整内容
  //       const fullContents = await Promise.all(
  //         simpleReferences.map((ref) => this.getFullEntityContent(ref.entity_id, ref.entity_type)),
  //       );

  //       const totalTokenCount = fullContents.reduce((sum, content) => sum + content.tokenCount, 0);

  //       if (totalTokenCount <= request.availableTokens) {
  //         trace?.event({
  //           name: 'simple-entities-full-content-fit',
  //           metadata: {
  //             total_token_count: totalTokenCount,
  //             available_tokens: request.availableTokens,
  //           },
  //         });

  //         return {
  //           atReferencesRetrieved: fullContents.map((content, index) => ({
  //             atReferenceId: content.atReferenceId,
  //             atReferenceType: content.atReferenceType,
  //             chunks: [
  //               {
  //                 id: content.atReferenceId,
  //                 type: content.atReferenceType,
  //                 chunk: content.content,
  //                 distance: 0,
  //                 tokenUsed: content.tokenCount,
  //                 title: simpleReferences[index]?.at_name || '',
  //               },
  //             ],
  //           })),
  //           totalTokensUsed: totalTokenCount,
  //           strategy: ContextBuildingStrategy.FULL_CONTENT,
  //         };
  //       } else {
  //         trace?.event({
  //           name: 'simple-entities-full-content-exceeds-limit',
  //           metadata: {
  //             total_token_count: totalTokenCount,
  //             available_tokens: request.availableTokens,
  //           },
  //         });
  //       }
  //     } catch (error) {
  //       console.error('Error getting full content for simple entities:', error);
  //     }
  //   }

  //   // 2. Prepare container contents if we have board group or board references
  //   const containerRefs = atReferences.filter(
  //     (ref): ref is AtReference =>
  //       contextUtils.isAtReference(ref) &&
  //       (ref.entity_type === MessageAtReferenceTypeEnum.BOARD_GROUP ||
  //         ref.entity_type === MessageAtReferenceTypeEnum.BOARD),
  //   );
  //   const boardRefs = atReferences.filter(
  //     (ref): ref is AtReference =>
  //       contextUtils.isAtReference(ref) && ref.entity_type === MessageAtReferenceTypeEnum.BOARD,
  //   );
  //   const finalContainerRefs = [...boardRefs, ...containerRefs];

  //   let containerContents: Record<string, ContainerContentItem[]> | undefined;
  //   if (finalContainerRefs.length > 0 && !request.quickMode) {
  //     const span = trace?.span({
  //       name: 'fetch-container-contents',
  //       input: finalContainerRefs,
  //     });
  //     containerContents = {};

  //     await Promise.all(
  //       finalContainerRefs.map(async (ref) => {
  //         try {
  //           const contentItems = await this.fetchContainerContentItems(
  //             ref.entity_id,
  //             ref.entity_type,
  //           );
  //           containerContents![ref.entity_id] = contentItems;
  //         } catch (error) {
  //           console.error(
  //             `Error fetching content items for ${ref.entity_type} ${ref.entity_id}:`,
  //             error,
  //           );
  //           containerContents![ref.entity_id] = [];
  //         }
  //       }),
  //     );

  //     span?.end({
  //       output: containerContents,
  //     });
  //   }

  //   // 3. Analyze the message with container contents or use provided analysis
  //   // TODO: Temporarily use mock analysis until LLMMessageAnalyzer is implemented
  //   const analysis = request.analysis || {
  //     needsContext: atReferences.length > 0,
  //     searchKeywords: 'mock search keywords',
  //     contextType: 'full' as const,
  //   };

  //   // 4. If no context needed, return empty result
  //   if (!analysis.needsContext) {
  //     trace?.event({
  //       name: 'no-context-needed',
  //     });
  //     return {
  //       atReferencesRetrieved: [],
  //       totalTokensUsed: 0,
  //       strategy: ContextBuildingStrategy.NO_CONTEXT,
  //     };
  //   } else if (analysis.contextType === 'full') {
  //     // 当需要完整内容时，优先使用完整内容采样，无论是否有容器引用
  //     trace?.event({
  //       name: 'full-content-sampling',
  //     });

  //     const validAtReferences = atReferences.filter(contextUtils.isAtReference);

  //     // 只对简单实体类型（snip, thought）使用完整内容采样
  //     // 复杂实体类型（board, board_group）仍然使用检索流程
  //     const simpleReferences = validAtReferences.filter(contextUtils.isSimpleEntity);
  //     const complexReferences = validAtReferences.filter(
  //       (ref) => !contextUtils.isSimpleEntity(ref),
  //     );

  //     const results: Array<{
  //       atReferenceId: string;
  //       atReferenceType: string;
  //       chunks: Array<ContextBuildingChunk>;
  //     }> = [];
  //     let totalTokensUsed = 0;

  //     // 处理简单实体类型的完整内容采样
  //     if (simpleReferences.length > 0) {
  //       const availableTokensForSimple: number =
  //         complexReferences.length > 0
  //           ? Math.floor(request.availableTokens * 0.4) // 为复杂实体留60%的token
  //           : request.availableTokens;

  //       const sampledContents = await this.getFullContentSamples(
  //         simpleReferences,
  //         availableTokensForSimple,
  //       );

  //       results.push(
  //         ...sampledContents.map((content) => ({
  //           atReferenceId: content.atReferenceId,
  //           atReferenceType: content.atReferenceType,
  //           chunks: [
  //             {
  //               id: content.atReferenceId,
  //               type: content.atReferenceType,
  //               chunk: content.content,
  //               distance: 0,
  //               tokenUsed: content.tokenCount,
  //             },
  //           ],
  //         })),
  //       );

  //       totalTokensUsed += sampledContents.reduce((sum, content) => sum + content.tokenCount, 0);
  //     }

  //     // 处理复杂实体类型的检索 - 对于full context，直接获取完整内容而不是语义搜索
  //     const remainingTokens = request.availableTokens - totalTokensUsed;

  //     if (complexReferences.length <= 0 || remainingTokens <= 0) {
  //       return {
  //         atReferencesRetrieved: [],
  //         totalTokensUsed,
  //         strategy: ContextBuildingStrategy.FULL_CONTENT_SAMPLING,
  //       };
  //     }

  //     // 对于复杂实体类型（board/board_group），获取其内部项目的完整内容
  //     const tokensPerComplexRef = Math.floor(remainingTokens / complexReferences.length);
  //     const boardProvider = this.providers.get(ContextProviderType.BOARD) as BoardContextProvider;

  //     const boardGroupProvider = this.providers.get(
  //       ContextProviderType.BOARD_GROUP,
  //     ) as BoardGroupContextProvider;

  //     const complexResults = await Promise.all(
  //       complexReferences.map(async (ref: AtReference) => {
  //         try {
  //           if (
  //             ref.entity_type !== MessageAtReferenceTypeEnum.BOARD &&
  //             ref.entity_type !== MessageAtReferenceTypeEnum.BOARD_GROUP
  //           ) {
  //             return null;
  //           }

  //           const contextProvider = this.providers.get(ref.entity_type);
  //           if (!contextProvider) {
  //             return null;
  //           }
  //           const boardItems: IContextSubItem[] = await contextProvider.getContextSubItems(
  //             request.userId,
  //             ref.entity_id,
  //           );

  //           // 过滤并获取简单实体（snip, thought）的内容
  //           const simpleItems = boardItems.filter(
  //             (item) => item.type === SourceTypeEnum.SNIP || item.type === SourceTypeEnum.THOUGHT,
  //           );

  //           if (simpleItems.length === 0) {
  //             const contextProvider =
  //               ref.entity_type === MessageAtReferenceTypeEnum.BOARD
  //                 ? boardProvider
  //                 : boardGroupProvider;
  //             const content = await contextProvider.getContextString(ref.entity_id);
  //             return {
  //               atReferenceId: ref.entity_id,
  //               atReferenceType: ref.entity_type,
  //               chunks: [
  //                 {
  //                   id: ref.entity_id,
  //                   type: ref.entity_type,
  //                   chunk: content,
  //                   distance: 0,
  //                   tokenUsed: estimateTokens(content),
  //                 },
  //               ],
  //               tokenCount: estimateTokens(content),
  //             };
  //           }

  //           // 为每个简单实体分配token
  //           const tokensPerItem = Math.floor(tokensPerComplexRef / simpleItems.length);

  //           // 并发获取所有简单实体的内容
  //           // TODO: jialiang, 需要优化，并发获取所有简单实体的内容
  //           const itemContents = await runConcurrently(
  //             simpleItems.map((item) => async () => {
  //               try {
  //                 const contextProvider =
  //                   item.type === SourceTypeEnum.SNIP
  //                     ? this.providers.get(ContextProviderType.SNIP)
  //                     : this.providers.get(ContextProviderType.THOUGHT);
  //                 const content = await contextProvider.getContextString(item.id, {
  //                   method: 'sample',
  //                   maxTokens: tokensPerItem,
  //                 });
  //                 return {
  //                   id: item.id,
  //                   type: item.type,
  //                   chunk: content || '',
  //                   tokenUsed: estimateTokens(content || ''),
  //                 };
  //               } catch (error) {
  //                 console.error(`Error getting content for ${item.type} ${item.id}:`, error);
  //                 return null;
  //               }
  //             }),
  //           );

  //           const validContents = itemContents.filter((content) => content !== null);
  //           const totalTokenCount = validContents.reduce(
  //             (sum, content) => sum + content.tokenUsed,
  //             0,
  //           );

  //           return {
  //             atReferenceId: ref.entity_id,
  //             atReferenceType: ref.entity_type,
  //             chunks: validContents.map((content) => ({
  //               id: content.id,
  //               type: content.type,
  //               chunk: content.chunk,
  //               distance: 0,
  //               tokenUsed: content.tokenUsed,
  //             })),
  //             tokenCount: totalTokenCount,
  //           };
  //         } catch (error) {
  //           console.error(
  //             `Error getting board items for ${ref.entity_type} ${ref.entity_id}:`,
  //             error,
  //           );
  //           return null;
  //         }
  //       }),
  //     );

  //     const validComplexResults = complexResults.filter(
  //       (result: (typeof complexResults)[0]): result is NonNullable<typeof result> =>
  //         result !== null,
  //     );

  //     results.push(
  //       ...validComplexResults.map((content: NonNullable<(typeof complexResults)[0]>) => ({
  //         atReferenceId: content.atReferenceId,
  //         atReferenceType: content.atReferenceType,
  //         chunks: content.chunks,
  //       })),
  //     );

  //     totalTokensUsed += validComplexResults.reduce(
  //       (sum: number, content: NonNullable<(typeof complexResults)[0]>) => sum + content.tokenCount,
  //       0,
  //     );

  //     return {
  //       atReferencesRetrieved: results,
  //       totalTokensUsed,
  //       strategy: ContextBuildingStrategy.FULL_CONTENT_SAMPLING,
  //     };
  //   }

  //   // 5. Retrieve content from references using semantic search
  //   const span = trace?.span({
  //     name: 'retrieve-content',
  //     input: {
  //       at_references: atReferences,
  //       search_keywords: analysis.searchKeywords,
  //     },
  //   });
  //   const validAtReferencesForRetrieval = atReferences.filter(contextUtils.isAtReference);
  //   const retrievedContent = await this.contentRetriever.retrieveContent(
  //     validAtReferencesForRetrieval,
  //     analysis.searchKeywords,
  //     request.userId,
  //     request.availableTokens,
  //     span,
  //   );
  //   span?.end({
  //     output: retrievedContent,
  //   });

  //   // 6. Calculate total tokens used
  //   const totalTokensUsed = retrievedContent.reduce((sum, content) => sum + content.tokenCount, 0);

  //   // 7. Return the result
  //   const result: ContextBuildingResult = {
  //     atReferencesRetrieved: retrievedContent.map((content) => ({
  //       atReferenceId: content.atReferenceId,
  //       atReferenceType: content.atReferenceType,
  //       chunks: content.chunks,
  //     })),
  //     totalTokensUsed,
  //     strategy: ContextBuildingStrategy.SEMANTIC_SEARCH,
  //     searchKeywords: analysis.searchKeywords || '',
  //   };

  //   return result;
  // }

  /**
   * 统一的上下文项处理函数
   * 调用 getContextItems 并转换为 ContextBuildingResult 格式
   */
  private async processContextItemsToResult(
    contextRequest: ContextProviderRequest,
    strategy: ContextBuildingStrategy,
    searchKeywords?: string,
  ): Promise<ContextBuildingResult> {
    try {
      const contextItems = await this.getContextItems(contextRequest);

      if (contextItems.length === 0) {
        return {
          atReferencesRetrieved: [],
          totalTokensUsed: 0,
          strategy,
          searchKeywords,
        };
      }

      // 将 IContextItem[] 转换为 ContextBuildingResult 格式
      const atReferencesRetrieved = contextItems.map((item) => ({
        atReferenceId: item.id,
        atReferenceType: item.type,
        chunks: [
          {
            id: item.id,
            type: item.type,
            chunk: item.content,
            distance: (item.extra?.relevance as number) || 0,
            tokenUsed: (item.extra?.tokenUsed as number) || estimateTokens(item.content),
            title: item.title,
          },
        ],
      }));

      const totalTokensUsed = contextItems.reduce(
        (sum, item) => sum + ((item.extra?.tokenUsed as number) || estimateTokens(item.content)),
        0,
      );

      return {
        atReferencesRetrieved,
        totalTokensUsed,
        strategy,
        searchKeywords,
      };
    } catch (error) {
      this.logger.error('Error in processContextItemsToResult:', error);
      return {
        atReferencesRetrieved: [],
        totalTokensUsed: 0,
        strategy: ContextBuildingStrategy.NO_CONTEXT,
        searchKeywords,
      };
    }
  }

  /**
   * 根据实体类型获取上下文
   */
  async getContextByEntity(
    entityType: string,
    entityId: string,
    options?: ContextOptions,
  ): Promise<any | null> {
    const provider = this.providers.get(entityType);
    if (!provider) {
      throw new Error(`No provider found for entity type: ${entityType}`);
    }
    return provider.getContextString(entityId, options);
  }

  /**
   * 根据实体类型获取上下文字符串
   */
  async getContextStringByEntity(
    entityType: string,
    entityId: string,
    options?: ContextOptions,
  ): Promise<string> {
    const provider = this.providers.get(entityType);
    if (!provider) {
      throw new Error(`No provider found for entity type: ${entityType}`);
    }
    return provider.getContextString(entityId, options);
  }

  /**
   * 批量获取上下文 - 按类型分组并发处理
   */
  async getBatchContext(
    requests: Array<{ type: string; id: string; options?: ContextOptions }>,
  ): Promise<Map<string, any>> {
    const results = new Map<string, any>();

    // 按类型分组
    const groupedRequests = new Map<string, Array<{ id: string; options?: ContextOptions }>>();

    for (const request of requests) {
      if (!groupedRequests.has(request.type)) {
        groupedRequests.set(request.type, []);
      }
      groupedRequests.get(request.type)!.push({ id: request.id, options: request.options });
    }

    // 并发处理每种类型
    const promises = Array.from(groupedRequests.entries()).map(async ([type, typeRequests]) => {
      const provider = this.providers.get(type);
      if (!provider) {
        this.logger.warn(`No provider found for type: ${type}`);
        return new Map<string, any>();
      }

      const typeResults = new Map<string, any>();
      for (const { id, options } of typeRequests) {
        try {
          const result = await provider.getContextString(id, options);
          if (result) {
            typeResults.set(`${type}:${id}`, result);
          }
        } catch (error) {
          this.logger.error(`Failed to get context for ${type}:${id}:`, error);
        }
      }
      return typeResults;
    });

    const allResults = await Promise.all(promises);
    allResults.forEach((typeResults) => {
      typeResults.forEach((value, key) => results.set(key, value));
    });

    return results;
  }
}
