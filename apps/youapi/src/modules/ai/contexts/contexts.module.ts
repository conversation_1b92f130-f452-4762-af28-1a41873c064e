/**
 * Contexts Module - 上下文模块
 *
 * 统一管理所有上下文相关的服务
 */

import { forwardRef, Module } from '@nestjs/common';
import { DiscoveryModule } from '@nestjs/core';
import { CqrsModule } from '@nestjs/cqrs';

// Import domain modules for dependencies
import { DomainModule } from '@/domain/domain.module';
import { WebSearchDomainService } from '@/domain/web-search';
import { ExternalSearchDomainService } from '@/domain/web-search/external-search';
import { InternalSearchDomainService } from '@/domain/web-search/internal-search';
import { InfraModule } from '@/infra/infra.module';
// Import context services
import { ContextManager } from './context.manager';
import { BoardContextProvider } from './providers/board-context.provider';
import { BoardGroupContextProvider } from './providers/board-group-context.provider';
import { LibraryContextProvider } from './providers/library-context.provider';
// Import providers
import { SnipContextProvider } from './providers/snip-context.provider';
import { ThoughtContextProvider } from './providers/thought-context.provider';
import { WebpageContextProvider } from './providers/webpage-context.provider';
import { ContentRetrieverService } from './services/content.retriever';

@Module({
  imports: [
    CqrsModule,
    DiscoveryModule, // For auto-discovery of context providers
    DomainModule,
    // forwardRef(()=> ), // For accessing existing domain services
    forwardRef(() => InfraModule), // For accessing infrastructure services
  ],
  providers: [
    // Context services
    ContextManager,
    ContentRetrieverService,

    // Context providers
    SnipContextProvider,
    ThoughtContextProvider,
    BoardContextProvider,
    BoardGroupContextProvider,
    WebpageContextProvider,
    LibraryContextProvider,

    // TODO should be removed later
    InternalSearchDomainService,
    ExternalSearchDomainService,
    WebSearchDomainService,
  ],
  exports: [
    // Export the main service for use by other modules
    ContextManager,
    ContentRetrieverService,

    // Export providers if needed
    SnipContextProvider,
    ThoughtContextProvider,
    BoardContextProvider,
    BoardGroupContextProvider,
    WebpageContextProvider,
    LibraryContextProvider,
  ],
})
export class ContextsModule {}
