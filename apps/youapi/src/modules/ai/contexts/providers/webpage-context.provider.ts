/**
 * Webpage Context Provider - 网页上下文提供者
 *
 * 使用 WebSearchDomainService 获取网页内容作为上下文
 *
 * 重构为使用 CQRS 与 web-search 服务交互
 *
 * 迁移自:
 * - youapi/src/domain/llm/context/webpage.ts
 */

import { forwardRef, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LangfuseTraceService } from '@/common/services/langfuse-trace.service';
import { type InternetSearchResult } from '@/common/types/chat.types';
import {
  ChatContext,
  ContextOptions,
  ContextProviderType,
  IContextItem,
  MagicCreationContext,
  MagicCreationOptions,
} from '@/common/types/context.type';
import { WebSearchDomainService } from '@/domain/web-search';
import { WebSearchParams } from '@/domain/web-search/types';
import { BaseContextProvider, ContextProvider } from './abstract.provider';

@Injectable()
@ContextProvider(ContextProviderType.WEBPAGE)
export class WebpageContextProvider extends BaseContextProvider {
  private readonly logger = new Logger(WebpageContextProvider.name);

  constructor(
    public readonly queryBus: QueryBus,
    public readonly commandBus: CommandBus,
    @Inject(forwardRef(() => WebSearchDomainService))
    public readonly webSearchDomainService: WebSearchDomainService,
    public readonly traceService: LangfuseTraceService,
  ) {
    super(queryBus, commandBus);
  }

  /**
   * 重写默认选项，为 Webpage 提供者定制默认配置
   */
  getDefaultContextOptions(): Partial<ContextOptions> {
    return {
      // 使用采样方法保持内容结构
    };
  }

  /**
   * 使用 WebSearchDomainService 获取网页数据
   */
  async getContextItems(_contextId: string, options?: ContextOptions): Promise<IContextItem[]> {
    try {
      const searchParams = {
        query: options?.extra?.query || '',
        type: options?.extra?.type || 'webpage',
        language: options?.extra?.language || 'en',
        location: options?.extra?.location || 'US',
        event_filter: options?.extra?.event_filter || 'all',
        freshness: options?.extra?.freshness || 'all',
        multilingual_queries: options?.extra?.multilingual_queries || [],
        chat: options?.extra?.chat || undefined,
        limit: options?.extra?.limit || undefined,
      } as WebSearchParams;
      // this.logger.debug('Context Parameters:', searchParams);

      let span = options?.span;
      if (!span) {
        span = await this.traceService.addSpan({
          name: 'webpage-context-provider',
          input: searchParams,
        });
      }

      // TODO: fix internal search
      const searchResults: InternetSearchResult[] = await this.webSearchDomainService.search(
        searchParams,
        span,
      );

      if (!searchResults || searchResults.length === 0) {
        this.logger.debug(`网页未找到: ${options?.extra?.query}`);
        return null;
      }

      return searchResults.map((result) => ({
        id: result.url,
        type: 'webpage',
        title: result.title || '',
        content: result.related_chunk || '',
        url: result.url,
      }));
    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.debug(`网页未找到: ${options?.extra?.query}`);
        return null;
      }
      this.logger.error(`获取网页数据失败: ${options?.extra?.query}`, error);
      throw error;
    }
  }

  /**
   * 获取网页的文本内容
   */
  async getContextString(_url: string, _options?: ContextOptions): Promise<string> {
    // TODO: 实现获取网页文本内容的逻辑
    return '';
  }

  /**
   * 获取聊天优化的上下文
   */
  async getChatContext(
    _url: string,
    _chat: unknown,
    _options?: ContextOptions,
  ): Promise<ChatContext> {
    return null;
  }

  /**
   * 获取 Magic Creation 上下文
   */
  async getMagicCreationContext(
    _url: string,
    _options?: MagicCreationOptions,
  ): Promise<MagicCreationContext> {
    return null;
  }
}
