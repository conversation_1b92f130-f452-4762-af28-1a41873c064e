/**
 * Snip Context Provider - 片段上下文提供者
 *
 * 重构为使用 CQRS 与 material-mng 模块交互
 *
 * 迁移自:
 * - youapi/src/domain/llm/context/snip.ts
 */

import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { sampleText } from '@repo/common';
import {
  ChatContext,
  ContextOptions,
  ContextProviderType,
  IContextItem,
  MagicCreationContext,
  MagicCreationOptions,
} from '@/common/types/context.type';
import { truncateByTokens } from '@/common/utils';
import { SnipType } from '@/modules/material-mng/domain/snip/models/snip.entity';
import { GetSnipQuery } from '@/modules/material-mng/services/queries/snip/get-snip.query';
import { BaseContextProvider, ContextProvider } from './abstract.provider';

@Injectable()
@ContextProvider(ContextProviderType.SNIP)
export class SnipContextProvider extends BaseContextProvider {
  private readonly logger = new Logger(SnipContextProvider.name);

  constructor(
    public readonly queryBus: QueryBus,
    public readonly commandBus: CommandBus,
  ) {
    super(queryBus, commandBus);
  }

  /**
   * 重写默认选项，为 Snip 提供者定制默认配置
   */
  getDefaultContextOptions(): Partial<ContextOptions> {
    return {
      maxTokens: 10000,
      method: 'sample',
    };
  }

  async getContextItem(id: string, _options?: ContextOptions): Promise<IContextItem> {
    try {
      // TODO: jialiang 获取正确的 spaceId 和 creatorId
      const query = new GetSnipQuery(id, 'placeholder-space-id', 'placeholder-creator-id');
      const result = (await this.queryBus.execute(query)) as any;

      // 转换数据格式，适配上下文提供者的接口
      return {
        id: result.id,
        type: 'snip',
        title: result.title,
        url: result.webpage?.url || `/snips/${id}`,
        content: result.content?.plain || result.content?.raw || '',
        extra: {
          snipType: result.type,
          url: result.webpage?.url,
          authors: (result.authors as any[]) || [],
          publishedAt: result.publishedAt,
          overview: (result.overview?.contents?.[0]?.plain as string) || '',
          transcript: (result.transcript?.contents?.[0]?.plain as string) || '',
          imageDescription: (result.imageDescription as string) || '',
          extractedText: (result.extractedText as string) || '',
          imageUrls: (result.imageUrls as string[]) || [],
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.debug(`片段未找到: ${id}`);
        return null;
      }
      this.logger.error(`获取片段数据失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 获取片段的上下文项目列表
   */
  async getContextItems(id: string, _options?: ContextOptions): Promise<IContextItem[]> {
    try {
      const snipItem = await this.getContextItem(id);
      if (!snipItem) {
        return [];
      }
      return [snipItem];
    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.debug(`片段未找到: ${id}`);
        return [];
      }
      this.logger.error(`获取片段上下文项目失败: ${id}`, error);
      return [];
    }
  }

  /**
   * 获取片段的文本内容
   */
  async getContextString(id: string, options?: ContextOptions): Promise<string> {
    try {
      const snipItem = await this.getContextItem(id);
      if (!snipItem) {
        return '';
      }

      let text = snipItem.content || '';

      // 根据片段类型选择最佳内容
      if (snipItem.extra.snipType === SnipType.IMAGE) {
        text =
          (snipItem.extra.extractedText as string) ||
          (snipItem.extra.imageDescription as string) ||
          '';
      } else if (snipItem.extra.snipType === SnipType.VIDEO) {
        text = (snipItem.extra.transcript as string) || text;
      } else if (snipItem.extra.snipType === SnipType.VOICE) {
        text = (snipItem.extra.transcript as string) || text;
      }

      const maxTokens = options?.maxTokens || 10000;

      return options?.method === 'slice'
        ? truncateByTokens(text, { maxTokens })
        : sampleText(text, maxTokens);
    } catch (error) {
      this.logger.error(`获取片段上下文字符串失败: ${id}`, error);
      return '';
    }
  }

  /**
   * 获取聊天优化的上下文
   */
  async getChatContext(id: string, _chat: any, options?: ContextOptions): Promise<ChatContext> {
    try {
      const snipItem = await this.getContextItem(id);
      if (!snipItem) {
        throw new NotFoundException(`Snip ${id} not found`);
      }

      // TODO: 实现别名获取
      const aliases: string[] = [];

      return {
        image_urls: (snipItem.extra.imageUrls as string[]) || [],
        fields: {
          url: snipItem.url || `/snips/${id}`,
          type: 'snip',
          title: snipItem.title || '无标题片段',
          snip_id: id,
          snip_type: snipItem.extra.snipType,
          author: (snipItem.extra.authors as any[])?.join(', ') || '',
          published_at: snipItem.extra.publishedAt,
          content: await this.getContextString(id, options),
          overview: snipItem.extra.overview || '',
          transcript: snipItem.extra.transcript || '',
          image_description: snipItem.extra.imageDescription || '',
          extracted_text: snipItem.extra.extractedText || '',
          aliases,
          // TODO: 实现 related_chunks
          related_chunks: '',
        },
      };
    } catch (error) {
      this.logger.error(`获取片段聊天上下文失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 获取 Magic Creation 上下文
   */
  async getMagicCreationContext(
    id: string,
    options?: MagicCreationOptions,
  ): Promise<MagicCreationContext> {
    try {
      const snipItem = await this.getContextItem(id);
      if (!snipItem) {
        throw new NotFoundException(`Snip ${id} not found`);
      }

      const content = await this.getContextString(id, {
        userId: options?.userId || '',
        spaceId: options?.spaceId || '',
        maxTokens: options?.maxTokens || 5000,
        method: options?.method || 'sample',
      });

      return {
        url: snipItem.url || `/snips/${id}`,
        title: snipItem.title || '无标题片段',
        content,
      };
    } catch (error) {
      this.logger.error(`获取片段 Magic Creation 上下文失败: ${id}`, error);
      throw error;
    }
  }
}
