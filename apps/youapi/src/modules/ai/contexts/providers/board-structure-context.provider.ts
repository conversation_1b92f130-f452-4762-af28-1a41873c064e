import { Injectable } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ChatContext,
  ContextOptions,
  ContextProviderType,
  IContextItem,
  MagicCreationContext,
  MagicCreationOptions,
} from '@/common/types/context.type';
import { BaseContextProvider, ContextProvider } from './abstract.provider';

@Injectable()
@ContextProvider(ContextProviderType.BOARD_STRUCTURE)
export class BoardStructureContextProvider extends BaseContextProvider {
  constructor(
    public readonly queryBus: QueryBus,
    public readonly commandBus: CommandBus,
  ) {
    super(queryBus, commandBus);
  }
  /**
   * 重写默认选项，为 Thought 提供者定制默认配置
   */
  getDefaultContextOptions(): Partial<ContextOptions> {
    return {
      maxTokens: 2000, // Thought 需要更多 tokens
      method: 'slice',
      // 可以添加其他 Thought 特有的默认选项
    };
  }

  getContextItems(_id: string, _options?: ContextOptions): Promise<IContextItem[]> {
    // 使用合并后的选项
    const options = this.mergeContextOptions(_options);

    console.log(`Thought provider options:`, {
      maxTokens: options.maxTokens,
      method: options.method,
      userId: options.userId,
      spaceId: options.spaceId,
    });

    // 格式
    //     📁 整理信息 [01980254]
    //    └─ 📝 thought [01982667]

    // 📄 Snips
    //    ├─ 编辑页面 [01984af2]
    //    ├─ streamText [01983d26]
    //    ├─ Langfuse
    //    │  ├─ SDK [01982c60]
    //    │  ├─ Libraries [01982c39]
    //    │  └─ AI Eng Lib [01982c38]
    //    ├─ OpenAI
    //    │  ├─ Prompt Cache [019826c5]
    //    │  └─ API Usage [01982199]
    //    ├─ Search Tools
    //    │  ├─ Personalized [019808d8]
    //    │  └─ Anthropic [019808ab,01980897]
    //    └─ LLM Prompts [019806eb]
    throw new Error('Method not implemented.');
  }

  getContextObject(_id: string, _options?: ContextOptions): Promise<any> {
    throw new Error('Method not implemented.');
  }

  getContextString(_id: string, _options?: ContextOptions): Promise<string> {
    // 同样使用合并后的选项
    const options = this.mergeContextOptions(_options);

    throw new Error('Method not implemented.');
  }

  getChatContext(_id: string, _chat: any, _options?: ContextOptions): Promise<ChatContext> {
    throw new Error('Method not implemented.');
  }

  getMagicCreationContext(
    _id: string,
    _options?: MagicCreationOptions,
  ): Promise<MagicCreationContext> {
    // MagicCreationOptions 继承自 ContextOptions，所以也可以合并
    const options = this.mergeContextOptions(_options);

    throw new Error('Method not implemented.');
  }
}
