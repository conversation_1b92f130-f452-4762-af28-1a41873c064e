import { Injectable } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ChatContext,
  ContextOptions,
  ContextProviderType,
  IContextItem,
  IContextSubItem,
  MagicCreationContext,
  MagicCreationOptions,
} from '@/common/types/context.type';
// import { GetBoardGroupQuery } from '@/modules/material-mng/services/queries/board-group/get-board-group.query';
// import { ListBoardItemsByParentBoardGroupIdQuery } from '@/modules/material-mng/services/queries/board-item/list-board-items-by-parent-board-group.query';
import { GetSnipQuery } from '@/modules/material-mng/services/queries/snip/get-snip.query';
import { GetThoughtQuery } from '@/modules/material-mng/services/queries/thought/get-thought.query';
// TODO: 移除 SearchService 依赖，后续迁移到 InternetSearchDomainService
// import { SearchService } from '@/modules/search/services/search.service';
import { BaseContextProvider, ContextProvider } from './abstract.provider';

/**
 * Board Group 上下文提供者
 *
 * 负责处理 Board Group 类型的上下文获取
 *
 * TODO: 后续迁移到 apps/youapi/src/domain/search/internet.ts 中的 InternetSearchDomainService
 */
@Injectable()
@ContextProvider(ContextProviderType.BOARD_GROUP)
export class BoardGroupContextProvider extends BaseContextProvider {
  constructor(
    public readonly queryBus: QueryBus,
    public readonly commandBus: CommandBus,
    // TODO: 移除 SearchService 依赖，后续迁移到 InternetSearchDomainService
    // public readonly searchService: SearchService,
  ) {
    super(queryBus, commandBus);
  }

  getDefaultContextOptions(): Partial<ContextOptions> {
    return {
      maxTokens: 3000, // Board Group 需要更多 tokens 来包含子项
      method: 'sample',
    };
  }

  async getContextItems(entityId: string, options?: ContextOptions): Promise<IContextItem[]> {
    const mergedOptions = this.mergeContextOptions(options);
    const items: IContextItem[] = [];

    try {
      // // 1. 获取 board group 基本信息
      // const boardGroup = await this.getBoardGroupData(entityId);

      // // 2. 获取 board group 内的 board items
      // const boardItems = await this.getBoardGroupItems(entityId);

      // // 3. 构建主要 context item
      // const mainContent = this.buildBoardGroupContent(boardGroup, boardItems);
      // const tokenCount = estimateTokens(mainContent);

      // items.push({
      //   id: entityId,
      //   type: 'board_group',
      //   title: boardGroup.name,
      //   content: mainContent,
      //   url: `/board/${boardGroup.boardId}?groupId=${entityId}`,
      //   extra: {
      //     boardId: boardGroup.boardId,
      //     itemCount: boardItems.length,
      //     createdAt: boardGroup.createdAt,
      //     tokenCount,
      //   },
      // });

      return this.applyContentSampling(items, mergedOptions);
    } catch (error) {
      console.error(`Error getting board group context for ${entityId}:`, error);
      return [];
    }
  }

  async getContextSubItems(
    _userId: string,
    boardGroupId: string,
    options?: ContextOptions,
  ): Promise<IContextSubItem[]> {
    try {
      // const boardItems = await this.getBoardGroupItems(boardGroupId);
      const boardItems = [];
      const subItems: IContextSubItem[] = [];

      // 获取每个 board item 的详细信息
      for (const item of boardItems) {
        if (item.entityType === 'snip') {
          try {
            // TODO: 需要从上下文中获取 spaceId 和 creatorId
            const snip = await this.queryBus.execute(
              new GetSnipQuery(item.entityId, 'default-space', 'default-user'),
            );
            subItems.push({
              id: item.entityId,
              type: 'snip',
              title: snip.title || 'Untitled Snip',
              content: snip.contentPlain || snip.contentRaw || '',
              url: `/snips/${item.entityId}`,
              extra: {
                snipType: snip.type,
                createdAt: snip.createdAt,
              },
            });
          } catch (error) {
            console.warn(`Failed to get snip ${item.entityId}:`, error);
          }
        } else if (item.entityType === 'thought') {
          try {
            const thought = await this.queryBus.execute(new GetThoughtQuery(item.entityId));
            subItems.push({
              id: item.entityId,
              type: 'thought',
              title: thought.title || 'Untitled Thought',
              content: thought.contentPlain || thought.contentRaw || '',
              url: `/thoughts/${item.entityId}`,
              extra: {
                createdAt: thought.createdAt,
              },
            });
          } catch (error) {
            console.warn(`Failed to get thought ${item.entityId}:`, error);
          }
        }
        // TODO: 添加 chat 支持
        // } else if (item.entityType === 'chat') {
        //   const chat = await this.queryBus.execute(new GetChatQuery(item.entityId));
        //   // ... handle chat
        // }
      }

      return this.applyContentSampling(subItems, options);
    } catch (error) {
      console.error(`Error getting board group sub items for ${boardGroupId}:`, error);
      return [];
    }
  }

  async getContextString(entityId: string, options?: ContextOptions): Promise<string> {
    const items = await this.getContextItems(entityId, options);
    return items.map((item) => `${item.title}\n${item.content}`).join('\n\n');
  }

  getContextObject(_id: string, _options?: ContextOptions): Promise<any> {
    throw new Error('Method not implemented.');
  }

  getChatContext(_id: string, _options?: ContextOptions): Promise<ChatContext> {
    throw new Error('Method not implemented.');
  }

  getMagicCreationContext(
    _id: string,
    _options?: MagicCreationOptions,
  ): Promise<MagicCreationContext> {
    throw new Error('Method not implemented.');
  }

  // Private helper methods
  // private async getBoardGroupData(boardGroupId: string): Promise<any> {
  //   try {
  //     return await this.queryBus.execute(new GetBoardGroupQuery(boardGroupId));
  //   } catch (error) {
  //     console.error(`Failed to get board group ${boardGroupId}:`, error);
  //     throw error;
  //   }
  // }

  // private async getBoardGroupItems(boardGroupId: string): Promise<any[]> {
  //   try {
  //     return await this.queryBus.execute(new ListBoardItemsByParentBoardGroupIdQuery(boardGroupId));
  //   } catch (error) {
  //     console.warn(`Failed to get board items for group ${boardGroupId}:`, error);
  //     return [];
  //   }
  // }

  private buildBoardGroupContent(boardGroup: any, boardItems: any[]): string {
    const lines = [
      `Board Group: ${boardGroup.name}`,
      `Created: ${boardGroup.createdAt}`,
      `Items: ${boardItems.length}`,
    ];

    if (boardItems.length > 0) {
      lines.push('', 'Items in this group:');
      boardItems.forEach((item, index) => {
        if (item.snipId) {
          lines.push(`${index + 1}. Snip: ${item.snipId}`);
        } else if (item.thoughtId) {
          lines.push(`${index + 1}. Thought: ${item.thoughtId}`);
        } else if (item.chatId) {
          lines.push(`${index + 1}. Chat: ${item.chatId}`);
        }
      });
    }

    return lines.join('\n');
  }
}
