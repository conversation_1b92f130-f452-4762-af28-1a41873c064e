import { Injectable, Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { sampleText } from '@repo/common';
import {
  ContextOptions,
  ContextProviderType,
  IContextItem,
  IContextSubItem,
  SourceTypeEnum,
} from '@/common/types/context.type';
import { runConcurrently, truncateByTokens } from '@/common/utils';
import { BoardDto, BoardItemDto, BoardWithItemsDto } from '@/modules/material-mng/dto/board.dto';
import { GetBoardQuery } from '@/modules/material-mng/services/queries/board/get-board.query';
import { GetBoardDetailQuery } from '@/modules/material-mng/services/queries/board/get-board-detail.query';
import { BaseContextProvider, ContextProvider } from './abstract.provider';

interface Source {
  entity_type: string;
  entity_id: string;
}

interface ChatContext {
  image_urls: string[];
  fields: {
    url: string;
    type: string;
    title: string;
  };
}

interface MagicCreationOptions {
  maxTokens?: number;
  method?: string;
}

interface MagicCreationContext {
  url: string;
  title: string;
  content: string;
}

interface HybridSearchResult {
  url: string;
  title: string;
  content: string;
}

const DEFAULT_SEARCH_FIELDS: string[] = [];

@Injectable()
@ContextProvider(ContextProviderType.BOARD)
export class BoardContextProvider extends BaseContextProvider {
  private readonly logger = new Logger(BoardContextProvider.name);

  constructor(
    public readonly queryBus: QueryBus,
    public readonly commandBus: CommandBus,
  ) {
    super(queryBus, commandBus);
  }

  /**
   * 重写默认选项，为 Board 提供者定制默认配置
   */
  getDefaultContextOptions(): Partial<ContextOptions> {
    return {
      maxTokens: 3000, // Board 需要更多 tokens 来包含项目内容
      method: 'slice',
    };
  }

  async getDescription(_board: BoardDto): Promise<string> {
    // if (context.sources.length < 10) {
    //   const source_contents = context.sources.map((s) =>
    //     pickSourceContext(s.fields),
    //   );
    //   return `${context.content}\nSource metadata:\n${jsonToXml({ sources: source_contents })}`;
    // }

    return `There are too many sources to list. Ask user to refer to specific source when chatting.`;
  }

  async getAllBoardContextItems(
    boardId: string,
    options?: ContextOptions,
  ): Promise<IContextItem[]> {
    try {
      const maxTokens = options?.maxTokens || 10000;
      const userId = options?.userId;
      const spaceId = options?.spaceId;

      if (!userId || !spaceId) {
        this.logger.error({ userId, spaceId }, 'userId and spaceId are required');
        throw new Error('userId and spaceId are required');
      }

      // 获取 Board
      const board: BoardDto = await this.queryBus.execute(
        new GetBoardQuery(userId, spaceId, boardId),
      );

      // 获取 Board Details
      const boardDetail: BoardWithItemsDto = await this.queryBus.execute(
        new GetBoardDetailQuery(userId, spaceId, boardId),
      );
      const boardItems: BoardItemDto[] = boardDetail.boardItems;

      // 过滤并获取简单实体（snip, thought）的内容
      const simpleItems: BoardItemDto[] = boardItems.filter(
        (item) =>
          item.entityType === SourceTypeEnum.SNIP || item.entityType === SourceTypeEnum.THOUGHT,
      );

      if (simpleItems.length === 0) {
        // 如果没有简单实体，返回板块本身的描述
        const content = await this.getDescription(boardDetail);
        return [
          {
            id: boardId,
            type: SourceTypeEnum.BOARD,
            title: `Board ${boardId}`,
            content,
            url: `/boards/${boardId}`,
          },
        ];
      }

      const tokensPerItem = Math.floor(maxTokens / simpleItems.length);

      // 并发获取所有简单实体的内容
      const simpleContextItems = await runConcurrently(
        simpleItems.map((item) => async () => {
          try {
            // TODO: 可以通过直接查询数据库或其他方式获取 snip/thought 内容
            // Provide.getContextItem()
            // 这里先返回基本信息，确保方法能正常工作
            // const contextItem = await ContextManager.getProvider(item.entityType).getContextItem(item.id, {
            //   maxTokens: tokensPerItem,
            //   method: 'slice',
            // });
            const content = `retrieve ${item.entityType} content for ${item.id}`;

            return {
              id: item.id,
              type: item.entityType,
              title: `${item.entityType} ${item.id}`,
              content: content || '',
              url: `/${item.entityType}s/${item.id}`,
            } as IContextItem;
          } catch (error) {
            console.error(`Error getting content for ${item.entityType} ${item.id}:`, error);
            return null;
          }
        }),
      );
      // TODO: Board Group Context Items 还没有调整

      // Filter out empty contents Items
      const validContextItems = simpleContextItems.filter(
        (_item): _item is IContextItem => _item.content !== '',
      ) as IContextItem[];

      return validContextItems;
    } catch (error) {
      this.logger.error(`Error getting all board context items for ${boardId}:`, error);
      return [];
    }
  }

  async searchBoardContextItems(
    _boardId: string,
    _options?: ContextOptions,
  ): Promise<IContextItem[]> {
    // TODO: contentRetrieverService

    return [];
  }

  async getContexts(
    entityId: string,
    options?: ContextOptions,
  ): Promise<{ contextItems: IContextItem[]; contextString: string }> {
    const contextItems = await this.getContextItems(entityId, options);

    if (contextItems.length === 0) {
      this.logger.debug(`⚠️ No context items found for board ${entityId}`);
      return { contextItems, contextString: '' };
    }

    // Combine all context items into a single string
    const contextString = contextItems
      .map((_item) => `## ${_item.title}\n\n${_item.content}`)
      .join('\n\n---\n\n');

    // Apply token limits if specified
    const maxTokens = options?.maxTokens || 10000;
    const finalContext =
      options?.method === 'slice'
        ? truncateByTokens(contextString, { maxTokens })
        : sampleText(contextString, maxTokens);

    this.logger.debug(
      `✅ BoardContextProvider returned context string of length ${finalContext.length} for board ${entityId}`,
    );

    return { contextItems, contextString: finalContext };
  }

  /**
   * 实现具体的 Board Search 逻辑，可能还需要一些裁剪方法。最后返回 IContextItem 就好了
   * @param id: boardId
   * @param options, search 参数，比如 query, contextType
   * [apps/youapi/src/modules/ai/tools/board-search.tool.ts]
   * @returns
   */
  async getContextItems(id: string, options?: ContextOptions): Promise<IContextItem[]> {
    this.logger.debug(`🔍 BoardContextProvider.getContextItems called with id: ${id}`);
    const { query, contextType } = options?.extra || {};
    // get board ids from
    if (!query && !contextType) {
      throw new Error('query or contextType are required');
    }

    // if (contextType === 'full') {
    //   return this.getAllBoardContextItems(id, options);
    // } else if (contextType === 'relevant') {
    //   return this.searchBoardContextItems(id, options);
    // } else {
    //   throw new Error('Invalid contextType');
    // }

    return await this.getAllBoardContextItems(id, options);

    // try {
    //   // 1. 获取 Board 基本信息
    //   const boardQuery = new GetBoardQuery(options.userId, options.spaceId, id);
    //   const board = await this.queryBus.execute(boardQuery);

    //   // 3. 构建 Board 内容
    //   const boardContent = await this.getContextString(id, options);

    //   const contextItems: IContextItem[] = [
    //     {
    //       id,
    //       type: 'board',
    //       title: board.name || 'Untitled Board',
    //       content: boardContent,
    //       url: `/boards/${id}`,
    //       extra: {
    //         boardType: board.type || 'normal',
    //         itemCount: boardItems.length,
    //         createdAt: board.createdAt,
    //         updatedAt: board.updatedAt,
    //       },
    //     },
    //   ];

    //   // Apply content sampling if options provided
    //   const sampledItems = this.applyContentSampling(contextItems, options);

    //   this.logger.debug(
    //     `✅ BoardContextProvider returned ${sampledItems.length} context items for board ${id}`,
    //   );
    //   return sampledItems;
    // } catch (error) {
    //   this.logger.error(`获取看板上下文项目失败: ${id}`, error);
    //   return [];
    // }
  }

  async getContextString(_id: string, _optionss?: ContextOptions): Promise<string> {
    this.logger.debug(`🔍 BoardContextProvider.getContextString called with id: $id`);
    return '';
  }

  async getContextSubItems(
    _userId: string,
    _entityId: string,
    _options?: ContextOptions,
  ): Promise<IContextSubItem[]> {
    // TODO: 获取看板子项
    // 1. 获取看板
    // 2. 获取看板子项
    // 3. 返回看板子项

    return [];
  }

  async getMagicCreationContext(
    _id: string,
    _options?: MagicCreationOptions,
  ): Promise<MagicCreationContext> {
    return {
      url: '',
      title: '',
      content: '',
    };
  }

  async getHybridSearchResult(_id: string, _options?: ContextOptions): Promise<HybridSearchResult> {
    // TODO: 获取看板子项
    // 1. 获取看板
    // 2. 获取看板子项
    // 3. 返回看板子项

    return {
      url: '',
      title: '',
      content: '',
    };
  }
}
