/**
 * Thought Context Provider - 思考上下文提供者
 *
 * 重构为使用 CQRS 与 material-mng 模块交互
 *
 * 迁移自:
 * - youapi/src/domain/llm/context/thought.ts
 */

import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { sampleText } from '@repo/common';
import {
  ChatContext,
  ContextOptions,
  ContextProviderType,
  IContextItem,
  MagicCreationContext,
  MagicCreationOptions,
} from '@/common/types/context.type';
import { truncateByTokens } from '@/common/utils';
import { ThoughtDto } from '@/modules/material-mng/dto/thought.dto';
// 导入 material-mng 模块的 CQRS queries
import { GetThoughtQuery } from '@/modules/material-mng/services/queries/thought/get-thought.query';
import { BaseContextProvider, ContextProvider } from './abstract.provider';

@Injectable()
@ContextProvider(ContextProviderType.THOUGHT)
export class ThoughtContextProvider extends BaseContextProvider {
  private readonly logger = new Logger(ThoughtContextProvider.name);

  constructor(
    public readonly queryBus: QueryBus,
    public readonly commandBus: CommandBus,
  ) {
    super(queryBus, commandBus);
  }

  /**
   * 重写默认选项，为 Thought 提供者定制默认配置
   */
  getDefaultContextOptions(): Partial<ContextOptions> {
    return {
      maxTokens: 5000,
      method: 'sample',
    };
  }

  async getContextItem(id: string, _options?: ContextOptions): Promise<IContextItem> {
    try {
      // TODO: jialiang 获取正确的 spaceId 和 creatorId
      const query = new GetThoughtQuery(id);
      const dto: ThoughtDto = await this.queryBus.execute(query);
      const thoughtItem: IContextItem = {
        id: dto.id,
        type: 'thought',
        title: dto.title,
        content: dto.content?.plain || '',
        extra: {
          contentRaw: dto.content?.raw || '',
          createdAt: dto.createdAt,
          updatedAt: dto.updatedAt,
          titleType: dto.titleType,
          visibility: dto.visibility,
          position: dto.position,
        },
      };
      return thoughtItem;
    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.debug(`思考未找到: ${id}`);
        return null;
      }
      this.logger.error(`获取思考数据失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 获取思考的上下文项目列表
   */
  async getContextItems(id: string, _options?: ContextOptions): Promise<IContextItem[]> {
    try {
      const thoughtItem = await this.getContextItem(id);
      return [thoughtItem];
    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.debug(`思考未找到: ${id}`);
        return null;
      }

      this.logger.error(`获取思考数据失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 获取思考的文本内容
   */
  async getContextString(id: string, options?: ContextOptions): Promise<string> {
    try {
      const thoughtItem = await this.getContextItem(id);
      if (!thoughtItem) {
        return '';
      }

      const mergedOptions = this.mergeContextOptions(options);
      const maxTokens = mergedOptions.maxTokens || 5000;

      // 优先使用纯文本内容，否则使用原始内容
      const text = thoughtItem.content || '';

      return mergedOptions.method === 'slice'
        ? truncateByTokens(text, { maxTokens })
        : sampleText(text, maxTokens);
    } catch (error) {
      this.logger.error(`获取思考上下文字符串失败: ${id}`, error);
      return '';
    }
  }

  /**
   * 获取聊天优化的上下文
   */
  async getChatContext(id: string, _chat: any, options?: ContextOptions): Promise<ChatContext> {
    try {
      const thoughtItem = await this.getContextItem(id);
      if (!thoughtItem) {
        throw new NotFoundException(`Thought ${id} not found`);
      }

      // TODO: 实现别名获取
      const aliases: string[] = [];

      return {
        image_urls: [], // 思考通常没有图片
        fields: {
          url: `/thoughts/${id}`,
          type: 'thought',
          title: thoughtItem.title || '无标题思考',
          thought_id: id,
          content: await this.getContextString(id, options),
          title_type: thoughtItem.extra.titleType,
          visibility: thoughtItem.extra.visibility,
          aliases,
          // TODO: 实现 related_chunks
          related_chunks: '',
        },
      };
    } catch (error) {
      this.logger.error(`获取思考聊天上下文失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 获取 Magic Creation 上下文
   */
  async getMagicCreationContext(
    id: string,
    options?: MagicCreationOptions,
  ): Promise<MagicCreationContext> {
    try {
      const thoughtItem = await this.getContextItem(id);
      if (!thoughtItem) {
        throw new NotFoundException(`Thought ${id} not found`);
      }

      const content = await this.getContextString(id, {
        userId: options?.userId || '',
        spaceId: options?.spaceId || '',
        maxTokens: options?.maxTokens || 5000,
        method: options?.method || 'sample',
      });

      return {
        url: thoughtItem?.url || `/thoughts/${id}`,
        title: thoughtItem.title || '无标题思考',
        content,
      };
    } catch (error) {
      this.logger.error(`获取思考 Magic Creation 上下文失败: ${id}`, error);
      throw error;
    }
  }
}
