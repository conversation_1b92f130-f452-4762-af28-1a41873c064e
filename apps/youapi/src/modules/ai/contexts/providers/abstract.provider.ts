import 'reflect-metadata';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ContextOptions,
  IContextItem,
  IContextProvider,
  IContextSubItem,
  MagicCreationContext,
  MagicCreationOptions,
} from '@/common/types/context.type';

import { estimateTokens, truncateByTokens } from '@/common/utils';
/**
 * Context Provider 装饰器
 *
 * 用于标记和配置 Context Provider 类
 * @param type Provider 类型标识
 */
export function ContextProvider(type: string) {
  return (target: any) => {
    // 存储类型信息到元数据
    Reflect.defineMetadata('context:provider:type', type, target);
  };
}

/**
 * Context Provider 抽象基类
 *
 * 提供通用的 Provider 功能，子类只需要实现具体的业务逻辑
 */
export abstract class BaseContextProvider implements IContextProvider {
  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
  ) {}

  /**
   * 从装饰器元数据中获取 Provider 类型
   */
  getProviderType(): string {
    return Reflect.getMetadata('context:provider:type', this.constructor);
  }

  mergeContextOptions(userOptions?: ContextOptions): ContextOptions {
    const defaultOptions = this.getDefaultContextOptions();
    return {
      ...defaultOptions,
      ...userOptions,
    };
  }

  getContexts(
    _entityId: string,
    _options?: ContextOptions,
  ): Promise<{ contextItems: IContextItem[]; contextString: string }> {
    throw new Error('Method not implemented.');
  }

  async getContextSubItems(
    _userId: string,
    _entityId: string,
    _options?: ContextOptions,
  ): Promise<IContextSubItem[]> {
    // 防止子类都要写这个方法，提前返回一个空的数组
    return [];
  }

  getAlias() {}

  /**
   * 应用内容采样策略
   */
  protected applyContentSampling(items: IContextItem[], options?: ContextOptions): IContextItem[] {
    if (!options?.maxTokens) {
      return items;
    }

    let totalTokens = 0;
    const result: IContextItem[] = [];

    for (const item of items) {
      const itemTokens = estimateTokens(item.content);
      if (totalTokens + itemTokens <= options.maxTokens) {
        result.push(item);
        totalTokens += itemTokens;
      } else {
        // 如果单个项目就超过了限制，进行截断
        if (result.length === 0) {
          const truncatedContent = truncateByTokens(item.content, { maxTokens: options.maxTokens });
          result.push({
            ...item,
            content: truncatedContent,
          });
        }
        break;
      }
    }

    return result;
  }

  abstract getDefaultContextOptions(): Partial<ContextOptions>;

  abstract getContextItems(entityId: string, options?: ContextOptions): Promise<IContextItem[]>;

  abstract getContextString(entityId: string, options?: ContextOptions): Promise<string>;

  abstract getMagicCreationContext(
    entityId: string,
    options?: MagicCreationOptions,
  ): Promise<MagicCreationContext>;
}
