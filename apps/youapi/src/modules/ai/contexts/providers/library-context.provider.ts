import { Injectable } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ChatContext,
  ContextOptions,
  ContextProviderType,
  IContextItem,
  MagicCreationContext,
  MagicCreationOptions,
} from '@/common/types/context.type';
import { estimateTokens } from '@/common/utils/token';
import { SearchDomainService } from '@/domain/search';
import { BaseContextProvider, ContextProvider } from './abstract.provider';

@Injectable()
@ContextProvider(ContextProviderType.LIBRARY)
export class LibraryContextProvider extends BaseContextProvider {
  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
    private readonly searchDomain: SearchDomainService,
  ) {
    super(queryBus, commandBus);
  }

  /**
   * 重写默认选项，为 Library 提供者定制默认配置
   */
  getDefaultContextOptions(): Partial<ContextOptions> {
    return {
      maxTokens: 20000, // Library 搜索需要更多 tokens
      method: 'slice',
    };
  }

  async getContexts(
    _entityId: string,
    options?: ContextOptions,
  ): Promise<{ contextItems: IContextItem[]; contextString: string }> {
    const contextItems = await this.getContextItems(_entityId, options);

    // 将 contextItems 转换为 contextString
    const contextString = contextItems
      .map((item) => item.content)
      .filter(Boolean)
      .join('\n\n---\n\n');

    return {
      contextItems,
      contextString,
    };
  }

  async getContextItems(_id: string, options?: ContextOptions): Promise<IContextItem[]> {
    // 使用合并后的选项
    const mergedOptions = this.mergeContextOptions(options);

    console.log(`Library provider options:`, {
      maxTokens: mergedOptions.maxTokens,
      method: mergedOptions.method,
      userId: mergedOptions.userId,
      spaceId: mergedOptions.spaceId,
    });

    const { query, contextType } = mergedOptions.extra || {};

    // 如果模型判断需要召回整个 library 里的内容，则无能为力
    if (contextType === 'full' || !query || query.length === 0) {
      return [];
    }

    // 否则走搜索逻辑
    const results = await this.searchDomain.hybridSearch({
      query: query,
      userId: mergedOptions.userId!,
      boardIds: [],
    });

    // 将搜索结果转换为上下文构建结果
    if (!results.hits || results.hits.length === 0) {
      return [];
    }

    // Define types for the search hit document with required fields
    type SearchHitDocument = {
      id?: string;
      docType?: string;
      content_0?: string;
      title?: string;
      [key: string]: unknown;
    };

    type SearchHighlight = {
      [field: string]: { snippet?: string } | Array<{ snippet?: string }> | unknown;
    };

    let totalTokensUsed = 0;
    const contextItems: IContextItem[] = [];

    // Group hits by entity (id + type)
    const entityGroups = new Map<string, typeof results.hits>();
    for (const hit of results.hits) {
      const doc = hit.document as SearchHitDocument;
      const entityKey = `${doc?.id}-${doc?.docType}`;
      if (!entityGroups.has(entityKey)) {
        entityGroups.set(entityKey, []);
      }
      entityGroups.get(entityKey)!.push(hit);
    }

    // Convert each entity group to context item
    for (const [entityKey, entityHits] of entityGroups.entries()) {
      const firstHit = entityHits[0];
      const firstDoc = firstHit.document as SearchHitDocument;
      if (!firstDoc?.id || !firstDoc?.docType) continue;

      let entityTokenCount = 0;
      let combinedContent = '';

      for (const hit of entityHits) {
        const doc = hit.document as SearchHitDocument;

        // Extract chunk content from highlight or fallback to document content
        let chunkContent = '';
        if (hit.highlight) {
          const highlightValues = Object.values(hit.highlight as SearchHighlight);
          chunkContent = highlightValues
            .map((h) => {
              if (Array.isArray(h)) {
                return h[0]?.snippet || '';
              } else if (h && typeof h === 'object' && 'snippet' in h) {
                return (h as { snippet?: string }).snippet || '';
              }
              return '';
            })
            .filter(Boolean)
            .join('\n\n');
        }

        if (!chunkContent && doc?.content_0) {
          chunkContent = doc.content_0.slice(0, 500); // Fallback with truncation
        }

        if (
          chunkContent &&
          entityTokenCount + estimateTokens(chunkContent) <= mergedOptions.maxTokens!
        ) {
          const tokenUsed = estimateTokens(chunkContent);
          entityTokenCount += tokenUsed;
          totalTokensUsed += tokenUsed;

          if (combinedContent) {
            combinedContent += '\n\n---\n\n';
          }
          combinedContent += chunkContent;
        }

        // Stop if we've reached the token limit
        if (totalTokensUsed >= mergedOptions.maxTokens!) {
          break;
        }
      }

      if (combinedContent) {
        contextItems.push({
          id: firstDoc.id,
          type: firstDoc.docType,
          title: firstDoc.title || '',
          content: combinedContent,
          extra: {
            relevance: 1 - (firstHit.text_match || 0), // Convert similarity back to distance
            tokenUsed: entityTokenCount,
          },
        });
      }

      // Stop if we've reached the token limit
      if (totalTokensUsed >= mergedOptions.maxTokens!) {
        break;
      }
    }

    return contextItems;
  }

  async getContextObject(_id: string, options?: ContextOptions): Promise<any> {
    const contextItems = await this.getContextItems(_id, options);
    return {
      type: 'library',
      items: contextItems,
      totalItems: contextItems.length,
    };
  }

  async getContextString(_id: string, options?: ContextOptions): Promise<string> {
    const { contextString } = await this.getContexts(_id, options);
    return contextString;
  }

  async getChatContext(_id: string, _chat: any, _options?: ContextOptions): Promise<ChatContext> {
    throw new Error('Method not implemented for library context provider.');
  }

  async getMagicCreationContext(
    _id: string,
    _options?: MagicCreationOptions,
  ): Promise<MagicCreationContext> {
    throw new Error('Method not implemented for library context provider.');
  }
}
