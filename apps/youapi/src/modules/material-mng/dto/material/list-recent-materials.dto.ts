import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';

export class ListRecentMaterialsDto {
  @ApiProperty({
    description: '返回数量',
    example: 20,
  })
  @IsNumber()
  number!: number;

  @ApiProperty({
    description: '是否包含已归档的内容',
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  includeArchived?: boolean;
}

export class RecentMaterialItemDto {
  @ApiProperty({
    description: '材料类型',
    enum: ['snip', 'thought'],
  })
  type!: 'snip' | 'thought';

  @ApiProperty({
    description: '材料ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id!: string;

  @ApiProperty({
    description: '排序时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  orderBy!: Date;

  @ApiProperty({
    description: '材料数据',
    type: Object,
  })
  data!: any;
}
