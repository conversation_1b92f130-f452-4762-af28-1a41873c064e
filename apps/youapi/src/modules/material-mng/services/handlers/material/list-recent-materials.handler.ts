import { Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { RecentMaterialItemDto } from '../../../dto/material/list-recent-materials.dto';
import { SnipRepository } from '../../../repositories/snip.repository';
import { ThoughtRepository } from '../../../repositories/thought.repository';
import { SnipDtoService } from '../../dto-services/snip-dto.service';
import { ThoughtDtoService } from '../../dto-services/thought-dto.service';
import { ListRecentMaterialsQuery } from '../../queries/material/list-recent-materials.query';

@QueryHandler(ListRecentMaterialsQuery)
@Injectable()
export class ListRecentMaterialsHandler implements IQueryHandler<ListRecentMaterialsQuery> {
  private readonly logger = new Logger(ListRecentMaterialsHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly thoughtRepository: ThoughtRepository,
    private readonly snipDtoService: SnipDtoService,
    private readonly thoughtDtoService: ThoughtDtoService,
  ) {}

  async execute(query: ListRecentMaterialsQuery): Promise<RecentMaterialItemDto[]> {
    const { userId, spaceId, number, includeArchived } = query;

    this.logger.log(
      `Listing recent materials for user ${userId} in space ${spaceId}, number: ${number}, includeArchived: ${includeArchived}`,
    );

    // 根据 includeArchived 参数获取不同的数据
    const [snips, thoughts] = await Promise.all([
      includeArchived
        ? // 包含归档：获取所有 snips
          this.snipRepository.findByFilter({ spaceId })
        : // 不包含归档：获取进行中的 snips
          this.snipRepository.findInProgressBySpaceId(spaceId),

      includeArchived
        ? // 包含归档：获取所有 thoughts
          this.thoughtRepository.findByFilter({ spaceId })
        : // 不包含归档：获取进行中的 thoughts
          this.thoughtRepository.findInProgressBySpaceId(spaceId),
    ]);

    // 转换为 DTO
    const [snipDtos, thoughtDtos] = await Promise.all([
      this.snipDtoService.toDtoList(snips),
      this.thoughtDtoService.toDtoList(thoughts),
    ]);

    // 合并并按时间排序
    const recentMaterials: RecentMaterialItemDto[] = [
      // snips 使用 createdAt 排序
      ...snipDtos.map((snip) => ({
        type: 'snip' as const,
        id: snip.id,
        orderBy: snip.createdAt,
        data: snip,
      })),
      // thoughts 使用 updatedAt 排序
      ...thoughtDtos.map((thought) => ({
        type: 'thought' as const,
        id: thought.id,
        orderBy: thought.updatedAt,
        data: thought,
      })),
    ]
      // 按时间倒序排序
      .sort((a, b) => b.orderBy.getTime() - a.orderBy.getTime())
      // 取前 number 个
      .slice(0, number);

    this.logger.log(`Found ${recentMaterials.length} recent materials`);

    return recentMaterials;
  }
}
