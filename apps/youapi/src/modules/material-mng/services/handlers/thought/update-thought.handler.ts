import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { ThoughtDto } from '../../../dto/thought.dto';
import { ThoughtRepository } from '../../../repositories/thought.repository';
import { UpdateThoughtCommand } from '../../commands/thought/update-thought.command';
import { ThoughtDtoService } from '../../dto-services/thought-dto.service';

@CommandHandler(UpdateThoughtCommand)
export class UpdateThoughtHandler implements ICommandHandler<UpdateThoughtCommand> {
  constructor(
    private readonly thoughtRepository: ThoughtRepository,
    private readonly thoughtDtoService: ThoughtDtoService,
  ) {}

  async execute(command: UpdateThoughtCommand): Promise<ThoughtDto> {
    const { thoughtId, title, titleType, content } = command;

    const thought = await this.thoughtRepository.getById(thoughtId);

    thought.update({
      title,
      titleType,
      content,
    });

    await this.thoughtRepository.save(thought);

    return this.thoughtDtoService.toDto(thought);
  }
}
