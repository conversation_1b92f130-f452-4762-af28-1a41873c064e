import { Injectable, Logger } from '@nestjs/common';
import { FileDomainService } from '@/domain/file';
import { Article } from '../../domain/snip/models/article.entity';
import { Image } from '../../domain/snip/models/image.entity';
import { Office } from '../../domain/snip/models/office.entity';
import { OtherWebpage } from '../../domain/snip/models/other-webpage.entity';
import { PDF } from '../../domain/snip/models/pdf.entity';
import { Snip } from '../../domain/snip/models/snip.entity';
import { Snippet } from '../../domain/snip/models/snippet.entity';
import { TextFile } from '../../domain/snip/models/text-file.entity';
import { UnknownWebpage } from '../../domain/snip/models/unknown-webpage.entity';
import { Video } from '../../domain/snip/models/video.entity';
import { Voice } from '../../domain/snip/models/voice.entity';
import { ReaderHTMLContent } from '../../domain/snip/value-objects/content.vo';
import { ArticleDto } from '../../dto/snip/article.dto';
import { BoardItemDto } from '../../dto/snip/board-item.dto';
import { FileMetaDto, ImageDto } from '../../dto/snip/image.dto';
import { OfficeDto, OfficeFileDto } from '../../dto/snip/office.dto';
import { OtherWebpageDto } from '../../dto/snip/other-webpage.dto';
import { PdfDto } from '../../dto/snip/pdf.dto';
import { SnipDto } from '../../dto/snip/snip.dto';
import { SnippetDto } from '../../dto/snip/snippet.dto';
import { TextDto } from '../../dto/snip/text.dto';
import { UnknownWebpageDto } from '../../dto/snip/unknown-webpage.dto';
import { VideoDto } from '../../dto/snip/video.dto';
import { VoiceDto } from '../../dto/snip/voice.dto';
import { SnipContentDto } from '../../dto/snip-content.dto';

/**
 * Snip DTO Service - 片段 DTO 转换服务
 * 负责将 Snip 领域实体转换为 DTO 对象
 */
@Injectable()
export class SnipDtoService {
  private static readonly logger = new Logger(SnipDtoService.name);

  constructor(private readonly fileDomainService: FileDomainService) {}

  /**
   * Helper method to create DTO with proper discriminator property
   */
  private createDto<T>(DtoClass: new () => T, data: Partial<T>): T {
    return Object.assign(new DtoClass(), data);
  }
  /**
   * 转换 Article 实体为 ArticleDto
   */
  toArticleDto(article: Article): ArticleDto {
    // 转换 BoardItem 信息
    const boardItem: BoardItemDto | undefined = article.boardItem
      ? {
          id: article.boardItem.id,
          boardId: article.boardItem.boardId,
          parentBoardGroupId: article.boardItem.parentBoardGroupId,
          boardGroupId: article.boardItem.boardGroupId,
          rank: article.boardItem.rank,
          createdAt: article.boardItem.createdAt,
          updatedAt: article.boardItem.updatedAt,
        }
      : undefined;

    return this.createDto(ArticleDto, {
      id: article.id,
      title: article.title,
      createdAt: article.createdAt,
      updatedAt: article.updatedAt,
      type: article.type,
      from: article.from,
      extra: article.extra,
      boardIds: article.boardItem?.boardId ? [article.boardItem.boardId] : [],
      status: article.status,
      boardItem: boardItem,
      visibility: article.visibility,
      spaceId: article.spaceId,
      creatorId: article.creatorId,
      boardId: article.boardItem?.boardId,
      // Article 特有字段
      playUrl: article.playUrl,
      webpage: article.webpage!,
      content: this.convertReaderHTMLContentToDto(article.content!),
      authors: article.authors,
      heroImageUrl: article.heroImageUrl,
      publishedAt: article.publishedAt,
    });
  }

  /**
   * 转换 Snippet 实体为 SnippetDto
   */
  toSnippetDto(snippet: Snippet): SnippetDto {
    SnipDtoService.logger.debug(`snippet = ${JSON.stringify(snippet)}`);
    // 转换 BoardItem 信息
    const boardItem: BoardItemDto | undefined = snippet.boardItem
      ? {
          id: snippet.boardItem.id,
          boardId: snippet.boardItem.boardId,
          parentBoardGroupId: snippet.boardItem.parentBoardGroupId,
          boardGroupId: snippet.boardItem.boardGroupId,
          rank: snippet.boardItem.rank,
          createdAt: snippet.boardItem.createdAt,
          updatedAt: snippet.boardItem.updatedAt,
        }
      : undefined;

    return this.createDto(SnippetDto, {
      id: snippet.id,
      title: snippet.title,
      createdAt: snippet.createdAt,
      updatedAt: snippet.updatedAt,
      type: snippet.type,
      from: snippet.from,
      extra: snippet.extra,
      boardIds: snippet.boardItem?.boardId ? [snippet.boardItem.boardId] : [],
      status: snippet.status,
      boardItem: boardItem,
      visibility: snippet.visibility,
      spaceId: snippet.spaceId,
      creatorId: snippet.creatorId,
      boardId: snippet.boardItem?.boardId,
      // Snippet 特有字段
      parentId: snippet.parentId,
      webpage: snippet.webpage,
      content: this.convertReaderHTMLContentToDto(snippet.content),
      selection: {
        matchText: snippet.selection.matchText,
        matchIndex: snippet.selection.matchIndex,
        hashID: snippet.selection.hashID, // 保持历史原因，DTO 和实体都使用 hashID
        color: snippet.selection.color,
      },
      // TODO: 添加 annotation 支持
      // annotation: snippet.annotation,
    });
  }

  /**
   * 转换 Image 实体为 ImageDto
   */
  toImageDto(image: Image): ImageDto {
    // 转换 BoardItem 信息
    const boardItem: BoardItemDto | undefined = image.boardItem
      ? {
          id: image.boardItem.id,
          boardId: image.boardItem.boardId,
          parentBoardGroupId: image.boardItem.parentBoardGroupId,
          boardGroupId: image.boardItem.boardGroupId,
          rank: image.boardItem.rank,
          createdAt: image.boardItem.createdAt,
          updatedAt: image.boardItem.updatedAt,
        }
      : undefined;

    // 转换文件元信息 - 根据不同文件类型处理
    let url: string | undefined;
    if (image.isUntransferredFile()) {
      // 未转存的文件使用 originalUrl
      url = 'originalUrl' in image.file ? image.file.originalUrl : undefined;
    } else if ('storageUrl' in image.file && image.file.storageUrl) {
      // 已转存的文件使用 fileDomainService 转换 storageUrl
      url = this.fileDomainService.getFileUrlByStorageUrl(image.file.storageUrl);
    }

    const file: FileMetaDto = {
      name: 'name' in image.file ? image.file.name : 'untitled',
      mimeType: 'mimeType' in image.file ? image.file.mimeType : 'image/jpeg',
      size: 'size' in image.file ? image.file.size : 0,
      storageUrl: 'storageUrl' in image.file ? image.file.storageUrl : '',
      url: url,
      originalUrl: 'originalUrl' in image.file ? image.file.originalUrl : undefined,
    };

    return this.createDto(ImageDto, {
      id: image.id,
      title: image.title,
      description: undefined, // Image entity doesn't have description field
      createdAt: image.createdAt,
      updatedAt: image.updatedAt,
      type: image.type,
      from: image.from,
      extra: image.extra,
      boardIds: image.boardItem?.boardId ? [image.boardItem.boardId] : [],
      status: image.status,
      boardItem: boardItem,
      visibility: image.visibility,
      file: file,
      webpage: image.webpage,
      spaceId: image.spaceId,
      creatorId: image.creatorId,
    });
  }

  /**
   * 转换 Voice 实体为 VoiceDto
   */
  toVoiceDto(voice: Voice): VoiceDto {
    // 转换 BoardItem 信息
    const boardItem: BoardItemDto | undefined = voice.boardItem
      ? {
          id: voice.boardItem.id,
          boardId: voice.boardItem.boardId,
          parentBoardGroupId: voice.boardItem.parentBoardGroupId,
          boardGroupId: voice.boardItem.boardGroupId,
          rank: voice.boardItem.rank,
          createdAt: voice.boardItem.createdAt,
          updatedAt: voice.boardItem.updatedAt,
        }
      : undefined;

    // 构建文件对象，包含 url 字段
    let url: string | undefined;
    if (voice.file?.storageUrl) {
      // Voice 文件通常都是已转存的，使用 fileDomainService 转换 storageUrl
      url = this.fileDomainService.getFileUrlByStorageUrl(voice.file.storageUrl);
    }

    const file = voice.file
      ? {
          name: voice.file.name,
          mimeType: voice.file.mimeType,
          size: voice.file.size,
          storageUrl: voice.file.storageUrl,
          url: url,
          originalUrl: 'originalUrl' in voice.file ? (voice.file.originalUrl as string) : undefined,
        }
      : undefined;

    return this.createDto(VoiceDto, {
      id: voice.id,
      title: voice.title,
      createdAt: voice.createdAt,
      updatedAt: voice.updatedAt,
      spaceId: voice.spaceId,
      creatorId: voice.creatorId,
      type: voice.type,
      from: voice.from,
      status: voice.status,
      extra: voice.extra,
      boardItem: boardItem,
      boardIds: voice.boardItem?.boardId ? [voice.boardItem.boardId] : [],
      visibility: voice.visibility,
      webpage: voice.webpage
        ? {
            url: voice.webpage.getUrl(),
            normalizedUrl: voice.webpage.getNormalizedUrl(),
            title: voice.webpage.getTitle(),
            description: voice.webpage.getDescription(),
            site: voice.webpage.getSite(),
          }
        : undefined,
      playUrl: voice.playUrl,
      // 只保留 file 对象
      file: file,
      // 内容相关字段映射
      authors: voice.authors,
      heroImageUrl: voice.heroImageUrl,
      publishedAt: voice.publishedAt,
      showNotes: voice.showNotes
        ? {
            format: voice.showNotes.getFormat(),
            raw: voice.showNotes.getRaw(),
            plain: voice.showNotes.getPlain(),
            language: voice.showNotes.getLanguage(),
          }
        : undefined,
    });
  }

  /**
   * 转换 Video 实体为 VideoDto
   */
  toVideoDto(video: Video): VideoDto {
    // 转换 BoardItem 信息
    const boardItem: BoardItemDto | undefined = video.boardItem
      ? {
          id: video.boardItem.id,
          boardId: video.boardItem.boardId,
          parentBoardGroupId: video.boardItem.parentBoardGroupId,
          boardGroupId: video.boardItem.boardGroupId,
          rank: video.boardItem.rank,
          createdAt: video.boardItem.createdAt,
          updatedAt: video.boardItem.updatedAt,
        }
      : undefined;

    return this.createDto(VideoDto, {
      id: video.id,
      title: video.title,
      createdAt: video.createdAt,
      updatedAt: video.updatedAt,
      type: video.type,
      from: video.from,
      extra: video.extra,
      boardIds: video.boardItem?.boardId ? [video.boardItem.boardId] : [],
      status: video.status,
      boardItem: boardItem,
      visibility: video.visibility,
      playUrl: video.playUrl,
      webpage: video.webpage!,
      description: this.convertReaderHTMLContentToDto(video.description!),
      authors: video.authors,
      heroImageUrl: video.heroImageUrl,
      publishedAt: video.publishedAt,
      spaceId: video.spaceId,
      creatorId: video.creatorId,
      boardId: video.boardItem?.boardId,
    });
  }

  /**
   * 转换 Office 实体为 OfficeDto
   */
  toOfficeDto(office: Office): OfficeDto {
    // 转换 BoardItem 信息
    const boardItem: BoardItemDto | undefined = office.boardItem
      ? {
          id: office.boardItem.id,
          boardId: office.boardItem.boardId,
          parentBoardGroupId: office.boardItem.parentBoardGroupId,
          boardGroupId: office.boardItem.boardGroupId,
          rank: office.boardItem.rank,
          createdAt: office.boardItem.createdAt,
          updatedAt: office.boardItem.updatedAt,
        }
      : undefined;

    // 转换文件元信息 - 保持为 file 对象
    let url: string | undefined;
    if (office.file?.storageUrl) {
      // Office 文件通常都是已转存的，使用 fileDomainService 转换 storageUrl
      url = this.fileDomainService.getFileUrlByStorageUrl(office.file.storageUrl);
    }

    const file: OfficeFileDto = {
      name: 'name' in office.file ? office.file.name : '',
      mimeType: 'mimeType' in office.file ? office.file.mimeType : '',
      size: 'size' in office.file ? office.file.size : 0,
      storageUrl: 'storageUrl' in office.file ? office.file.storageUrl : '',
      url: url,
      originalUrl: 'originalUrl' in office.file ? (office.file.originalUrl as string) : undefined,
    };

    return this.createDto(OfficeDto, {
      id: office.id,
      title: office.title,
      createdAt: office.createdAt,
      updatedAt: office.updatedAt,
      type: office.type,
      from: office.from,
      status: office.status,
      extra: office.extra,
      boardItem: boardItem,
      visibility: office.visibility,
      content: office.content ? this.convertReaderHTMLContentToDto(office.content) : undefined,
      // 保持为 file 对象而不是拆分成多个字段
      file: file,
      spaceId: office.spaceId,
      creatorId: office.creatorId,
      boardIds: office.boardItem?.boardId ? [office.boardItem.boardId] : [],
    });
  }

  /**
   * 转换 PDF 实体为 PdfDto
   */
  toPdfDto(pdf: PDF): PdfDto {
    // 转换 BoardItem 信息
    const boardItem: BoardItemDto | undefined = pdf.boardItem
      ? {
          id: pdf.boardItem.id,
          boardId: pdf.boardItem.boardId,
          parentBoardGroupId: pdf.boardItem.parentBoardGroupId,
          boardGroupId: pdf.boardItem.boardGroupId,
          rank: pdf.boardItem.rank,
          createdAt: pdf.boardItem.createdAt,
          updatedAt: pdf.boardItem.updatedAt,
        }
      : undefined;

    // 构建文件对象，包含 url 字段
    let url: string | undefined;
    if (pdf.file?.storageUrl) {
      // PDF 文件通常都是已转存的，使用 fileDomainService 转换 storageUrl
      url = this.fileDomainService.getFileUrlByStorageUrl(pdf.file.storageUrl);
    }

    const file = pdf.file
      ? {
          name: pdf.file.name,
          mimeType: pdf.file.mimeType,
          size: pdf.file.size,
          storageUrl: pdf.file.storageUrl,
          url: url,
          originalUrl: 'originalUrl' in pdf.file ? (pdf.file.originalUrl as string) : undefined,
        }
      : undefined;

    return this.createDto(PdfDto, {
      id: pdf.id,
      title: pdf.title,
      createdAt: pdf.createdAt,
      updatedAt: pdf.updatedAt,
      spaceId: pdf.spaceId,
      creatorId: pdf.creatorId,
      type: pdf.type,
      from: pdf.from,
      status: pdf.status,
      parentId: pdf.parentId,
      boardIds: pdf.boardItem?.boardId ? [pdf.boardItem.boardId] : [],
      extra: pdf.extra,
      boardItem: boardItem,
      visibility: pdf.visibility,
      content: pdf.content ? this.convertReaderHTMLContentToDto(pdf.content) : undefined,
      // 只保留 file 对象
      file: file,
      authors: pdf.authors,
      heroImageUrl: pdf.heroImageUrl,
      publishedAt: pdf.publishedAt,
    });
  }

  /**
   * 转换 TextFile 实体为 TextDto
   */
  toTextDto(textFile: TextFile): TextDto {
    // 转换 BoardItem 信息
    const boardItem: BoardItemDto | undefined = textFile.boardItem
      ? {
          id: textFile.boardItem.id,
          boardId: textFile.boardItem.boardId,
          parentBoardGroupId: textFile.boardItem.parentBoardGroupId,
          boardGroupId: textFile.boardItem.boardGroupId,
          rank: textFile.boardItem.rank,
          createdAt: textFile.boardItem.createdAt,
          updatedAt: textFile.boardItem.updatedAt,
        }
      : undefined;

    // 转换文件元信息
    let url: string | undefined;
    if (textFile.file?.storageUrl) {
      // Text 文件通常都是已转存的，使用 fileDomainService 转换 storageUrl
      url = this.fileDomainService.getFileUrlByStorageUrl(textFile.file.storageUrl);
    }

    const file: FileMetaDto = {
      name: textFile.file.name,
      size: textFile.file.size,
      mimeType: textFile.file.mimeType,
      storageUrl: textFile.file.storageUrl,
      url: url,
      originalUrl:
        'originalUrl' in textFile.file ? (textFile.file.originalUrl as string) : undefined,
    };

    return this.createDto(TextDto, {
      id: textFile.id,
      title: textFile.title,
      createdAt: textFile.createdAt,
      updatedAt: textFile.updatedAt,
      type: textFile.type,
      from: textFile.from,
      status: textFile.status,
      boardItem: boardItem,
      visibility: textFile.visibility,
      file: file,
      content: this.convertReaderHTMLContentToDto(textFile.content),
      spaceId: textFile.spaceId,
      creatorId: textFile.creatorId,
      boardIds: textFile.boardItem?.boardId ? [textFile.boardItem.boardId] : [],
    });
  }

  /**
   * 转换 UnknownWebpage 实体为 UnknownWebpageDto
   */
  toUnknownWebpageDto(unknownWebpage: UnknownWebpage): UnknownWebpageDto {
    // 转换 BoardItem 信息
    const boardItem: BoardItemDto | undefined = unknownWebpage.boardItem
      ? {
          id: unknownWebpage.boardItem.id,
          boardId: unknownWebpage.boardItem.boardId,
          parentBoardGroupId: unknownWebpage.boardItem.parentBoardGroupId,
          boardGroupId: unknownWebpage.boardItem.boardGroupId,
          rank: unknownWebpage.boardItem.rank,
          createdAt: unknownWebpage.boardItem.createdAt,
          updatedAt: unknownWebpage.boardItem.updatedAt,
        }
      : undefined;

    return this.createDto(UnknownWebpageDto, {
      id: unknownWebpage.id,
      createdAt: unknownWebpage.createdAt,
      updatedAt: unknownWebpage.updatedAt,
      type: unknownWebpage.type,
      from: unknownWebpage.from,
      title: unknownWebpage.title,
      webpage: {
        url: unknownWebpage.webpage.getUrl(),
        normalizedUrl: unknownWebpage.webpage.getNormalizedUrl(),
        title: unknownWebpage.webpage.getTitle(),
        description: unknownWebpage.webpage.getDescription(),
        site: {
          name: unknownWebpage.webpage.getSiteName(),
          host: unknownWebpage.webpage.getSiteHost(),
          faviconUrl: unknownWebpage.webpage.getSiteFaviconUrl(),
        },
      },
      extra: unknownWebpage.extra,
      status: unknownWebpage.status,
      visibility: unknownWebpage.visibility,
      boardIds: unknownWebpage.boardItem?.boardId ? [unknownWebpage.boardItem.boardId] : [],
      boardItem: boardItem,
    });
  }

  /**
   * 转换 OtherWebpage 实体为 OtherWebpageDto
   */
  toOtherWebpageDto(otherWebpage: OtherWebpage): OtherWebpageDto {
    // 转换 BoardItem 信息
    const boardItem: BoardItemDto | undefined = otherWebpage.boardItem
      ? {
          id: otherWebpage.boardItem.id,
          boardId: otherWebpage.boardItem.boardId,
          parentBoardGroupId: otherWebpage.boardItem.parentBoardGroupId,
          boardGroupId: otherWebpage.boardItem.boardGroupId,
          rank: otherWebpage.boardItem.rank,
          createdAt: otherWebpage.boardItem.createdAt,
          updatedAt: otherWebpage.boardItem.updatedAt,
        }
      : undefined;

    return this.createDto(OtherWebpageDto, {
      id: otherWebpage.id,
      createdAt: otherWebpage.createdAt,
      updatedAt: otherWebpage.updatedAt,
      type: otherWebpage.type,
      from: otherWebpage.from,
      title: otherWebpage.title,
      webpage: {
        url: otherWebpage.webpage.getUrl(),
        normalizedUrl: otherWebpage.webpage.getNormalizedUrl(),
        title: otherWebpage.webpage.getTitle(),
        description: otherWebpage.webpage.getDescription(),
        site: {
          name: otherWebpage.webpage.getSiteName(),
          host: otherWebpage.webpage.getSiteHost(),
          faviconUrl: otherWebpage.webpage.getSiteFaviconUrl(),
        },
      },
      extra: otherWebpage.extra,
      boardIds: otherWebpage.boardItem?.boardId ? [otherWebpage.boardItem.boardId] : [],
      status: otherWebpage.status,
      boardItem: boardItem,
      visibility: otherWebpage.visibility,
    });
  }

  /**
   * 通用的 Snip DTO 转换方法
   */
  toDto<T extends Snip>(snip: T): SnipDto {
    // 基础字段
    const baseDto = <SnipDto>{
      id: snip.id,
      createdAt: snip.createdAt,
      updatedAt: snip.updatedAt,
      spaceId: snip.spaceId,
      creatorId: snip.creatorId,
      type: snip.type,
      from: snip.from,
      title: snip.title,
      status: snip.status,
      visibility: snip.visibility,
      boardId: snip.boardItem?.boardId,
      boardIds: snip.boardItem?.boardId ? [snip.boardItem.boardId] : [],
      boardItem: snip.boardItem
        ? {
            id: snip.boardItem.id,
            boardId: snip.boardItem.boardId,
            parentBoardGroupId: snip.boardItem.parentBoardGroupId,
            boardGroupId: snip.boardItem.boardGroupId,
            rank: snip.boardItem.rank,
            createdAt: snip.boardItem.createdAt,
            updatedAt: snip.boardItem.updatedAt,
          }
        : undefined,
    };

    // 如果是 Article 类型，返回完整的 ArticleDto
    if (snip instanceof Article) {
      return this.toArticleDto(snip);
    }

    // 如果是 Snippet 类型，返回完整的 SnippetDto
    if (snip instanceof Snippet) {
      return this.toSnippetDto(snip);
    }

    // 如果是 Image 类型，返回完整的 ImageDto
    if (snip instanceof Image) {
      return this.toImageDto(snip);
    }

    // 如果是 Voice 类型，返回完整的 VoiceDto
    if (snip instanceof Voice) {
      return this.toVoiceDto(snip);
    }

    // 如果是 Video 类型，返回完整的 VideoDto
    if (snip instanceof Video) {
      return this.toVideoDto(snip);
    }

    // 如果是 Office 类型，返回完整的 OfficeDto
    if (snip instanceof Office) {
      return this.toOfficeDto(snip);
    }

    // 如果是 PDF 类型，返回完整的 PdfDto
    if (snip instanceof PDF) {
      return this.toPdfDto(snip);
    }

    // 如果是 TextFile 类型，返回完整的 TextDto
    if (snip instanceof TextFile) {
      return this.toTextDto(snip);
    }

    // 如果是 OtherWebpage 类型，返回完整的 OtherWebpageDto
    if (snip instanceof OtherWebpage) {
      return this.toOtherWebpageDto(snip);
    }

    // 如果是 UnknownWebpage 类型，返回完整的 UnknownWebpageDto
    if (snip instanceof UnknownWebpage) {
      return this.toUnknownWebpageDto(snip);
    }

    return baseDto;
  }

  toDtoList(snips: Snip[]): SnipDto[] {
    return snips.map((snip) => this.toDto(snip));
  }

  /**
   * 将 ReaderHTMLContent 领域对象转换为 SnipContentDto
   */
  private convertReaderHTMLContentToDto(content: ReaderHTMLContent): SnipContentDto {
    if (content == null) {
      return undefined;
    }
    return this.createDto(SnipContentDto, {
      format: content.getFormat(),
      language: content.getLanguage(),
      raw: content.getRaw(),
      plain: content.getPlain(),
    });
  }
}
