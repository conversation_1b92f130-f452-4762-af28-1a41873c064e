import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiExtraModels, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BaseController } from '@/shared/base.controller';
import { ListMaterialsResponseDto } from '../dto/material/list-materials.dto';
import {
  ListRecentMaterialsDto,
  RecentMaterialItemDto,
} from '../dto/material/list-recent-materials.dto';
import { ListUnusedMaterialsResponseDto } from '../dto/material/list-unused-materials.dto';
import { ListUnusedMaterialsForMobileResponseDto } from '../dto/material/list-unused-materials-for-mobile.dto';
import {
  PublishMaterialDto,
  PublishMaterialResponseDto,
} from '../dto/material/publish-material.dto';
import {
  SaveSharedMaterialDto,
  SaveSharedMaterialResponseDto,
} from '../dto/material/save-shared-material.dto';
import { UnpublishMaterialDto } from '../dto/material/unpublish-material.dto';
import { ThoughtDto } from '../dto/thought.dto';
import { PublishMaterialCommand } from '../services/commands/material/publish-material.command';
import { SaveSharedMaterialCommand } from '../services/commands/material/save-shared-material.command';
import { UnpublishMaterialCommand } from '../services/commands/material/unpublish-material.command';
import { ListMaterialsQuery } from '../services/queries/material/list-materials.query';
import { ListRecentMaterialsQuery } from '../services/queries/material/list-recent-materials.query';
import { ListUnusedMaterialsQuery } from '../services/queries/material/list-unused-materials.query';
import { ListUnusedMaterialsForMobileQuery } from '../services/queries/material/list-unused-materials-for-mobile.query';

@ApiTags('Material')
@Controller('api/v1/material')
@ApiExtraModels(RecentMaterialItemDto, ThoughtDto)
export class MaterialController extends BaseController {
  @Post('listMaterials')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取材料列表',
    description: '获取材料，包括 snips、thoughts 和 chats',
  })
  @ApiResponse({
    status: 200,
    description: '成功返回材料列表',
    type: ListMaterialsResponseDto,
  })
  async listMaterials(): Promise<ListMaterialsResponseDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new ListMaterialsQuery(userId, spaceId);
    return this.queryBus.execute(query);
  }

  @Post('listRecentMaterials')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取最近材料列表',
    description: '按时间顺序获取最近的材料（snips 和 thoughts），支持按数量和是否包含归档内容过滤',
  })
  @ApiResponse({
    status: 200,
    description: '成功返回最近材料列表',
    type: [RecentMaterialItemDto],
  })
  async listRecentMaterials(@Body() dto: ListRecentMaterialsDto): Promise<RecentMaterialItemDto[]> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new ListRecentMaterialsQuery(
      userId,
      spaceId,
      dto.number,
      dto.includeArchived || false,
    );
    return this.queryBus.execute(query);
  }

  @Post('listUnusedMaterials')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取未使用的材料列表',
    description: '获取未使用的材料，包括 snips 和 thoughts（未添加到任何 board 中的材料）',
  })
  @ApiResponse({
    status: 200,
    description: '成功返回未使用的材料列表',
    type: ListUnusedMaterialsResponseDto,
  })
  async listUnusedMaterials(): Promise<ListUnusedMaterialsResponseDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new ListUnusedMaterialsQuery(userId, spaceId);
    return this.queryBus.execute(query);
  }

  @Post('listUnusedMaterialsForMobile')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取未使用的材料列表（移动端）',
    description:
      '获取未使用的材料，包括 snips 和 thoughts（未添加到任何 board 中的材料），每种类型限制 20 条',
  })
  @ApiResponse({
    status: 200,
    description: '成功返回未使用的材料列表（移动端）',
    type: ListUnusedMaterialsForMobileResponseDto,
  })
  async listUnusedMaterialsForMobile(): Promise<ListUnusedMaterialsForMobileResponseDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new ListUnusedMaterialsForMobileQuery(userId, spaceId);
    return this.queryBus.execute(query);
  }

  @Post('publish')
  @HttpCode(200)
  @ApiOperation({
    summary: '发布材料',
    description: '将材料（snip、thought、chat）设置为公开可见，并生成分享链接',
  })
  @ApiResponse({
    status: 200,
    description: '成功发布材料，返回短链接信息',
    type: PublishMaterialResponseDto,
  })
  async publish(@Body() dto: PublishMaterialDto): Promise<PublishMaterialResponseDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new PublishMaterialCommand(userId, spaceId, dto.id, dto.type);
    return this.commandBus.execute(command);
  }

  @Post('unpublish')
  @HttpCode(200)
  @ApiOperation({
    summary: '取消发布材料',
    description: '将材料（snip、thought）从公开状态改为私有状态，并停用关联的短链接',
  })
  @ApiResponse({
    status: 200,
    description: '成功取消发布材料',
  })
  async unpublish(@Body() dto: UnpublishMaterialDto): Promise<void> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new UnpublishMaterialCommand(dto.id, dto.type, spaceId, userId);
    await this.commandBus.execute(command);
  }

  @Post('saveShared')
  @HttpCode(200)
  @ApiOperation({
    summary: '保存分享的材料',
    description: '通过短链接ID将分享的材料（snip、thought）保存到指定看板，支持跨空间克隆',
  })
  @ApiResponse({
    status: 200,
    description: '成功保存分享的材料',
    type: SaveSharedMaterialResponseDto,
  })
  async saveShared(@Body() dto: SaveSharedMaterialDto): Promise<SaveSharedMaterialResponseDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new SaveSharedMaterialCommand(dto.boardId, dto.shortId, spaceId, userId);
    return this.commandBus.execute(command);
  }
}
