import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';

/**
 * V2 API Shortcut DTO - 快捷指令数据传输对象
 */
export class ShortcutV2Dto {
  @ApiProperty({ description: 'Shortcut ID' })
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp' })
  updatedAt: Date;

  @ApiProperty({ description: 'Creator user ID' })
  creatorId: string;

  @ApiProperty({ description: 'Shortcut name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Prompt content' })
  @IsString()
  prompt: string;

  @ApiProperty({ description: 'Rank for ordering' })
  @IsString()
  rank: string;
}

/**
 * V2 API 创建快捷指令请求 DTO
 * 对应 youapp 的 CreateShortcutParamSchema
 */
export class CreateShortcutV2Dto {
  @ApiProperty({
    description: 'Shortcut name',
    example: 'Translate to English',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Prompt content',
    example: 'Please translate the following text to English: {text}',
  })
  @IsString()
  prompt: string;
}

/**
 * V2 API 创建快捷指令响应 DTO
 */
export class CreateShortcutV2ResponseDto {
  @ApiProperty({
    description: 'Created shortcut details',
    type: ShortcutV2Dto,
  })
  @ValidateNested()
  @Type(() => ShortcutV2Dto)
  shortcut: ShortcutV2Dto;
}

/**
 * V2 API 删除快捷指令请求 DTO
 * 对应 youapp 的 DeleteShortcutParamSchema
 */
export class DeleteShortcutV2Dto {
  @ApiProperty({
    description: 'Shortcut ID to delete',
  })
  @IsUUID()
  id: string;
}

/**
 * V2 API 列出快捷指令响应 DTO
 * 对应 youapp 的 ListShortcutResult
 */
export class ListShortcutsV2ResponseDto {
  @ApiProperty({
    description: 'Shortcuts data',
    type: [ShortcutV2Dto],
  })
  data: ShortcutV2Dto[];

  @ApiProperty({
    description: 'Pagination information',
    properties: {
      current: { type: 'number', description: 'Current page (0-based)' },
      pageSize: { type: 'number', description: 'Page size' },
      total: { type: 'number', description: 'Total number of shortcuts' },
    },
  })
  paging: {
    current: number;
    pageSize: number;
    total: number;
  };
}

/**
 * V2 API 移动快捷指令请求 DTO
 * 对应 youapp 的 MoveShortcutParamSchema
 */
export class MoveShortcutV2Dto {
  @ApiProperty({
    description: 'Shortcut ID to move',
  })
  @IsUUID()
  id: string;

  @ApiPropertyOptional({
    description: 'ID of the shortcut to move after (optional, if not provided moves to first)',
  })
  @IsOptional()
  @IsUUID()
  rankAfterId?: string;
}

/**
 * V2 API 编辑快捷指令请求 DTO
 * 对应 youapp 的 PatchShortcutParamSchema
 */
export class PatchShortcutV2Dto {
  @ApiProperty({
    description: 'Shortcut ID to update',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Updated shortcut name',
    example: 'Translate to Chinese',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Updated prompt content',
    example: 'Please translate the following text to Chinese: {text}',
  })
  @IsString()
  prompt: string;
}
