/**
 * Chat Detail V2 DTO - V2 API 聊天详情数据传输对象
 *
 * This is a properly structured version with:
 * - All DTOs have both @ApiProperty AND validation decorators
 * - Consistent decorator ordering
 * - Proper naming (no single DTOs returning arrays)
 * - Clear type definitions for union types and arrays
 *
 * V2 API 使用现代的 blocks 结构：
 * - Assistant消息使用 blocks 数组来表示内容、推理和工具调用
 * - 不再使用 content, reasoning, events 字段
 * - 更灵活和可扩展的数据结构
 */

import { ApiProperty, ApiPropertyOptional, getSchemaPath } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { DISCRIMINATOR_DEFAULT_NAME, SwaggerBaseDto } from '@/common/base/swagger-base-dto';
import {
  ChatModeEnum,
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  LLMs,
  MessageModeEnum,
  MessageRoleEnum,
  MessageStatusEnum,
} from '@/common/types';
import {
  AtReferenceDto,
  ChatOriginDto,
  EditCommandDto,
  ShortcutDto,
  UseToolsDto,
} from '../chat.dto';

/**
 * V2 API 完成块基础格式
 * All completion blocks extend this base class
 */
export class CompletionBlockV2Dto extends SwaggerBaseDto {
  @ApiProperty({ description: 'Block ID', format: 'uuid' })
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Creation timestamp', type: Date })
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp', type: Date })
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  @ApiProperty({
    description: 'Block type',
    enum: CompletionBlockTypeEnum,
    enumName: 'CompletionBlockTypeEnum',
  })
  @IsEnum(CompletionBlockTypeEnum)
  type: CompletionBlockTypeEnum;

  @ApiProperty({
    description: 'Block status',
    enum: CompletionBlockStatusEnum,
    enumName: 'CompletionBlockStatusEnum',
  })
  @IsEnum(CompletionBlockStatusEnum)
  status: CompletionBlockStatusEnum;

  @ApiProperty({ description: 'Message ID this block belongs to', format: 'uuid' })
  @IsString()
  @IsUUID()
  messageId: string;

  @ApiPropertyOptional({ description: 'Extra metadata', type: Object })
  @IsOptional()
  @IsObject()
  extra?: Record<string, any>;
}

/**
 * V2 API 内容块格式
 */
export class ContentBlockV2Dto extends CompletionBlockV2Dto {
  @ApiProperty({
    description: 'Block type',
    enum: CompletionBlockTypeEnum,
    enumName: 'CompletionBlockTypeEnum',
  })
  @IsEnum([CompletionBlockTypeEnum.CONTENT])
  declare type: CompletionBlockTypeEnum.CONTENT;

  @ApiProperty({ description: 'Content data' })
  @IsString()
  data: string;
}

/**
 * V2 API 推理块格式
 */
export class ReasoningBlockV2Dto extends CompletionBlockV2Dto {
  @ApiProperty({
    description: 'Block type',
    enum: CompletionBlockTypeEnum,
    enumName: 'CompletionBlockTypeEnum',
  })
  @IsEnum([CompletionBlockTypeEnum.REASONING])
  declare type: CompletionBlockTypeEnum.REASONING;

  @ApiProperty({ description: 'Reasoning data' })
  @IsString()
  data: string;
}

/**
 * V2 API 工具块格式
 */
export class ToolBlockV2Dto extends CompletionBlockV2Dto {
  @ApiProperty({
    description: 'Block type',
    enum: CompletionBlockTypeEnum,
    enumName: 'CompletionBlockTypeEnum',
  })
  @IsEnum([CompletionBlockTypeEnum.TOOL])
  declare type: CompletionBlockTypeEnum.TOOL;

  @ApiProperty({ description: 'Tool ID', format: 'uuid' })
  @IsString()
  @IsUUID()
  toolId: string;

  @ApiProperty({ description: 'Tool name' })
  @IsString()
  toolName: string;

  @ApiPropertyOptional({ description: 'Tool arguments', type: Object })
  @IsOptional()
  @IsObject()
  toolArguments?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Tool execution result', type: Object })
  @IsOptional()
  @IsObject()
  toolResult?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Tool response text' })
  @IsOptional()
  @IsString()
  toolResponse?: string;

  @ApiPropertyOptional({ description: 'Tool generation time in milliseconds' })
  @IsOptional()
  @IsNumber()
  toolGenerateElapsedMs?: number;

  @ApiPropertyOptional({ description: 'Tool execution time in milliseconds' })
  @IsOptional()
  @IsNumber()
  toolExecuteElapsedMs?: number;
}

/**
 * V2 API 用户消息格式
 */
export class UserMessageV2Dto extends SwaggerBaseDto {
  @ApiProperty({ description: 'Message ID', format: 'uuid' })
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Chat ID', format: 'uuid' })
  @IsString()
  @IsUUID()
  chatId: string;

  @ApiProperty({ description: 'Creation timestamp', type: Date })
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp', type: Date })
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  @ApiProperty({
    description: 'Message role',
    enum: MessageRoleEnum,
    enumName: 'MessageRoleEnum',
  })
  @IsEnum([MessageRoleEnum.USER])
  role: MessageRoleEnum.USER;

  @ApiProperty({
    description: 'Message status',
    enum: MessageStatusEnum,
    enumName: 'MessageStatusEnum',
  })
  @IsEnum(MessageStatusEnum)
  status: MessageStatusEnum;

  @ApiProperty({ description: 'Message content' })
  @IsString()
  content: string;

  @ApiProperty({ description: 'Message origin context', type: () => ChatOriginDto })
  @ValidateNested()
  @Type(() => ChatOriginDto)
  origin: ChatOriginDto;

  @ApiPropertyOptional({ description: 'Selected text context' })
  @IsOptional()
  @IsString()
  selection?: string;

  @ApiPropertyOptional({ description: 'Referenced entities', type: [AtReferenceDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AtReferenceDto)
  atReferences?: AtReferenceDto[];

  @ApiProperty({ description: 'Board ID if applicable', format: 'uuid' })
  @IsString()
  @IsUUID()
  boardId: string;

  @ApiPropertyOptional({
    description: 'Tools configuration',
    type: () => UseToolsDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UseToolsDto)
  tools?: UseToolsDto;

  @ApiPropertyOptional({
    description: 'Edit command',
    type: () => EditCommandDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EditCommandDto)
  command?: EditCommandDto;

  @ApiProperty({
    description: 'Message mode',
    enum: MessageModeEnum,
    enumName: 'MessageModeEnum',
  })
  @IsEnum(MessageModeEnum)
  mode: MessageModeEnum;

  @ApiPropertyOptional({ description: 'Shortcut information', type: () => ShortcutDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => ShortcutDto)
  shortcut?: ShortcutDto;
}

/**
 * V2 API 助手消息格式
 * 使用现代的 blocks 结构
 */
export class AssistantMessageV2Dto extends SwaggerBaseDto {
  @ApiProperty({ description: 'Message ID', format: 'uuid' })
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Chat ID', format: 'uuid' })
  @IsString()
  @IsUUID()
  chatId: string;

  @ApiProperty({ description: 'Creation timestamp', type: Date })
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp', type: Date })
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  @ApiProperty({
    description: 'Message role',
    enum: MessageRoleEnum,
    enumName: 'MessageRoleEnum',
  })
  @IsEnum([MessageRoleEnum.ASSISTANT])
  role: MessageRoleEnum.ASSISTANT;

  @ApiProperty({
    description: 'Message status',
    enum: MessageStatusEnum,
    enumName: 'MessageStatusEnum',
  })
  @IsEnum(MessageStatusEnum)
  status: MessageStatusEnum;

  @ApiProperty({
    description: 'AI model used',
    enum: LLMs,
    enumName: 'LLMs',
  })
  @IsEnum(LLMs)
  model: LLMs;

  @ApiProperty({ description: 'Trace ID for debugging' })
  @IsString()
  traceId: string;

  @ApiProperty({
    description: 'Completion blocks containing content, reasoning, and tool calls',
    oneOf: [
      { $ref: '#/components/schemas/ContentBlockV2Dto' },
      { $ref: '#/components/schemas/ReasoningBlockV2Dto' },
      { $ref: '#/components/schemas/ToolBlockV2Dto' },
    ],
    discriminator: {
      propertyName: DISCRIMINATOR_DEFAULT_NAME,
      mapping: {
        ContentBlockV2Dto: getSchemaPath(ContentBlockV2Dto),
        ReasoningBlockV2Dto: getSchemaPath(ReasoningBlockV2Dto),
        ToolBlockV2Dto: getSchemaPath(ToolBlockV2Dto),
      },
    },
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CompletionBlockV2Dto)
  blocks: CompletionBlockV2Dto[];

  @ApiPropertyOptional({ description: 'Error information if any', type: Object })
  @IsOptional()
  @IsObject()
  error?: Record<string, any>;
}

/**
 * Union type for V2 messages
 */
export type MessageV2Dto = UserMessageV2Dto | AssistantMessageV2Dto;

/**
 * V2 API 聊天基础信息格式（不含消息）
 */
export class ChatV2Dto {
  @ApiProperty({ description: 'Chat ID', format: 'uuid' })
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Creator user ID', format: 'uuid' })
  @IsString()
  @IsUUID()
  creatorId: string;

  @ApiProperty({ description: 'Creation timestamp', type: Date })
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp', type: Date })
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  @ApiProperty({ description: 'Chat title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Chat origin context', type: () => ChatOriginDto })
  @ValidateNested()
  @Type(() => ChatOriginDto)
  origin: ChatOriginDto;

  @ApiPropertyOptional({ description: 'Board ID if applicable', format: 'uuid' })
  @IsOptional()
  @IsUUID()
  boardId?: string;

  @ApiProperty({
    description: 'Chat mode',
    enum: ChatModeEnum,
    enumName: 'ChatModeEnum',
  })
  @IsEnum(ChatModeEnum)
  mode: ChatModeEnum;

  @ApiProperty({ description: 'Whether to show new board suggestion' })
  @IsBoolean()
  showNewBoardSuggestion: boolean;

  @ApiPropertyOptional({ description: 'New board chat ID if applicable', format: 'uuid' })
  @IsOptional()
  @IsUUID()
  newBoardChatId?: string;

  @ApiPropertyOptional({
    description: 'Associated board IDs',
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsUUID('all', { each: true })
  boardIds?: string[];

  @ApiPropertyOptional({ description: 'Board item information', type: Object })
  @IsOptional()
  @IsObject()
  boardItem?: any; // TODO: Define proper BoardItem type
}

/**
 * V2 API 聊天详情格式（包含消息）
 */
export class ChatDetailV2Dto extends ChatV2Dto {
  @ApiProperty({
    description: 'Chat messages (mixed user and assistant messages)',
    type: 'array',
    items: {
      oneOf: [
        { $ref: '#/components/schemas/UserMessageV2Dto' },
        { $ref: '#/components/schemas/AssistantMessageV2Dto' },
      ],
      discriminator: {
        propertyName: DISCRIMINATOR_DEFAULT_NAME,
        mapping: {
          UserMessageV2Dto: getSchemaPath(UserMessageV2Dto),
          AssistantMessageV2Dto: getSchemaPath(AssistantMessageV2Dto),
        },
      },
    },
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object) // Since it's a union type
  messages: MessageV2Dto[];
}

/**
 * V2 API 聊天列表响应格式
 * NOTE: Properly named - this DTO returns a list, so it has "List" in the name
 */
export class ChatListV2ResponseDto {
  @ApiProperty({
    description: 'List of chats',
    type: [ChatV2Dto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatV2Dto)
  data: ChatV2Dto[];

  @ApiProperty({
    description: 'Total number of items',
  })
  @IsNumber()
  total: number;
}

/**
 * V2 API 聊天详情列表响应格式
 * NOTE: Properly named - returns a list of detailed chats
 */
export class ChatDetailListV2ResponseDto {
  @ApiProperty({
    description: 'List of chat details',
    type: [ChatDetailV2Dto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatDetailV2Dto)
  data: ChatDetailV2Dto[];

  @ApiProperty({
    description: 'Total number of items',
  })
  @IsNumber()
  total: number;
}

/**
 * Base class for send message operations
 */
export class BaseSendMessageDto {
  @ApiProperty({ description: 'Message content' })
  @IsString()
  message: string;

  @ApiPropertyOptional({ description: 'Board ID', format: 'uuid' })
  @IsOptional()
  @IsString()
  @IsUUID()
  boardId?: string;

  @ApiPropertyOptional({ description: 'Selected text context' })
  @IsOptional()
  @IsString()
  selection?: string;

  @ApiPropertyOptional({
    description: 'Referenced entities',
    type: [AtReferenceDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AtReferenceDto)
  atReferences?: AtReferenceDto[];

  @ApiPropertyOptional({
    description: 'AI model to use',
    enum: LLMs,
    enumName: 'LLMs',
  })
  @IsOptional()
  @IsEnum(LLMs)
  chatModel?: LLMs;

  @ApiPropertyOptional({
    description: 'Tools configuration',
    type: () => UseToolsDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UseToolsDto)
  tools?: UseToolsDto;

  @ApiPropertyOptional({
    description: 'Shortcut information',
    type: () => ShortcutDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ShortcutDto)
  shortcut?: ShortcutDto;

  @ApiPropertyOptional({
    description: 'Message mode',
    enum: MessageModeEnum,
    enumName: 'MessageModeEnum',
  })
  @IsOptional()
  @IsEnum(MessageModeEnum)
  messageMode?: MessageModeEnum;

  @ApiPropertyOptional({
    description: 'Edit command',
    type: () => EditCommandDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EditCommandDto)
  command?: EditCommandDto;

  @ApiPropertyOptional({
    description: 'Chat origin context',
    type: () => ChatOriginDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ChatOriginDto)
  origin?: ChatOriginDto;
}

/**
 * V2 API 创建聊天请求 DTO
 */
export class CreateChatV2Dto extends BaseSendMessageDto {
  // Inherits all properties from BaseSendMessageDto
}

/**
 * V2 API 发送消息请求 DTO
 */
export class SendMessageV2Dto extends BaseSendMessageDto {
  @ApiProperty({ description: 'Chat ID to send message to', format: 'uuid' })
  @IsString()
  @IsUUID()
  chatId: string;
}

/**
 * V2 API 创建空聊天请求 DTO
 */
export class CreateEmptyChatDto {
  @ApiProperty({
    description: 'Chat origin context',
    type: () => ChatOriginDto,
  })
  @ValidateNested()
  @Type(() => ChatOriginDto)
  origin: ChatOriginDto;

  @ApiProperty({ description: 'Board ID where the chat will be created', format: 'uuid' })
  @IsString()
  @IsUUID()
  boardId: string;

  @ApiProperty({
    description: 'Chat title',
    example: 'New Chat',
  })
  @IsString()
  title: string;
}

/**
 * V2 API 创建空聊天响应 DTO
 */
export class CreateEmptyChatResponseDto {
  @ApiProperty({
    description: 'Created chat details',
    type: () => ChatDetailV2Dto,
  })
  @ValidateNested()
  @Type(() => ChatDetailV2Dto)
  chat: ChatDetailV2Dto;
}

/**
 * V2 API 重新生成消息请求 DTO
 */
export class RegenerateMessageV2Dto {
  @ApiProperty({ description: 'Chat ID containing the message to regenerate', format: 'uuid' })
  @IsString()
  @IsUUID()
  chatId: string;

  @ApiProperty({ description: 'User message ID to regenerate from', format: 'uuid' })
  @IsString()
  @IsUUID()
  userMessageId: string;
}

/**
 * V2 API 重新生成消息响应 DTO
 */
export class RegenerateMessageV2ResponseDto {
  @ApiProperty({
    description: 'Updated chat details with regenerated message',
    type: () => ChatDetailV2Dto,
  })
  @ValidateNested()
  @Type(() => ChatDetailV2Dto)
  chat: ChatDetailV2Dto;
}

/**
 * V2 API 列出聊天历史请求 DTO
 */
export class ListChatHistoryV2Dto {
  @ApiPropertyOptional({
    description: 'Current page number (0-based)',
    minimum: 0,
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  current?: number;

  @ApiPropertyOptional({
    description: 'Page size',
    minimum: 1,
    maximum: 100,
    default: 10,
  })
  @IsOptional()
  @IsNumber()
  pageSize?: number;

  @ApiPropertyOptional({
    description: 'Chat origin filter',
    type: () => ChatOriginDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ChatOriginDto)
  origin?: ChatOriginDto;

  @ApiPropertyOptional({
    description: 'Search query for chat titles',
  })
  @IsOptional()
  @IsString()
  query?: string;
}

/**
 * Pagination information DTO
 */
export class PaginationDto {
  @ApiProperty({ description: 'Current page (0-based)', minimum: 0 })
  @IsNumber()
  current: number;

  @ApiProperty({ description: 'Page size', minimum: 1 })
  @IsNumber()
  pageSize: number;

  @ApiProperty({ description: 'Total number of items', minimum: 0 })
  @IsNumber()
  total: number;
}

/**
 * V2 API 列出聊天历史响应 DTO
 * NOTE: Properly structured with separate pagination object
 */
export class ListChatHistoryV2ResponseDto {
  @ApiProperty({
    description: 'Paginated chat history data',
    type: [ChatV2Dto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatV2Dto)
  data: ChatV2Dto[];

  @ApiProperty({
    description: 'Pagination information',
    type: () => PaginationDto,
  })
  @ValidateNested()
  @Type(() => PaginationDto)
  paging: PaginationDto;
}
