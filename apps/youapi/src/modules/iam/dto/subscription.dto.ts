import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import type { SubscriptionProvider } from '../domain/subscription/models/subscription.types';
import {
  BillingInterval,
  SubscriptionProductTier,
  SubscriptionStatus,
} from '../domain/subscription/models/subscription.types';
import { RenewChangeDto } from './renew-change.dto';

export class SubscriptionDto {
  @ApiProperty({ description: '订阅 ID' })
  id: string;

  @ApiProperty({
    description: '产品等级',
    enum: SubscriptionProductTier,
    enumName: 'SubscriptionProductTier',
  })
  productTier: SubscriptionProductTier;

  @ApiProperty({ description: '计费周期', enum: BillingInterval, enumName: 'BillingInterval' })
  billingInterval: BillingInterval;

  @ApiProperty({ description: '状态' })
  status: SubscriptionStatus;

  @ApiProperty({ description: '续期周期锚点' })
  renewCycleAnchor: Date;

  @ApiProperty({ description: '当前周期开始时间' })
  currentPeriodStart: Date;

  @ApiProperty({ description: '当前周期结束时间' })
  currentPeriodEnd: Date;

  @ApiProperty({ description: '是否到期取消' })
  cancelAtPeriodEnd: boolean;

  @ApiPropertyOptional({
    description: '续期变更',
    type: RenewChangeDto,
  })
  @IsOptional()
  renewChange?: RenewChangeDto;

  @ApiProperty({ description: '提供商' })
  provider: SubscriptionProvider;

  @ApiProperty({ description: '外部 ID' })
  externalId: string;

  @ApiPropertyOptional({ description: '未支付账单 URL' })
  @IsOptional()
  unpaidInvoiceUrl?: string;
}
