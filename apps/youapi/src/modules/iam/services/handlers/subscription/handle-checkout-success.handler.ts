import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import Stripe from 'stripe';

// Stripe expand 配置常量
const SUBSCRIPTION_EXPAND = ['subscription'];

import { Logger } from '@nestjs/common';
import { DEFAULT_NEXT } from '@/modules/iam/constants';
import { RedirectResponse } from '@/modules/iam/types/iam.types';
import { Subscription } from '../../../domain/subscription/models/subscription.entity';
import { SpaceRepository } from '../../../repositories/space.repository';
import { SubscriptionRepository } from '../../../repositories/subscription.repository';
import { HandleCheckoutSuccessCommand } from '../../commands/subscription/handle-check-out-success.command';
import { StripeHolder } from '../../stripe.holder';

@CommandHandler(HandleCheckoutSuccessCommand)
export class HandleCheckoutSuccessHandler implements ICommandHandler<HandleCheckoutSuccessCommand> {
  private readonly logger = new Logger(HandleCheckoutSuccessHandler.name);
  constructor(
    private readonly spaceRepository: SpaceRepository,
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly stripeHolder: StripeHolder,
  ) {}

  async execute(command: HandleCheckoutSuccessCommand): Promise<RedirectResponse> {
    const { sessionId } = command;

    const session = await this.stripeHolder.hongkong.checkout.sessions.retrieve(sessionId, {
      expand: SUBSCRIPTION_EXPAND,
    });

    const stripeSubscription = session.subscription as Stripe.Subscription;
    const spaceId = stripeSubscription.metadata.space_id as string;

    const redirect = `${DEFAULT_NEXT}?subscribed=true`;
    const space = await this.spaceRepository.getById(spaceId);

    // TODO 发事件，在事件处理那里发邮件

    // 获取或创建订阅
    let subscription = await this.subscriptionRepository.findBySpaceId(space.id);
    if (subscription) {
      return {
        redirect,
      };
    }

    // customer.subscription.created 事件还没到，这里主动调用一下
    if (!subscription) {
      try {
        subscription = Subscription.createFromStripeSubscription(space.id, stripeSubscription);
        await this.subscriptionRepository.save(subscription);
        subscription.commit();
      } catch (error) {
        // 这里创建失败，大概率是跟事件处理并发冲突了
        // 这里重新查询一下，如果存在，说明事件处理成功了，否则需要人工介入
        subscription = await this.subscriptionRepository.findBySpaceId(space.id);
        if (!subscription) {
          throw error;
        }
      }
    }

    return {
      redirect,
    };
  }
}
