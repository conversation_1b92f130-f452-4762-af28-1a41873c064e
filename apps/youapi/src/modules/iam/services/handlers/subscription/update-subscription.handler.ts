import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { <PERSON>Handler, ICommandHandler } from '@nestjs/cqrs';
import Stripe from 'stripe';
import { Subscription } from '@/modules/iam/domain/subscription/models/subscription.entity';
import { StripePriceLookup<PERSON>ey } from '@/modules/iam/domain/subscription/models/subscription.types';
import { UpdateSubscriptionResponseDto } from '@/modules/iam/dto/update-subscription-response.dto';
import { SubscriptionRepository } from '../../../repositories/subscription.repository';
import { UserRepository } from '../../../repositories/user.repository';
import { UpdateSubscriptionCommand } from '../../commands/update-subscription.command';
import { StripeService } from '../../stripe.service';
import { SubscriptionService } from '../../subscription.service';
import { UserDtoService } from '../../user-dto.service';

@Injectable()
@CommandHandler(UpdateSubscriptionCommand)
export class UpdateSubscriptionHandler implements ICommandHandler<UpdateSubscriptionCommand> {
  private readonly logger = new Logger(UpdateSubscriptionHandler.name);
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly userRepository: UserRepository,
    private readonly userDtoService: UserDtoService,
    private readonly stripeService: StripeService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  async execute(command: UpdateSubscriptionCommand): Promise<UpdateSubscriptionResponseDto> {
    const { spaceId, targetProductTier, targetBillingInterval, userId } = command;

    // 获取当前聚合
    const subscription = await this.subscriptionRepository.getBySpaceId(spaceId);
    const user = await this.userRepository.getById(userId);

    const stripeSubscriptionId = subscription.getStripeSubscriptionId();
    const stripeSubscription = await this.stripeService.getSubscription(stripeSubscriptionId);
    const { productTier, billingInterval } = Subscription.parseFromPriceKey(
      stripeSubscription.items.data[0].price.lookup_key as StripePriceLookupKey,
    );

    if (productTier === targetProductTier && billingInterval === targetBillingInterval) {
      // 目标计划和当前计划相同，需要处理 schedule 和 cancel_at_period_end
      const schedule = stripeSubscription.schedule as Stripe.SubscriptionSchedule | undefined;
      if (schedule) {
        await this.stripeService.releaseSubscriptionSchedule(schedule.id);
        const updatedStripeSubscription =
          await this.stripeService.getSubscription(stripeSubscriptionId);
        const result =
          await this.subscriptionService.syncFromStripeSubscription(updatedStripeSubscription);
        return {
          ...result,
          message: `Subscription is already on the requested plan, but need to release schedule.`,
        };
      } else if (stripeSubscription.cancel_at_period_end) {
        const updatedStripeSubscription =
          await this.stripeService.doNotCancelAtPeriodEnd(stripeSubscriptionId);
        const result =
          await this.subscriptionService.syncFromStripeSubscription(updatedStripeSubscription);
        return {
          ...result,
          message: `Subscription is already on the requested plan, but need to do not cancel at period end.`,
        };
      } else {
        return {
          subscription: this.userDtoService.toSubscriptionDto(subscription),
          message: `Subscription is already on the requested plan.`,
        };
      }
    } else {
      // 目标计划和当前计划不同，更新到新价格
      // 构建新的 price lookup key
      const targetPriceLookupKey = this.stripeService.getPriceLookupKey(
        targetProductTier,
        targetBillingInterval,
        user.timeZone,
      );
      // 获取 Stripe 价格
      const targetPrice = await this.stripeService.getPriceByLookupKey(targetPriceLookupKey);
      if (!targetPrice) {
        throw new BadRequestException(`Price not found for ${targetPriceLookupKey}`);
      }
      // 升级立即生效，降级和周期变更到期生效
      const isUpgrade = productTier === 'pro' && targetProductTier === 'max';
      if (isUpgrade) {
        // 升级：立即生效，重置积分和账单周期

        // 如果有 schedule，先释放，否则无法升级
        const schedule = stripeSubscription.schedule as Stripe.SubscriptionSchedule | undefined;
        if (schedule) {
          await this.stripeService.releaseSubscriptionSchedule(schedule.id);
        }
        // 如果有 cancel_at_period_end，在 upgrade 时一并取消掉，设置 cancel_at_period_end 为 false
        try {
          const updatedStripeSubscription = await this.stripeService.upgrade(
            stripeSubscriptionId,
            targetPrice.id,
          );
          const result =
            await this.subscriptionService.syncFromStripeSubscription(updatedStripeSubscription);
          return {
            ...result,
            // 如果扣款失败，会进入 past_due 状态，并且有一个 unpaid_invoice，需要客户跳转处理
            redirect: result.subscription?.unpaidInvoiceUrl,
            message: `Upgrade to ${targetProductTier} ${targetBillingInterval} immediately.`,
          };
        } catch (error) {
          this.logger.error(error, 'Upgrade failed');
          // 升级碰到的问题需要客户处理，抛 400 异常
          throw new BadRequestException(error.message);
        }
      } else {
        // 降级/周期变更：延期生效，不立即变更当前状态
        // subscription 的 cancel_at_period_end 和 schedule 是互斥的，要使用 schedule 需要先取消 cancel_at_period_end
        if (stripeSubscription.cancel_at_period_end) {
          await this.stripeService.doNotCancelAtPeriodEnd(stripeSubscriptionId);
        }
        // 用 schedule 实现延期变更
        await this.stripeService.scheduleUpdateSubscriptionAtPeriodEnd(
          stripeSubscriptionId,
          targetPrice.id,
        );
        const updatedStripeSubscription =
          await this.stripeService.getSubscription(stripeSubscriptionId);
        const result =
          await this.subscriptionService.syncFromStripeSubscription(updatedStripeSubscription);
        return {
          ...result,
          message: `Change to ${targetProductTier} ${targetBillingInterval} will take effect at the end of the current period.`,
        };
      }
    }
  }
}
