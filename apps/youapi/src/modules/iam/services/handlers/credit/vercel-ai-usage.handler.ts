import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { ClsService } from 'nestjs-cls';
import { Transactional } from '@/common/database/transaction.decorator';
import { VercelAiUsageEvent } from '../../../domain/credit/events/vercel-ai-usage.event';
import { CreditAccountRepository } from '../../../repositories/credit-account.repository';
import { CreditTransactionRepository } from '../../../repositories/credit-transaction.repository';
import { SpaceRepository } from '../../../repositories/space.repository';

@Injectable()
@EventsHandler(VercelAiUsageEvent)
export class VercelAiUsageHandler implements IEventHandler<VercelAiUsageEvent> {
  constructor(
    private readonly cls: ClsService,
    private readonly spaceRepository: SpaceRepository,
    private readonly creditAccountRepository: CreditAccountRepository,
    private readonly creditTransactionRepository: CreditTransactionRepository,
  ) {}

  @Transactional()
  async handle(event: VercelAiUsageEvent) {
    // 从 CLS 获取 spaceId，取不到再通过 userId 查找
    let spaceId = this.cls.get('spaceId');
    if (!spaceId) {
      const userId = this.cls.get('userId');
      if (!userId) {
        throw new Error('Neither spaceId nor userId found in CLS context');
      }

      const space = await this.spaceRepository.getByCreatorId(userId);
      spaceId = space.id;
    }

    // 获取并锁定积分账户
    const creditAccount = await this.creditAccountRepository.getBySpaceIdForUpdate(spaceId);

    // 使用 CreditAccount 的内置方法消耗积分
    const consumeTransaction = creditAccount.consumeByTokens(event);

    if (!consumeTransaction) {
      return;
    }

    // 保存更新
    await this.creditAccountRepository.save(creditAccount);
    await this.creditTransactionRepository.save(consumeTransaction);

    // 提交领域事件
    creditAccount.commit();
    consumeTransaction.commit();
  }
}
