import { Injectable, Logger } from '@nestjs/common';
import Stripe from 'stripe';
import { Transactional } from '@/common/database/transaction.decorator';
import { Subscription } from '../domain/subscription/models/subscription.entity';
import { CreditAccountDto } from '../dto/credit-account.dto';
import { CreditTransactionDto } from '../dto/credit-transaction.dto';
import { SubscriptionDto } from '../dto/subscription.dto';
import { CreditAccountRepository } from '../repositories/credit-account.repository';
import { CreditTransactionRepository } from '../repositories/credit-transaction.repository';
import { SubscriptionRepository } from '../repositories/subscription.repository';
import { UserDtoService } from './user-dto.service';

interface SyncFromStripeSubscriptionResult {
  subscription?: SubscriptionDto;
  creditAccount?: CreditAccountDto;
  creditTransactions?: CreditTransactionDto[];
  redirect?: string;
}

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly creditAccountRepository: CreditAccountRepository,
    private readonly creditTransactionRepository: CreditTransactionRepository,
    private readonly userDtoService: UserDtoService,
  ) {}

  /**
   * 从 Stripe 同步订阅信息（包含数据库操作和积分账户同步）
   */
  @Transactional()
  async syncFromStripeSubscription(
    stripeSubscription: Stripe.Subscription,
    syncToCreditAccount: boolean = true,
  ): Promise<SyncFromStripeSubscriptionResult> {
    const spaceId = stripeSubscription.metadata?.space_id;
    if (!spaceId) {
      throw new Error(`No space_id found in subscription metadata: ${stripeSubscription.id}`);
    }

    // 同步删除订阅
    if (
      stripeSubscription.status === 'canceled' ||
      stripeSubscription.status === 'incomplete_expired'
    ) {
      const subscription = await this.subscriptionRepository.findBySpaceId(spaceId);
      if (subscription) {
        await this.subscriptionRepository.deleteById(subscription.id);
        this.logger.log(`Delete subscription ${subscription.id} for space ${spaceId}.`);
        if (syncToCreditAccount) {
          const lockedAccount = await this.creditAccountRepository.getBySpaceIdForUpdate(spaceId);
          const creditTransactions = lockedAccount.resetToFree();
          if (creditTransactions.length > 0) {
            await this.creditTransactionRepository.createMany(creditTransactions);
            await this.creditAccountRepository.save(lockedAccount);
            lockedAccount.commit();
            this.logger.log(`Reset credit account to free tier for space ${spaceId}.`);
          }
          return {
            subscription: null,
            creditAccount: this.userDtoService.toCreditAccountDto(lockedAccount),
            creditTransactions: creditTransactions.map((transaction) =>
              this.userDtoService.toCreditTransactionDto(transaction),
            ),
          };
        }
      }
      return {
        subscription: null,
      };
    }

    // 同步更新订阅
    let lockedSubscription = await this.subscriptionRepository.findBySpaceIdForUpdate(spaceId);

    if (!lockedSubscription) {
      this.logger.log(`Creating new subscription for space: ${spaceId}`);
      lockedSubscription = Subscription.createFromStripeSubscription(spaceId, stripeSubscription);
    } else {
      this.logger.log(`Updating existing subscription for space: ${spaceId}`);
      lockedSubscription.syncFromStripeSubscription(stripeSubscription);
    }

    await this.subscriptionRepository.save(lockedSubscription);
    lockedSubscription.commit();
    this.logger.log(
      `Synced stripe subscription ${stripeSubscription.id} to subscription ${lockedSubscription.id} for space ${spaceId}.`,
    );

    if (syncToCreditAccount) {
      // 同步积分账户
      const lockedAccount = await this.creditAccountRepository.getBySpaceIdForUpdate(
        lockedSubscription.spaceId,
      );
      const creditTransactions = lockedAccount.syncFromSubscription(lockedSubscription);
      if (creditTransactions.length > 0) {
        await this.creditTransactionRepository.createMany(creditTransactions);
        await this.creditAccountRepository.save(lockedAccount);
        lockedAccount.commit();
        this.logger.log(
          `Credit account ${lockedAccount.id} reset or refreshed by subscription ${lockedSubscription.id} for space ${spaceId}.`,
        );
        this.logger.log(lockedAccount, 'Credit account');
        this.logger.log(creditTransactions, 'Credit transactions');
      }
      return {
        subscription: this.userDtoService.toSubscriptionDto(lockedSubscription),
        creditAccount: this.userDtoService.toCreditAccountDto(lockedAccount),
        creditTransactions: creditTransactions.map((transaction) =>
          this.userDtoService.toCreditTransactionDto(transaction),
        ),
      };
    }

    return {
      subscription: this.userDtoService.toSubscriptionDto(lockedSubscription),
    };
  }
}
