import { Injectable } from '@nestjs/common';
import { CreditAccount } from '../domain/credit/models/credit-account.entity';
import { CreditTransaction } from '../domain/credit/models/credit-transaction.entity';
import { Space, SpaceStatus } from '../domain/space/models/space.entity';
import { Subscription } from '../domain/subscription/models/subscription.entity';
import { User } from '../domain/user/models/user.entity';
import { UserPreference } from '../domain/user-preference/models/user-preference.aggregate';
import { CreditAccountDto } from '../dto/credit-account.dto';
import { CreditTransactionDto } from '../dto/credit-transaction.dto';
import { SpaceDto } from '../dto/space.dto';
import { SubscriptionDto } from '../dto/subscription.dto';
import { UserDto } from '../dto/user.dto';
import { UserPreferenceDto } from '../dto/user-preference.dto';
import { UserWithPreferenceSpaceDto } from '../dto/user-with-preference-space.dto';
import { CreditAccountRepository } from '../repositories/credit-account.repository';
import { SubscriptionRepository } from '../repositories/subscription.repository';
import { TestClockService } from './test-clock.service';

@Injectable()
export class UserDtoService {
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly creditAccountRepository: CreditAccountRepository,
    private readonly testClockService: TestClockService,
  ) {}
  async toUserWithPreferenceSpaceDto(
    user: User,
    preference: UserPreference,
    space: Space,
  ): Promise<UserWithPreferenceSpaceDto> {
    const dto = new UserWithPreferenceSpaceDto();

    // 用户基本信息
    dto.id = user.id;
    dto.createdAt = user.createdAt;
    dto.updatedAt = user.updatedAt;
    dto.email = user.email;
    dto.name = user.name;
    dto.picture = user.picture;
    dto.timeZone = user.timeZone;
    dto.confirmedAt = user.confirmedAt;
    dto.lastSignInAt = user.lastSignInAt;

    // 用户偏好设置
    dto.preference = this.toUserPreferenceDto(preference);

    // 用户空间信息
    dto.space = await this.toSpaceDto(space);

    return dto;
  }

  toUserDto(user: User): UserDto {
    const dto = new UserDto();

    // 用户基本信息
    dto.id = user.id;
    dto.createdAt = user.createdAt;
    dto.updatedAt = user.updatedAt;
    dto.email = user.email;
    dto.name = user.name;
    dto.picture = user.picture;
    dto.timeZone = user.timeZone;
    dto.confirmedAt = user.confirmedAt;
    dto.lastSignInAt = user.lastSignInAt;

    return dto;
  }

  toUserPreferenceDto(preference: UserPreference): UserPreferenceDto {
    const dto = new UserPreferenceDto();

    dto.id = preference.id;
    dto.displayLanguage = preference.displayLanguage;
    dto.aiResponseLanguage = preference.aiResponseLanguage;
    dto.ai2ndResponseLanguage = preference.ai2ndResponseLanguage;
    dto.enableBilingual = preference.enableBilingual;

    return dto;
  }

  async toSpaceDto(space: Space): Promise<SpaceDto> {
    const dto = new SpaceDto();

    dto.id = space.id;
    dto.createdAt = space.createdAt;
    dto.updatedAt = space.updatedAt;
    dto.creatorId = space.creatorId;

    const [subscription, creditAccount] = await Promise.all([
      this.subscriptionRepository.findBySpaceId(space.id),
      this.creditAccountRepository.findBySpaceId(space.id),
    ]);

    if (subscription) {
      dto.subscription = this.toSubscriptionDto(subscription);
    }

    if (creditAccount) {
      dto.creditAccount = this.toCreditAccountDto(creditAccount);
    }

    // 兼容 IOS 老逻辑
    dto.status = dto.subscription ? SpaceStatus.SUBSCRIBED : SpaceStatus.TRIALING;
    const trialExpiresAt = new Date();
    trialExpiresAt.setDate(trialExpiresAt.getDate() + 7);
    dto.trialExpiresAt = trialExpiresAt;

    return dto;
  }

  toSubscriptionDto(subscription: Subscription): SubscriptionDto {
    const dto = new SubscriptionDto();
    dto.id = subscription.id;
    dto.productTier = subscription.productTier;
    dto.billingInterval = subscription.billingInterval;
    dto.status = subscription.status;
    dto.renewCycleAnchor = subscription.renewCycleAnchor;
    dto.currentPeriodStart = subscription.currentPeriodStart;
    dto.currentPeriodEnd = subscription.currentPeriodEnd;
    dto.cancelAtPeriodEnd = subscription.cancelAtPeriodEnd;
    dto.provider = subscription.provider;
    dto.externalId = subscription.getExternalId();
    dto.unpaidInvoiceUrl = subscription.getUnpaidInvoiceUrl();
    dto.renewChange = subscription.renewChange ? subscription.renewChange : undefined;
    return dto;
  }

  toCreditAccountDto(creditAccount: CreditAccount): CreditAccountDto {
    const dto = new CreditAccountDto();
    dto.id = creditAccount.id;
    dto.spaceId = creditAccount.spaceId;
    dto.monthlyBalance = creditAccount.monthlyBalance;
    dto.createdAt = creditAccount.createdAt;
    dto.updatedAt = creditAccount.updatedAt;
    dto.productTier = creditAccount.productTier;
    dto.currentPeriodStart = creditAccount.currentPeriodStart;
    dto.currentPeriodEnd = creditAccount.currentPeriodEnd;
    dto.refreshCycleAnchor = creditAccount.refreshCycleAnchor;
    dto.monthlyQuota = creditAccount.getMonthlyQuota();
    dto.testClock = creditAccount.testClock;
    return dto;
  }

  toCreditTransactionDto(transaction: CreditTransaction): CreditTransactionDto {
    const dto = new CreditTransactionDto();
    dto.id = transaction.id;
    dto.accountId = transaction.creditAccountId;
    dto.spaceId = transaction.spaceId;
    // userId field removed from entity per design document
    dto.type = transaction.type;
    dto.amount = transaction.amount;
    dto.balanceBefore = transaction.balanceBefore;
    dto.balanceAfter = transaction.balanceAfter;
    dto.reason = transaction.reason;
    dto.metadata = transaction.metadata;
    dto.createdAt = transaction.createdAt;
    return dto;
  }
}
