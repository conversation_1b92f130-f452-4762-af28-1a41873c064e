import { Environment } from '@apple/app-store-server-library';
import type { RawBodyRequest } from '@nestjs/common';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Headers,
  HttpCode,
  Post,
  Query,
  Redirect,
  Req,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CommandBus, EventBus, QueryBus } from '@nestjs/cqrs';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import type { Request } from 'express';
import { PublicRoute } from '@/common/decorators';
import { getBaseUrlFromRequest } from '@/common/utils';
import { BaseController } from '@/shared/base.controller';
import { AppleEvent } from '../domain/space/events/apple.event';
import { StripeEvent } from '../domain/space/events/stripe.event';
import { CheckoutSuccessQueryDto } from '../dto/checkout-success-query.dto';
import { CreateSubscriptionDto } from '../dto/create-subscription.dto';
import { CreateSubscriptionResponseDto } from '../dto/create-subscription-response.dto';
import { RedirectResponseDto } from '../dto/sign-in-redirect-response.dto';
import { SubscriptionDto } from '../dto/subscription.dto';
import { UpdateSubscriptionDto } from '../dto/update-subscription.dto';
import { UpdateSubscriptionResponseDto } from '../dto/update-subscription-response.dto';
import { UserWithPreferenceSpaceDto } from '../dto/user-with-preference-space.dto';
import { VerifyTransactionDto } from '../dto/verify-transaction.dto';
import { CancelSubscriptionCommand } from '../services/commands/cancel-subscription.command';
import { CreateBillingPortalSessionCommand } from '../services/commands/subscription/create-billing-portal-session.command';
import { CreateSubscriptionCommand } from '../services/commands/subscription/create-subscription.command';
import { deleteSubscriptionForTestCommand } from '../services/commands/subscription/delete-subscription-for-test.command';
import { HandleCheckoutSuccessCommand } from '../services/commands/subscription/handle-check-out-success.command';
import { VerifyTransactionCommand } from '../services/commands/subscription/verify-transaction.command';
import { UpdateSubscriptionCommand } from '../services/commands/update-subscription.command';
import { FindSubscriptionQuery } from '../services/queries/find-subscription.query';
import { StripeHolder } from '../services/stripe.holder';

@ApiTags('Subscription')
@Controller()
export class SubscriptionController extends BaseController {
  constructor(
    protected readonly commandBus: CommandBus,
    protected readonly queryBus: QueryBus,
    private readonly stripeHolder: StripeHolder,
    private readonly configService: ConfigService,
    private readonly eventBus: EventBus,
  ) {
    super();
  }

  @Post('api/v1/subscription/findSubscription')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Find current user subscription',
    description: 'Query and return current user subscription information',
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription found or undefined if not exists',
    type: SubscriptionDto,
    // OpenAPI 对 response body optional 的支持还未实现
    // https://github.com/OAI/OpenAPI-Specification/issues/2236
  })
  async findSubscription(): Promise<SubscriptionDto | undefined> {
    const spaceId = await this.getSpaceId();
    const query = new FindSubscriptionQuery(spaceId);
    return this.queryBus.execute(query);
  }

  @Post('api/v1/subscription/createSubscription')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建付费订阅',
    description: '免费用户创建付费订阅，包括 Stripe Customer 创建、订阅创建和积分重置',
  })
  @ApiResponse({
    status: 200,
    description: '订阅创建成功或返回支付链接',
    type: CreateSubscriptionResponseDto,
  })
  async createSubscription(
    @Req() request: Request,
    @Body() dto: CreateSubscriptionDto,
  ): Promise<CreateSubscriptionResponseDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();
    const baseUrl = getBaseUrlFromRequest(request);

    const command = new CreateSubscriptionCommand(
      spaceId,
      userId,
      dto.productTier,
      dto.billingInterval,
      baseUrl,
    );

    return this.commandBus.execute(command);
  }

  @Post('api/v1/subscription/createBillingPortalSession')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建 Stripe Billing Portal 会话',
    description:
      '为当前用户创建 Stripe Billing Portal 会话，用户可以在 Billing Portal 中管理订阅、更新支付方式、查看账单历史等',
  })
  @ApiResponse({
    status: 200,
    description: 'Billing Portal 会话创建成功',
    type: RedirectResponseDto,
  })
  async createBillingPortalSession(): Promise<RedirectResponseDto> {
    const userId = this.getUserId();

    const command = new CreateBillingPortalSessionCommand(userId);

    return this.commandBus.execute(command);
  }

  /**
   * 处理 Stripe 支付成功后的回调页面
   */
  @Get('checkout-success')
  @Redirect()
  async checkoutSuccess(@Query() query: CheckoutSuccessQueryDto) {
    const command = new HandleCheckoutSuccessCommand(query.sessionId);

    const { redirect } = await this.commandBus.execute(command);
    return { url: redirect };
  }

  @PublicRoute()
  @Post('webhook/v1/stripe')
  @HttpCode(200)
  @ApiOperation({
    summary: '处理 Stripe Webhook',
    description: '接收并处理 Stripe 的 webhook 事件',
  })
  async handleStripeWebhook(
    @Req() req: RawBodyRequest<Request>,
    @Headers('stripe-signature') signature: string,
  ) {
    // 获取原始请求体文本
    const rawBody = req.rawBody;

    // 获取 webhook secret
    const webhookSecret = this.configService.getOrThrow<string>('STRIPE_WEBHOOK_SECRET');

    try {
      // 使用 Stripe SDK 构造并验证事件
      const rawEvent = this.stripeHolder.hongkong.webhooks.constructEvent(
        rawBody.toString('utf8'),
        signature,
        webhookSecret,
      );
      // 发布到 EventBus 异步处理
      this.eventBus.publish(new StripeEvent(rawEvent));
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      throw new BadRequestException('Webhook signature verification failed');
    }
  }

  @Post('api/v1/subscription/verifyTransaction')
  @HttpCode(200)
  @ApiOperation({
    summary: '验证 Apple 内购交易',
    description: '验证 Apple 内购交易的有效性，确认交易属于当前用户，并更新用户的订阅状态',
  })
  @ApiResponse({
    status: 200,
    description: '交易验证成功',
    type: UserWithPreferenceSpaceDto,
  })
  async verifyTransaction(@Body() body: VerifyTransactionDto): Promise<UserWithPreferenceSpaceDto> {
    const userId = this.getUserId();

    const command = new VerifyTransactionCommand(userId, body.transactionId, body.environment);

    return this.commandBus.execute(command);
  }

  @PublicRoute()
  @Post('webhook/v1/apple')
  @HttpCode(200)
  @ApiOperation({
    summary: '处理 Apple App Store Server Notifications',
    description: '处理来自 Apple App Store 的服务器通知事件（生产环境）',
  })
  async handleAppleNotification(@Body() body: { signedPayload: string }) {
    // 发布到 EventBus 异步处理
    // TODO 改回生产环境
    this.eventBus.publish(new AppleEvent(body.signedPayload, Environment.SANDBOX));
  }

  @PublicRoute()
  @Post('webhook/v1/apple-sandbox')
  @HttpCode(200)
  @ApiOperation({
    summary: '处理 Apple App Store Server Notifications (Sandbox)',
    description: '处理来自 Apple App Store 的服务器通知事件（沙盒环境）',
  })
  async handleAppleSandboxNotification(@Body() body: { signedPayload: string }) {
    // 发布到 EventBus 异步处理
    this.eventBus.publish(new AppleEvent(body.signedPayload, Environment.SANDBOX));
  }

  /**
   * 变更订阅配置
   */
  @Post('api/v1/subscription/updateSubscription')
  @HttpCode(200)
  @ApiOperation({
    summary: '变更订阅配置',
    description: '用户主动变更订阅的产品等级和计费周期，支持升级、降级和周期变更',
  })
  @ApiResponse({
    status: 200,
    description: '订阅变更成功',
    type: UpdateSubscriptionResponseDto,
  })
  async updateSubscription(
    @Req() request: Request,
    @Body() dto: UpdateSubscriptionDto,
  ): Promise<UpdateSubscriptionResponseDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const baseUrl = getBaseUrlFromRequest(request);
    const command = new UpdateSubscriptionCommand(
      spaceId,
      userId,
      dto.productTier,
      dto.billingInterval,
      baseUrl,
    );

    return this.commandBus.execute(command);
  }

  /**
   * 取消订阅
   */
  @Post('api/v1/subscription/cancelSubscription')
  @HttpCode(200)
  @ApiOperation({
    summary: '取消订阅',
    description: '用户主动取消付费订阅，设置为当前周期结束时生效',
  })
  @ApiResponse({
    status: 200,
    description: '订阅取消成功',
    type: SubscriptionDto,
  })
  async cancelSubscription(): Promise<SubscriptionDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new CancelSubscriptionCommand(spaceId, userId);
    return this.commandBus.execute(command);
  }

  /**
   * TODO 加 admin 权限
   * 清空订阅，用于测试
   */
  @Post('api/v1/subscription/deleteSubscriptionForTest')
  @HttpCode(200)
  @ApiOperation({
    summary: '删除订阅，用于测试',
    description: '删除订阅，用于测试',
  })
  async deleteSubscriptionForTest() {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new deleteSubscriptionForTestCommand(userId, spaceId);
    return this.commandBus.execute(command);
  }
}
