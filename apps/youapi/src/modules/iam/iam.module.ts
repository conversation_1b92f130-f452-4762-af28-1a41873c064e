/**
 * IAM Module - Identity & Access Management
 * 身份与访问管理模块
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/auth/*
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/user/*
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/space/*
 */

import { Module } from '@nestjs/common';
import { AuthController } from './controllers/auth.controller';
import { CreditController } from './controllers/credit.controller';
import { SubscriptionController } from './controllers/subscription.controller';
import { UserController } from './controllers/user.controller';
import { UserPreferenceController } from './controllers/user-preference.controller';
import { CreditAccountRepository } from './repositories/credit-account.repository';
import { CreditTransactionRepository } from './repositories/credit-transaction.repository';
import { CustomerRepository } from './repositories/customer.repository';
import { SpaceRepository } from './repositories/space.repository';
import { SubscriptionRepository } from './repositories/subscription.repository';
import { UserRepository } from './repositories/user.repository';
import { UserPreferenceRepository } from './repositories/user-preference.repository';
import { AppleHolder } from './services/apple.holder';
import { CreditService } from './services/credit.service';
// Auth CQRS Handlers
import { HandleAuthCallbackHandler } from './services/handlers/auth/handle-auth-callback.handler';
import { LogOutHandler } from './services/handlers/auth/log-out.handler';
import { SignInWithOAuthHandler } from './services/handlers/auth/sign-in-with-oauth.handler';
import { SignInWithOTPHandler } from './services/handlers/auth/sign-in-with-otp.handler';
import { ValidateOTPTokenHandler } from './services/handlers/auth/validate-otp-token.handler';
// Credit CQRS Handlers
import { ConsumeCreditsHandler } from './services/handlers/credit/consume-credits.handler';
import { GetCreditAccountHandler } from './services/handlers/credit/get-credit-account.handler';
import { InitUserCreditHandler } from './services/handlers/credit/init-user-credit.handler';
import { ListCreditTransactionsHandler } from './services/handlers/credit/list-credit-transactions.handler';
import { VercelAiUsageHandler } from './services/handlers/credit/vercel-ai-usage.handler';
// Space CQRS Handlers
import { AppleEventHandler } from './services/handlers/space/apple-event.handler';
import { GetSpaceByUserIdHandler } from './services/handlers/space/get-space-by-user-id.handler';
// Subscription CQRS Handlers
import { CancelSubscriptionHandler } from './services/handlers/subscription/cancel-subscription.handler';
import { CreateBillingPortalSessionHandler } from './services/handlers/subscription/create-billing-portal-session.handler';
import { CreateCheckoutSessionHandler } from './services/handlers/subscription/create-checkout-session.handler';
import { CreateSubscriptionHandler } from './services/handlers/subscription/create-subscription.handler';
import { DeleteSubscriptionForTestHandler } from './services/handlers/subscription/delete-subscription-for-test.handler';
import { FindSubscriptionHandler } from './services/handlers/subscription/find-subscription.handler';
import { HandleCheckoutSuccessHandler } from './services/handlers/subscription/handle-checkout-success.handler';
import { StripeEventHandler } from './services/handlers/subscription/stripe-event.handler';
import { UpdateSubscriptionHandler } from './services/handlers/subscription/update-subscription.handler';
import { VerifyTransactionHandler } from './services/handlers/subscription/verify-transaction.handler';
import { AdvanceTestClockHandler } from './services/handlers/test/advance-test-clock.handler';
import { ResetCreditsForTestHandler } from './services/handlers/test/reset-credits-for-test.handler';
// User CQRS Handlers
import { DeleteUserHandler } from './services/handlers/user/delete-user.handler';
import { GetUserHandler } from './services/handlers/user/get-user.handler';
import { GetUserPreferenceHandler as GetUserPreferenceHandlerUser } from './services/handlers/user/get-user-preference.handler';
import { InitUserHandler } from './services/handlers/user/init-user.handler';
import { InitUsersHandler } from './services/handlers/user/init-users.handler';
import { UpdateUserAvatarHandler } from './services/handlers/user/update-user-avatar.handler';
import { UpdateUserNameHandler } from './services/handlers/user/update-user-name.handler';
import { UpdateUserPreferenceHandler as UpdateUserPreferenceHandlerUser } from './services/handlers/user/update-user-preference.handler';
import { UpdateUserTimeZoneHandler } from './services/handlers/user/update-user-time-zone.handler';
import { UpdateUserTimeZoneIfNotSetHandler } from './services/handlers/user/update-user-time-zone-if-not-set.handler';
// User Preference CQRS Handlers
import { GetUserPreferenceHandler } from './services/handlers/user-preference/get-user-preference.handler';
import { UpdateUserPreferenceHandler } from './services/handlers/user-preference/update-user-preference.handler';
// Subscription Services
import { StripeHolder } from './services/stripe.holder';
import { StripeService } from './services/stripe.service';
import { SubscriptionService } from './services/subscription.service';
import { TestClockService } from './services/test-clock.service';
// DTO Services
import { UserDtoService } from './services/user-dto.service';

@Module({
  imports: [
    // FIXME 暂不依赖 MaterialMngModule，免得又间接引入一堆老代码的依赖
    // forwardRef(() => MaterialMngModule),
  ],
  controllers: [
    AuthController,
    UserController,
    UserPreferenceController,
    SubscriptionController,
    CreditController,
  ],
  providers: [
    // Repositories
    SpaceRepository,
    SubscriptionRepository,
    UserRepository,
    CustomerRepository,
    CreditAccountRepository,
    CreditTransactionRepository,

    // DTO Services
    UserDtoService,

    // Credit CQRS Components
    ConsumeCreditsHandler,
    GetCreditAccountHandler,
    InitUserCreditHandler,
    ListCreditTransactionsHandler,

    // Credit Services
    CreditService,

    // Credit CQRS Components
    VercelAiUsageHandler,

    // User CQRS Components
    DeleteUserHandler,
    GetUserHandler,
    GetUserPreferenceHandlerUser,
    InitUserHandler,
    UpdateUserAvatarHandler,
    UpdateUserNameHandler,
    UpdateUserPreferenceHandlerUser,
    UpdateUserTimeZoneHandler,
    UpdateUserTimeZoneIfNotSetHandler,
    InitUsersHandler,

    // User Preference CQRS Components
    UserPreferenceRepository,
    UpdateUserPreferenceHandler,
    GetUserPreferenceHandler,

    // Space CQRS Components
    AppleEventHandler,
    GetSpaceByUserIdHandler,

    // Subscription Services and CQRS Components
    StripeHolder,
    StripeService,
    SubscriptionService,
    CancelSubscriptionHandler,
    DeleteSubscriptionForTestHandler,
    FindSubscriptionHandler,
    ResetCreditsForTestHandler,
    CreateBillingPortalSessionHandler,
    CreateCheckoutSessionHandler,
    CreateSubscriptionHandler,
    HandleCheckoutSuccessHandler,
    StripeEventHandler,
    UpdateSubscriptionHandler,
    AppleHolder,
    VerifyTransactionHandler,

    // Auth CQRS Components
    HandleAuthCallbackHandler,
    LogOutHandler,
    SignInWithOAuthHandler,
    SignInWithOTPHandler,
    ValidateOTPTokenHandler,

    // Test Clock CQRS Components
    AdvanceTestClockHandler,
    TestClockService,
  ],
  exports: [UserPreferenceRepository, SpaceRepository],
})
export class IamModule {}
