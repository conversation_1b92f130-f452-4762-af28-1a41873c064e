import { AggregateRoot } from '@nestjs/cqrs';
import { UserDeletedEvent } from '../events/user-deleted.event';
import { UserInitializedEvent } from '../events/user-initialized.event';
import { UserUpdatedEvent } from '../events/user-updated.event';

type RawUserMetaData = {
  name?: string;
  picture?: string;
};

const OnboardStatus = {
  WAITING: 'waiting',
  ONBOARDED: 'onboarded',
  HOLD: 'hold',
  UNKNOWN: 'unknown',
} as const;

type OnboardStatus = (typeof OnboardStatus)[keyof typeof OnboardStatus];

type RawAppMetaData = {
  onboard_status?: OnboardStatus;
  name?: string;
  avatar_url?: string;
  time_zone?: string;
};

export interface UserConstructorParams {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  email?: string;
  confirmedAt?: Date;
  lastSignInAt: Date;
  rawUserMetaData: RawUserMetaData;
  rawAppMetaData: RawAppMetaData;
}
export class User extends AggregateRoot {
  // 1. 公开只读的不可变属性
  public readonly id: string;
  public readonly createdAt: Date;
  public readonly email?: string;
  public readonly updatedAt: Date;
  public readonly lastSignInAt: Date;
  public readonly confirmedAt?: Date;

  private _rawUserMetaData: RawUserMetaData;
  private _rawAppMetaData: RawAppMetaData;

  constructor(params: UserConstructorParams) {
    super();
    // 只读属性直接赋值
    this.id = params.id;
    this.createdAt = params.createdAt;
    this.email = params.email;
    this.updatedAt = params.updatedAt;
    this.confirmedAt = params.confirmedAt;
    this.lastSignInAt = params.lastSignInAt;

    // 可变属性
    this._rawUserMetaData = params.rawUserMetaData;
    this._rawAppMetaData = params.rawAppMetaData;
  }

  get name(): string | undefined {
    return this._rawAppMetaData.name || this._rawUserMetaData.name;
  }
  get picture(): string | undefined {
    return this._rawAppMetaData.avatar_url || this._rawUserMetaData.picture;
  }
  get timeZone(): string | undefined {
    return this._rawAppMetaData.time_zone;
  }
  get rawUserMetaData(): RawUserMetaData {
    return this._rawUserMetaData;
  }
  get rawAppMetaData(): RawAppMetaData {
    return this._rawAppMetaData;
  }

  updateName(name: string) {
    this._rawAppMetaData.name = name;
    this.apply(new UserUpdatedEvent(this.id, 'name', { name }));
  }

  updateAvatar(avatarUrl: string) {
    this._rawAppMetaData.avatar_url = avatarUrl;
    this.apply(new UserUpdatedEvent(this.id, 'avatar', { picture: avatarUrl }));
  }

  updateTimeZone(timeZone: string) {
    this._rawAppMetaData.time_zone = timeZone;
    this.apply(new UserUpdatedEvent(this.id, 'timeZone', { timeZone }));
  }

  delete() {
    this.apply(new UserDeletedEvent(this.id));
  }

  isInitialized(): boolean {
    return !!this._rawAppMetaData.onboard_status;
  }

  initialize(timeZone: string): boolean {
    if (this.isInitialized()) {
      return false;
    }

    this._rawAppMetaData.onboard_status = OnboardStatus.ONBOARDED;
    this._rawAppMetaData.time_zone = timeZone;
    this.apply(new UserInitializedEvent(this.id));
  }
}
