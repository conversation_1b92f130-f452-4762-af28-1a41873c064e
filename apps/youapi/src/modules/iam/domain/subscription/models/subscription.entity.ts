import {
  JWSRenewalInfoDecodedPayload,
  JWSTransactionDecodedPayload,
} from '@apple/app-store-server-library';
import { AggregateRoot } from '@nestjs/cqrs';
import Stripe from 'stripe';
import { uuidv7 } from 'uuidv7';
import { SubscriptionCreatedEvent } from '../events/subscription-created.event';
import { SubscriptionSyncedFromAppleEvent } from '../events/subscription-synced-from-apple.event';
import { SubscriptionSyncedFromStripeEvent } from '../events/subscription-synced-from-stripe.event';
import { SubscriptionUpdatedEvent } from '../events/subscription-updated.event';
import {
  AppleProductId,
  BillingInterval,
  PriceKey,
  ProductTier,
  RenewChange,
  StripePriceLookupKey,
  SubscriptionConstructorParams,
  SubscriptionMetadata,
  SubscriptionProductTier,
  SubscriptionProvider,
  SubscriptionStatus,
  TestAppleProductId,
} from './subscription.types';

export class Subscription extends AggregateRoot {
  public readonly id: string;
  public readonly spaceId: string;
  public readonly createdAt: Date;

  // 内部产品定义
  private _productTier: SubscriptionProductTier;
  private _billingInterval: BillingInterval;
  private _status: SubscriptionStatus;

  // 基于 Stripe 计费周期模型的字段（适用于所有订阅类型）
  private _renewCycleAnchor: Date; // 续期周期锚点
  private _currentPeriodStart: Date; // 当前周期开始时间
  private _currentPeriodEnd: Date; // 当前周期结束时间
  private _cancelAtPeriodEnd: boolean; // 是否在周期结束时取消
  private _renewChange?: RenewChange | null; // 续期时的变更
  private _updatedAt: Date;

  // 外部系统集成
  private _provider: SubscriptionProvider;
  private _metadata: SubscriptionMetadata;

  // 实体生命周期
  private _isNew: boolean = false;

  constructor(params: SubscriptionConstructorParams) {
    super();
    this.id = params.id;
    this.spaceId = params.spaceId;
    this.createdAt = params.createdAt;
    this._updatedAt = params.updatedAt;
    this._productTier = params.productTier;
    this._billingInterval = params.billingInterval;
    this._status = params.status;
    this._renewCycleAnchor = params.renewCycleAnchor || null;
    this._currentPeriodStart = params.currentPeriodStart || null;
    this._currentPeriodEnd = params.currentPeriodEnd || null;
    this._cancelAtPeriodEnd = params.cancelAtPeriodEnd;
    this._renewChange = params.renewChange;
    this._provider = params.provider;
    this._metadata = params.metadata;
  }

  // Getters
  get productTier(): SubscriptionProductTier {
    return this._productTier;
  }

  get billingInterval(): BillingInterval {
    return this._billingInterval;
  }

  get status(): SubscriptionStatus {
    return this._status;
  }

  get renewCycleAnchor(): Date {
    return this._renewCycleAnchor;
  }

  get currentPeriodStart(): Date {
    return this._currentPeriodStart;
  }

  get currentPeriodEnd(): Date {
    return this._currentPeriodEnd;
  }

  get cancelAtPeriodEnd(): boolean {
    return this._cancelAtPeriodEnd;
  }

  set cancelAtPeriodEnd(value: boolean) {
    this._cancelAtPeriodEnd = value;
    this._updatedAt = new Date();
  }

  get renewChange(): RenewChange {
    return this._renewChange;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  get provider(): SubscriptionProvider {
    return this._provider;
  }

  get metadata(): SubscriptionMetadata {
    return this._metadata;
  }

  get isNew(): boolean {
    return this._isNew;
  }

  getExternalId(): string {
    if (this._provider === SubscriptionProvider.STRIPE && 'subscription' in this._metadata) {
      return this._metadata.subscription.id;
    }
    if (this._provider === SubscriptionProvider.APPLE && 'transactionInfo' in this._metadata) {
      return this._metadata.transactionInfo.originalTransactionId;
    }
    throw new Error(`No external ID found for subscription ${this.id}`);
  }

  getUnpaidInvoiceUrl(): string | undefined {
    if (
      this.status === SubscriptionStatus.PAST_DUE &&
      this._provider === SubscriptionProvider.STRIPE &&
      'subscription' in this._metadata
    ) {
      const subscription = this._metadata.subscription as Stripe.Subscription;
      if (subscription.latest_invoice && typeof subscription.latest_invoice !== 'string') {
        return subscription.latest_invoice.hosted_invoice_url;
      }
    }
    return undefined;
  }

  static parseFromPriceKey(priceKey: PriceKey): {
    productTier: SubscriptionProductTier;
    billingInterval: BillingInterval;
  } {
    switch (priceKey) {
      case StripePriceLookupKey.YOUMIND_PRO_MONTHLY:
      case AppleProductId.YOUMIND_PRO_MONTHLY:
      case TestAppleProductId.YOUMIND_PRO_MONTHLY:
        return { productTier: ProductTier.PRO, billingInterval: BillingInterval.MONTHLY };
      case StripePriceLookupKey.YOUMIND_PRO_YEARLY:
      case StripePriceLookupKey.YOUMIND_PRO_YEARLY_CNY:
      case AppleProductId.YOUMIND_PRO_YEARLY:
      case TestAppleProductId.YOUMIND_PRO_YEARLY:
        return { productTier: ProductTier.PRO, billingInterval: BillingInterval.YEARLY };
      case StripePriceLookupKey.YOUMIND_MAX_MONTHLY:
      case AppleProductId.YOUMIND_MAX_MONTHLY:
      case TestAppleProductId.YOUMIND_MAX_MONTHLY:
        return { productTier: ProductTier.MAX, billingInterval: BillingInterval.MONTHLY };
      case StripePriceLookupKey.YOUMIND_MAX_YEARLY:
      case StripePriceLookupKey.YOUMIND_MAX_YEARLY_CNY:
      case AppleProductId.YOUMIND_MAX_YEARLY:
      case TestAppleProductId.YOUMIND_MAX_YEARLY:
        return { productTier: ProductTier.MAX, billingInterval: BillingInterval.YEARLY };
      default:
        throw new Error(`Invalid price key: ${priceKey}`);
    }
  }

  /**
   * 从 Stripe 订阅计划中解析续期变更
   *
   * 返回 null 才能保证更新为 null
   */
  static parseRenewChangeFromSchedule(schedule: Stripe.SubscriptionSchedule): RenewChange | null {
    if (schedule.status !== 'active' || schedule.phases.length < 2 || !schedule.current_phase) {
      return null;
    }

    // 使用 Stripe 提供的 current_phase 信息找到对应的阶段索引
    const currentPhaseStartDate = schedule.current_phase.start_date;
    let currentPhaseIndex = -1;

    // 通过 start_date 匹配找到当前阶段在 phases 数组中的位置
    for (let i = 0; i < schedule.phases.length; i++) {
      if (schedule.phases[i].start_date === currentPhaseStartDate) {
        currentPhaseIndex = i;
        break;
      }
    }

    // 如果找不到当前阶段或没有下一阶段，返回空
    if (currentPhaseIndex === -1 || currentPhaseIndex >= schedule.phases.length - 1) {
      return null;
    }

    const currentPhase = schedule.phases[currentPhaseIndex];
    const nextPhase = schedule.phases[currentPhaseIndex + 1];

    // 确保两个阶段都有价格信息
    if (!currentPhase.items?.[0]?.price || !nextPhase.items?.[0]?.price) {
      return null;
    }

    const currentPrice = currentPhase.items[0].price as Stripe.Price;
    const nextPrice = nextPhase.items[0].price as Stripe.Price;

    // 解析当前和目标产品信息
    const currentPriceKey = currentPrice.lookup_key as StripePriceLookupKey;
    const nextPriceKey = nextPrice.lookup_key as StripePriceLookupKey;

    const { productTier: currentProductTier, billingInterval: currentBillingInterval } =
      Subscription.parseFromPriceKey(currentPriceKey);
    const { productTier: nextProductTier, billingInterval: nextBillingInterval } =
      Subscription.parseFromPriceKey(nextPriceKey);

    // 检查是否有实际变更
    if (currentProductTier === nextProductTier && currentBillingInterval === nextBillingInterval) {
      return null;
    }

    return {
      productTier: nextProductTier,
      billingInterval: nextBillingInterval,
    };
  }

  static createFromStripeSubscription(
    spaceId: string,
    stripeSubscription: Stripe.Subscription,
  ): Subscription {
    const now = new Date();
    // 从 Stripe 订阅中提取价格信息
    const priceLookupKey = stripeSubscription.items.data[0].price
      .lookup_key as StripePriceLookupKey;

    const { productTier, billingInterval } = Subscription.parseFromPriceKey(priceLookupKey);

    const schedule = stripeSubscription.schedule as Stripe.SubscriptionSchedule | undefined;
    // 创建实际的订阅实例
    const subscription = new Subscription({
      id: uuidv7(),
      spaceId,
      createdAt: new Date(stripeSubscription.created * 1000), // Stripe 时间戳转换为 Date
      updatedAt: now,
      productTier,
      billingInterval,
      status: stripeSubscription.status as SubscriptionStatus,
      renewCycleAnchor:
        stripeSubscription.billing_cycle_anchor &&
        new Date(stripeSubscription.billing_cycle_anchor * 1000),
      currentPeriodStart:
        stripeSubscription.items.data[0].current_period_start &&
        new Date(stripeSubscription.items.data[0].current_period_start * 1000),
      currentPeriodEnd:
        stripeSubscription.items.data[0].current_period_end &&
        new Date(stripeSubscription.items.data[0].current_period_end * 1000),
      cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end ?? false,
      renewChange: schedule ? Subscription.parseRenewChangeFromSchedule(schedule) : null,
      provider: SubscriptionProvider.STRIPE,
      metadata: {
        subscription: stripeSubscription,
      },
    });

    // 设置实体生命周期标记
    subscription._isNew = true;

    // 触发领域事件
    subscription.apply(
      new SubscriptionCreatedEvent(
        subscription.id,
        spaceId,
        subscription.productTier,
        subscription.createdAt,
      ),
    );

    return subscription;
  }

  static createFromAppleTransaction(
    spaceId: string,
    transactionInfo: JWSTransactionDecodedPayload,
  ): Subscription {
    const now = new Date();
    const { productTier, billingInterval } = Subscription.parseFromPriceKey(
      transactionInfo.productId as AppleProductId | TestAppleProductId,
    );
    const subscription = new Subscription({
      id: uuidv7(),
      spaceId,
      createdAt: new Date(transactionInfo.originalPurchaseDate),
      updatedAt: now,
      productTier,
      billingInterval,
      status: SubscriptionStatus.ACTIVE,
      renewCycleAnchor: new Date(transactionInfo.originalPurchaseDate),
      currentPeriodStart: new Date(transactionInfo.purchaseDate),
      currentPeriodEnd: new Date(transactionInfo.expiresDate),
      cancelAtPeriodEnd: false,
      provider: SubscriptionProvider.APPLE,
      metadata: {
        transactionInfo,
      },
    });
    return subscription;
  }

  // 业务方法：从 Stripe 同步订阅信息
  syncFromStripeSubscription(stripeSubscriptionWithSchedule: Stripe.Subscription) {
    const priceLookupKey = stripeSubscriptionWithSchedule.items.data[0].price
      .lookup_key as StripePriceLookupKey;

    // 更新核心字段
    this._provider = SubscriptionProvider.STRIPE;
    this._status = stripeSubscriptionWithSchedule.status as SubscriptionStatus;

    // 更新计费周期字段（基于 Stripe 模型）
    this._renewCycleAnchor =
      stripeSubscriptionWithSchedule.billing_cycle_anchor &&
      new Date(stripeSubscriptionWithSchedule.billing_cycle_anchor * 1000);
    this._currentPeriodStart =
      stripeSubscriptionWithSchedule.items.data[0].current_period_start &&
      new Date(stripeSubscriptionWithSchedule.items.data[0].current_period_start * 1000);
    this._currentPeriodEnd =
      stripeSubscriptionWithSchedule.items.data[0].current_period_end &&
      new Date(stripeSubscriptionWithSchedule.items.data[0].current_period_end * 1000);

    const { productTier, billingInterval } = Subscription.parseFromPriceKey(priceLookupKey);
    this._productTier = productTier;
    this._billingInterval = billingInterval;

    this._cancelAtPeriodEnd = stripeSubscriptionWithSchedule.cancel_at_period_end ?? false;

    const schedule = stripeSubscriptionWithSchedule.schedule as
      | Stripe.SubscriptionSchedule
      | undefined;
    // IMPORTANT: 没有 schedule 时，renewChange 为 null 而不是 undefined，这样才能保证更新为 null
    this._renewChange = schedule ? Subscription.parseRenewChangeFromSchedule(schedule) : null;

    this._updatedAt = new Date();

    // 更新元数据
    this._metadata = {
      ...this._metadata,
      subscription: stripeSubscriptionWithSchedule,
    };

    // 触发领域事件
    this.apply(
      new SubscriptionSyncedFromStripeEvent(
        this.id,
        this.spaceId,
        stripeSubscriptionWithSchedule.id,
        this._updatedAt,
      ),
    );
  }

  // 业务方法：处理 Apple 交易
  syncFromAppleTransaction(
    transactionInfo: JWSTransactionDecodedPayload,
    renewalInfo: JWSRenewalInfoDecodedPayload,
  ): void {
    const productId = transactionInfo.productId as AppleProductId | TestAppleProductId;

    this._provider = SubscriptionProvider.APPLE;
    this._status = SubscriptionStatus.ACTIVE;

    const { productTier, billingInterval } = Subscription.parseFromPriceKey(productId);
    this._productTier = productTier;
    this._billingInterval = billingInterval;

    // Apple 订阅周期设置（基于交易信息）
    this._renewCycleAnchor = new Date(transactionInfo.originalPurchaseDate);
    this._currentPeriodStart = new Date(); // Apple 当前激活时间
    this._currentPeriodEnd = transactionInfo.expiresDate && new Date(transactionInfo.expiresDate);
    this._cancelAtPeriodEnd = renewalInfo.autoRenewStatus === 0;

    if (renewalInfo.autoRenewStatus === 1 && renewalInfo.autoRenewProductId) {
      const autoRenewProductId = renewalInfo.autoRenewProductId as
        | AppleProductId
        | TestAppleProductId;
      const { productTier, billingInterval } = Subscription.parseFromPriceKey(autoRenewProductId);
      this._renewChange = {
        productTier,
        billingInterval,
      };
    }

    this._updatedAt = new Date();

    // 更新元数据
    this._metadata = {
      transactionInfo,
      renewalInfo,
    };

    this.apply(new SubscriptionSyncedFromAppleEvent(this.id, this.spaceId, this._updatedAt));
  }

  // 业务方法：获取外部订阅 ID（从 metadata 中提取）
  getStripeSubscriptionId(): string {
    if (
      this._provider === SubscriptionProvider.STRIPE &&
      this._metadata &&
      'subscription' in this._metadata
    ) {
      return this._metadata.subscription.id;
    }
    throw new Error(`No external subscription ID found for subscription ${this.id}`);
  }

  // 查询方法
  isActive(): boolean {
    return this._status === SubscriptionStatus.ACTIVE;
  }

  // 业务方法：计划取消订阅（在周期结束时生效）
  scheduleCancel(): void {
    // 设置为在周期结束时取消
    this._cancelAtPeriodEnd = true;
    this._updatedAt = new Date();

    // 发布订阅取消计划事件
    this.apply(new SubscriptionUpdatedEvent(this.id, this.spaceId, this._updatedAt));
  }

  // 实体生命周期
  markAsExisting(): void {
    this._isNew = false;
  }
}
