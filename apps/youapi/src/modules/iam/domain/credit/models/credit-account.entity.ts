import type { Usage } from '@anthropic-ai/sdk/resources/messages';
import { Logger } from '@nestjs/common';
import { AggregateRoot } from '@nestjs/cqrs';
import { LanguageModelUsage } from 'ai';
import Stripe from 'stripe';
import { uuidv7 } from 'uuidv7';
import { LLMs } from '@/common/types';
import { Subscription } from '../../subscription/models/subscription.entity';
import {
  BillingInterval,
  ProductTier,
  SubscriptionStatus,
} from '../../subscription/models/subscription.types';
import { CreditAccountCreatedEvent } from '../events/credit-account-created.event';
import { CreditsConsumedEvent } from '../events/credits-consumed.event';
import type {
  BuildMetadataParams,
  ConsumeCreditsParams,
  CreateFreeParams,
  CreditAccountConstructorParams,
  CreditAccountMetadata,
  LanguageModelUsageParams,
} from './credit-account.types';
import { CreditTransaction } from './credit-transaction.entity';
import type { ClaudeUsageMetadata, OpenAIUsageMetadata } from './credit-transaction.types';

/**
 * 积分账户聚合根 - 极简设计，遵循 YAGNI 原则
 * 负责管理用户的月度积分余额和消耗逻辑
 */
export class CreditAccount extends AggregateRoot {
  private static readonly logger = new Logger(CreditAccount.name);
  private static readonly TIER_CREDITS = {
    [ProductTier.FREE]: 2000,
    [ProductTier.PRO]: 20000,
    [ProductTier.MAX]: 200000,
  };

  // 模型定价配置 - 每 1000 tokens 对应的积分数
  private static readonly MODEL_PRICING: Record<
    string,
    {
      provider: 'openai' | 'claude';
      inputRate: number;
      outputRate: number;
      cacheRate?: number;
      reasoningRate?: number;
    }
  > = {
    // OpenAI 模型定价
    [LLMs.GPT_4O]: {
      provider: 'openai' as const,
      inputRate: 2.5, // $2.50/MTok = 2.5 credits/1k tokens
      outputRate: 10, // $10/MTok = 10 credits/1k tokens
      cacheRate: 1.25, // $1.25/MTok = 1.25 credits/1k tokens
    },
    [LLMs.GPT_4O_MINI]: {
      provider: 'openai' as const,
      inputRate: 0.15, // $0.15/MTok = 0.15 credits/1k tokens
      outputRate: 0.6, // $0.60/MTok = 0.6 credits/1k tokens
      cacheRate: 0.075, // $0.075/MTok = 0.075 credits/1k tokens
    },
    [LLMs.GPT_41]: {
      provider: 'openai' as const,
      inputRate: 2, // $2/MTok = 2 credits/1k tokens
      outputRate: 8, // $8/MTok = 8 credits/1k tokens
      cacheRate: 0.5, // $0.5/MTok = 0.5 credits/1k tokens
    },
    [LLMs.GPT_41_MINI]: {
      provider: 'openai' as const,
      inputRate: 0.4, // $0.4/MTok = 0.4 credits/1k tokens
      outputRate: 1.6, // $1.6/MTok = 1.6 credits/1k tokens
      cacheRate: 0.1, // $0.1/MTok = 0.1 credits/1k tokens
    },
    [LLMs.GPT_41_NANO]: {
      provider: 'openai' as const,
      inputRate: 0.1, // $0.1/MTok = 0.1 credits/1k tokens
      outputRate: 0.4, // $0.4/MTok = 0.4 credits/1k tokens
      cacheRate: 0.025, // $0.025/MTok = 0.025 credits/1k tokens
    },
    [LLMs.O1_PREVIEW]: {
      provider: 'openai' as const,
      inputRate: 15, // $15/MTok = 15 credits/1k tokens
      outputRate: 60, // $60/MTok = 60 credits/1k tokens
      reasoningRate: 60, // $60/MTok = 60 credits/1k tokens
    },
    [LLMs.O1_MINI]: {
      provider: 'openai' as const,
      inputRate: 3, // $3/MTok = 3 credits/1k tokens
      outputRate: 12, // $12/MTok = 12 credits/1k tokens
      reasoningRate: 12, // $12/MTok = 12 credits/1k tokens
    },
    [LLMs.O3_MINI]: {
      provider: 'openai' as const,
      inputRate: 1.1, // $1.1/MTok = 1.1 credits/1k tokens
      outputRate: 4.4, // $4.4/MTok = 4.4 credits/1k tokens
      cacheRate: 0.55, // $0.55/MTok = 0.55 credits/1k tokens
      reasoningRate: 4.4, // 推理tokens使用输出费率
    },
    [LLMs.O4_MINI]: {
      provider: 'openai' as const,
      inputRate: 1.1, // $1.1/MTok = 1.1 credits/1k tokens
      outputRate: 4.4, // $4.4/MTok = 4.4 credits/1k tokens
      cacheRate: 0.275, // $0.275/MTok = 0.275 credits/1k tokens
      reasoningRate: 4.4, // 推理tokens使用输出费率
    },
    // Claude 模型定价
    [LLMs.CLAUDE_4_SONNET]: {
      provider: 'claude' as const,
      inputRate: 3, // $3/MTok = 3 credits/1k tokens
      outputRate: 15, // $15/MTok = 15 credits/1k tokens
      cacheRate: 0.3, // $0.3/MTok = 0.3 credits/1k tokens
    },
    [LLMs.CLAUDE_35_SONNET]: {
      provider: 'claude' as const,
      inputRate: 3, // $3/MTok = 3 credits/1k tokens
      outputRate: 15, // $15/MTok = 15 credits/1k tokens
      cacheRate: 0.3, // $0.3/MTok = 0.3 credits/1k tokens
    },
    [LLMs.CLAUDE_35_HAIKU]: {
      provider: 'claude' as const,
      inputRate: 0.8, // $0.8/MTok = 0.8 credits/1k tokens
      outputRate: 4, // $4/MTok = 4 credits/1k tokens
      cacheRate: 0.08, // $0.08/MTok = 0.08 credits/1k tokens
    },
    [LLMs.CLAUDE_37_SONNET]: {
      provider: 'claude' as const,
      inputRate: 3, // $3/MTok = 3 credits/1k tokens
      outputRate: 15, // $15/MTok = 15 credits/1k tokens
      cacheRate: 0.3, // $0.3/MTok = 0.3 credits/1k tokens
    },
    [LLMs.CLAUDE_37_SONNET_THINKING]: {
      provider: 'claude' as const,
      inputRate: 3, // $3/MTok = 3 credits/1k tokens
      outputRate: 15, // $15/MTok = 15 credits/1k tokens
      cacheRate: 0.3, // $0.3/MTok = 0.3 credits/1k tokens
    },
    // Google 模型定价
    [LLMs.GEMINI_25_PRO]: {
      provider: 'openai' as const, // 使用通用格式
      inputRate: 1.25, // $1.25/MTok = 1.25 credits/1k tokens
      outputRate: 2.5, // $2.5/MTok = 2.5 credits/1k tokens
    },
    [LLMs.GEMINI_25_FLASH]: {
      provider: 'openai' as const, // 使用通用格式
      inputRate: 0.15, // $0.15/MTok = 0.15 credits/1k tokens
      outputRate: 0.6, // $0.6/MTok = 0.6 credits/1k tokens
    },
    [LLMs.GEMINI_25_FLASH_LITE]: {
      provider: 'openai' as const, // 使用通用格式
      inputRate: 0.1, // $0.1/MTok = 0.1 credits/1k tokens
      outputRate: 0.4, // $0.4/MTok = 0.4 credits/1k tokens
    },
    [LLMs.GEMINI_2_FLASH]: {
      provider: 'openai' as const, // 使用通用格式
      inputRate: 0.15, // $0.15/MTok = 0.15 credits/1k tokens
      outputRate: 0.6, // $0.6/MTok = 0.6 credits/1k tokens
    },
    [LLMs.GEMINI_2_FLASH_LITE]: {
      provider: 'openai' as const, // 使用通用格式
      inputRate: 0.075, // $0.075/MTok = 0.075 credits/1k tokens
      outputRate: 0.3, // $0.3/MTok = 0.3 credits/1k tokens
    },
    // DeepSeek 模型定价
    [LLMs.DEEPSEEK_CHAT]: {
      provider: 'openai' as const, // 使用通用格式，免费模型
      inputRate: 0,
      outputRate: 0,
    },
    [LLMs.DEEPSEEK_REASONER]: {
      provider: 'openai' as const, // 使用通用格式
      inputRate: 1.35, // $1.35/MTok = 1.35 credits/1k tokens
      outputRate: 5, // $5/MTok = 5 credits/1k tokens
      reasoningRate: 5, // 推理tokens使用输出费率
    },
    // 阿里云 Qwen 模型定价
    [LLMs.QWEN_TURBO]: {
      provider: 'openai' as const, // 使用通用格式
      inputRate: 0.05, // $0.05/MTok = 0.05 credits/1k tokens
      outputRate: 0.2, // $0.2/MTok = 0.2 credits/1k tokens
    },
    [LLMs.QWEN_PLUS]: {
      provider: 'openai' as const, // 使用通用格式
      inputRate: 0.4, // $0.4/MTok = 0.4 credits/1k tokens
      outputRate: 1.2, // $1.2/MTok = 1.2 credits/1k tokens
    },
    [LLMs.QWEN_MAX]: {
      provider: 'openai' as const, // 使用通用格式
      inputRate: 1.6, // $1.6/MTok = 1.6 credits/1k tokens
      outputRate: 6.4, // $6.4/MTok = 6.4 credits/1k tokens
    },
  };

  public readonly id: string;
  public readonly spaceId: string;
  public readonly createdAt: Date;

  private _monthlyBalance: number;
  private _refreshCycleAnchor: Date;
  private _currentPeriodStart: Date;
  private _currentPeriodEnd: Date;
  private _productTier: ProductTier;
  private _metadata: CreditAccountMetadata;
  private _updatedAt: Date;
  private _isNew: boolean = false;

  constructor(params: CreditAccountConstructorParams) {
    super();
    this.id = params.id;
    this.spaceId = params.spaceId;
    this.createdAt = params.createdAt;
    this._monthlyBalance = params.monthlyBalance;
    this._refreshCycleAnchor = params.refreshCycleAnchor;
    this._currentPeriodStart = params.currentPeriodStart;
    this._currentPeriodEnd = params.currentPeriodEnd;
    this._productTier = params.productTier;
    this._metadata = params.metadata || {};
    this._updatedAt = params.updatedAt;
  }

  // Getters
  get monthlyBalance(): number {
    return this._monthlyBalance;
  }

  get refreshCycleAnchor(): Date {
    return this._refreshCycleAnchor;
  }

  get currentPeriodStart(): Date {
    return this._currentPeriodStart;
  }

  get currentPeriodEnd(): Date {
    return this._currentPeriodEnd;
  }

  get productTier(): ProductTier {
    return this._productTier;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  get metadata(): CreditAccountMetadata {
    return this._metadata;
  }

  get isNew(): boolean {
    return this._isNew;
  }

  get testClock(): Stripe.TestHelpers.TestClock | undefined {
    return this._metadata.testClock;
  }

  set testClock(testClock: Stripe.TestHelpers.TestClock | undefined) {
    this._metadata.testClock = testClock;
    this._updatedAt = new Date();
  }

  // 静态工厂方法：为免费用户创建积分账户
  static createFree(params: CreateFreeParams): {
    creditAccount: CreditAccount;
    initialTransaction: CreditTransaction;
  } {
    const { spaceId, testClock } = params;
    const now = testClock?.frozen_time ? new Date(testClock.frozen_time * 1000) : new Date();
    const id = uuidv7();
    const FREE_USER_CREDITS = CreditAccount.TIER_CREDITS[ProductTier.FREE];

    // 使用公共方法计算周期时间
    const [periodStart, periodEnd] = CreditAccount.calculatePeriod(now, now);

    const account = new CreditAccount({
      id,
      spaceId,
      monthlyBalance: FREE_USER_CREDITS,
      refreshCycleAnchor: now, // 锚点为创建时间
      currentPeriodStart: periodStart, // 当前周期开始
      currentPeriodEnd: periodEnd, // 当前周期结束
      productTier: ProductTier.FREE,
      createdAt: now,
      updatedAt: now,
      metadata: testClock ? { testClock } : {},
    });

    // 创建初始积分交易记录
    const initialTransaction = CreditTransaction.grant({
      creditAccountId: id,
      spaceId,
      amount: FREE_USER_CREDITS,
      balanceBefore: 0,
      reason: `Initial credits for free user - Period: ${periodStart.toISOString()}-${periodEnd.toISOString()}`,
    });

    account.apply(new CreditAccountCreatedEvent(id, params.spaceId, FREE_USER_CREDITS, now));
    account._isNew = true;

    return { creditAccount: account, initialTransaction };
  }

  /**
   * 根据订阅创建积分账户
   * @param subscription 订阅
   * @returns 积分账户和初始积分交易记录
   */
  static createBySubscription(subscription: Subscription): {
    creditAccount: CreditAccount;
    initialTransaction: CreditTransaction;
  } {
    // 订阅不处于有效状态，创建免费用户
    if (
      subscription.status === SubscriptionStatus.INCOMPLETE ||
      subscription.status === SubscriptionStatus.PAST_DUE
    ) {
      return CreditAccount.createFree({ spaceId: subscription.spaceId });
    }

    const id = uuidv7();
    const USER_CREDITS = CreditAccount.TIER_CREDITS[subscription.productTier];
    const refreshCycleAnchor = subscription.renewCycleAnchor;
    const now = new Date();

    // CreditAccount 应该根据 refreshCycleAnchor 和当前时间计算自己的月度周期
    // 而不是直接使用 Subscription 的周期（特别是年付的情况）
    const [currentPeriodStart, currentPeriodEnd] = CreditAccount.calculatePeriod(
      refreshCycleAnchor,
      now,
    );

    // 打印双方的ID和周期信息对比
    CreditAccount.logger.log(
      `Creating CreditAccount from Subscription:
      ├─ Subscription ID: ${subscription.id}
      ├─ CreditAccount ID: ${id}
      ├─ Billing Interval: ${subscription.billingInterval}
      ├─ Product Tier: ${subscription.productTier}
      ├─ Anchor: ${refreshCycleAnchor.toISOString()}
      ├─ Subscription Period: [${subscription.currentPeriodStart.toISOString()}, ${subscription.currentPeriodEnd.toISOString()})
      └─ CreditAccount Period: [${currentPeriodStart.toISOString()}, ${currentPeriodEnd.toISOString()})`,
    );

    // 对于月付订阅，如果周期不一致则警告

    if (
      subscription.currentPeriodStart?.getTime() !== currentPeriodStart.getTime() ||
      subscription.currentPeriodEnd?.getTime() !== currentPeriodEnd.getTime()
    ) {
      if (subscription.billingInterval === BillingInterval.MONTHLY) {
        CreditAccount.logger.warn(
          `⚠️  Monthly subscription periods don't match - this might indicate an issue!`,
        );
      } else {
        CreditAccount.logger.log(
          `✅ Yearly subscription - CreditAccount has its own monthly periods (expected behavior)`,
        );
      }
    }

    const account = new CreditAccount({
      id,
      spaceId: subscription.spaceId,
      monthlyBalance: USER_CREDITS,
      refreshCycleAnchor, // 锚点为创建时间
      currentPeriodStart, // 当前周期开始
      currentPeriodEnd, // 当前周期结束
      productTier: subscription.productTier,
      createdAt: now,
      updatedAt: now,
    });

    // 创建初始积分交易记录
    const initialTransaction = CreditTransaction.grant({
      creditAccountId: id,
      spaceId: subscription.spaceId,
      amount: USER_CREDITS,
      balanceBefore: 0,
      reason: `Initial credits for paid user - Period: ${currentPeriodStart.toISOString()}-${currentPeriodEnd.toISOString()}`,
    });

    account.apply(new CreditAccountCreatedEvent(id, subscription.spaceId, USER_CREDITS, now));
    account._isNew = true;

    return { creditAccount: account, initialTransaction };
  }

  // 业务方法：消耗积分
  consume(params: ConsumeCreditsParams): CreditTransaction {
    const balanceBefore = this._monthlyBalance;
    this._monthlyBalance -= params.amount;
    this._updatedAt = new Date();

    // 创建消耗交易记录
    const consumeTransaction = CreditTransaction.consume({
      creditAccountId: this.id,
      spaceId: this.spaceId,
      amount: params.amount,
      balanceBefore,
      balanceAfter: this._monthlyBalance,
      reason: params.reason,
      metadata: params.metadata,
    });

    this.apply(
      new CreditsConsumedEvent(
        this.id,
        this.spaceId,
        params.amount,
        balanceBefore,
        this._monthlyBalance,
        params.reason,
        params.metadata || {},
        this._updatedAt,
      ),
    );

    return consumeTransaction;
  }

  // 查询方法：获取当前产品等级对应的积分额度
  getMonthlyQuota(): number {
    return CreditAccount.TIER_CREDITS[this._productTier];
  }

  /**
   * 根据情况尝试刷新或强制刷新
   */
  resetOrRefreshIfNeeded(productTier: ProductTier, refreshCycleAnchor: Date): CreditTransaction[] {
    // 如果 productTier 和 refreshCycleAnchor 都没变，则尝试刷新
    if (
      this._productTier === productTier &&
      this._refreshCycleAnchor.getTime() === refreshCycleAnchor.getTime()
    ) {
      return this.refreshIfNeeded();
    }
    // 否则，更新 productTier 和 refreshCycleAnchor，然后强制刷新
    this._productTier = productTier;
    this._refreshCycleAnchor = refreshCycleAnchor;
    // 强制刷新
    return this.refreshIfNeeded(true);
  }

  /**
   * 幂等的安全的刷新
   * - force 为 true 时，会强制刷新
   * - force 为 false 时，如果 periodStart 没变，说明已经刷新过了，则不刷新
   */
  refreshIfNeeded(force: boolean = false): CreditTransaction[] {
    const now = this.testClock?.frozen_time
      ? new Date(this.testClock.frozen_time * 1000)
      : new Date();

    // 根据指定日期所处的周期
    const [periodStart, periodEnd] = this.calculatePeriod(now);
    /**
     * 防重检查：如果 periodStart 没变，说明已经刷新过了
     * 当上层方法需要重置时，可以传入 force = true 来强制刷新
     */
    if (periodStart.getTime() === this._currentPeriodStart.getTime() && !force) {
      return [];
    }

    const transactions: CreditTransaction[] = [];
    const newCredits = CreditAccount.TIER_CREDITS[this._productTier];

    // 1. 废弃旧积分
    if (this._monthlyBalance > 0) {
      const forfeitTransaction = CreditTransaction.forfeit({
        creditAccountId: this.id,
        spaceId: this.spaceId,
        amount: -this._monthlyBalance, // 负数表示废弃
        balanceBefore: this._monthlyBalance,
        reason: `Forfeit old credits - Refresh for period: ${periodStart.toISOString()} - ${periodEnd.toISOString()}`,
      });
      transactions.push(forfeitTransaction);
      this._monthlyBalance = 0;
    }

    // 2. 发放新积分
    const grantTransaction = CreditTransaction.grant({
      creditAccountId: this.id,
      spaceId: this.spaceId,
      amount: newCredits,
      balanceBefore: this._monthlyBalance,
      reason: `Grant ${newCredits} credits - Refresh for period: ${periodStart.toISOString()} - ${periodEnd.toISOString()}`,
    });
    transactions.push(grantTransaction);

    // 3. 更新账户状态（不改变 productTier 和 refreshCycleAnchor）
    this._monthlyBalance = newCredits;
    this._currentPeriodStart = periodStart;
    this._currentPeriodEnd = periodEnd;
    this._updatedAt = new Date();

    return transactions;
  }

  /**
   * 对于 Subscripton 来说，有两种事件：
   * - 到期事件
   *   - 到期取消（直接 delete subscription）
   *   - 到期更新，[currentPeriodStart, currentPeriodEnd] 变化，可能变化：
   *     - productTier
   *     - billingInterval
   * - 突发事件
   *   - 升级，一定变化 productTier, renewCycleAnchor, [currentPeriodStart, currentPeriodEnd], 可能变化：
   *     - billingInterval
   *   - 立即取消（直接 delete subscription）
   *
   * 对于 CreditAccount 来说，只关心 Subscription 的 productTier 和 renewCycleAnchor 变化，然后就可以自己在正确的时间刷新正确的积分
   * - CreditAccount 的刷新 key 是 productTier + currentPeriodStart
   * *重要*：CreditAccount 的 currentPeriodStart 跟 Subscription 的 currentPeriodStart 不一样，永远是按月推进的
   *
   * CreditAccount 的 [currentPeriodStart, currentPeriodEnd] 可完全通过 refreshCycleAnchor 来计算
   * 不需要从 Subscription 的 [currentPeriodStart, currentPeriodEnd] 同步，而且年付的 Subscription 的 [currentPeriodStart, currentPeriodEnd] 永远跟 CreditAccount 的 [currentPeriodStart, currentPeriodEnd] 不一样
   */
  syncFromSubscription(subscription: Subscription): CreditTransaction[] {
    // 订阅不处于有效状态，不同步
    if (
      subscription.status === SubscriptionStatus.INCOMPLETE ||
      subscription.status === SubscriptionStatus.PAST_DUE
    ) {
      return [];
    }

    return this.resetOrRefreshIfNeeded(subscription.productTier, subscription.renewCycleAnchor);
  }

  // ========== 周期时间计算方法 ==========

  /**
   * 根据锚点和指定日期计算包含该日期的积分周期
   *
   * 边界条件说明：
   * - 如果 date 正好等于当月刷新日期：属于当前周期
   * - 周期是左闭右开区间：[start, end)
   * - 即：start <= date < end
   *
   * 例子：锚点是每月15号10:00
   * - calculatePeriod(anchor, "2024-01-15 10:00") → ["2024-01-15 10:00", "2024-02-15 10:00")
   * - calculatePeriod(anchor, "2024-01-15 09:59") → ["2023-12-15 10:00", "2024-01-15 10:00")
   * - calculatePeriod(anchor, "2024-01-20 12:00") → ["2024-01-15 10:00", "2024-02-15 10:00")
   *
   * @param anchor 锚点时间（用于确定每月的具体日期和时间）
   * @param now 指定日期
   * @returns [currentPeriodStart, currentPeriodEnd] 左闭右开区间
   */
  static calculatePeriod(anchor: Date, date?: Date): [Date, Date] {
    const now = date ?? new Date();

    const targetYear = now.getFullYear();
    const targetMonth = now.getMonth();

    // 计算当月的刷新日期
    const lastDayOfCurrentMonth = new Date(targetYear, targetMonth + 1, 0).getDate();
    const currentMonthDay = Math.min(anchor.getDate(), lastDayOfCurrentMonth);

    const currentMonthRefresh = new Date(
      targetYear,
      targetMonth,
      currentMonthDay,
      anchor.getHours(),
      anchor.getMinutes(),
      anchor.getSeconds(),
      anchor.getMilliseconds(),
    );

    let periodStart: Date;
    let periodEnd: Date;

    // 关键边界判断：date >= currentMonthRefresh 说明在当月刷新日期之后（包含相等）
    if (now >= currentMonthRefresh) {
      // 属于当月周期：[当月刷新日, 下月刷新日)
      periodStart = currentMonthRefresh;

      // 计算下个月的刷新日期
      let nextMonth = targetMonth + 1;
      let nextYear = targetYear;
      if (nextMonth > 11) {
        nextMonth = 0;
        nextYear++;
      }

      const lastDayOfNextMonth = new Date(nextYear, nextMonth + 1, 0).getDate();
      const nextMonthDay = Math.min(anchor.getDate(), lastDayOfNextMonth);

      periodEnd = new Date(
        nextYear,
        nextMonth,
        nextMonthDay,
        anchor.getHours(),
        anchor.getMinutes(),
        anchor.getSeconds(),
        anchor.getMilliseconds(),
      );
    } else {
      // 属于上月周期：[上月刷新日, 当月刷新日)
      periodEnd = currentMonthRefresh;

      // 计算上个月的刷新日期
      let prevMonth = targetMonth - 1;
      let prevYear = targetYear;
      if (prevMonth < 0) {
        prevMonth = 11;
        prevYear--;
      }

      const lastDayOfPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate();
      const prevMonthDay = Math.min(anchor.getDate(), lastDayOfPrevMonth);

      periodStart = new Date(
        prevYear,
        prevMonth,
        prevMonthDay,
        anchor.getHours(),
        anchor.getMinutes(),
        anchor.getSeconds(),
        anchor.getMilliseconds(),
      );
    }

    return [periodStart, periodEnd];
  }

  calculatePeriod(date?: Date): [Date, Date] {
    return CreditAccount.calculatePeriod(this._refreshCycleAnchor, date ?? new Date());
  }

  // 实体生命周期方法
  markAsExisting(): void {
    this._isNew = false;
  }

  // 业务方法：重置为免费用户状态
  resetToFree(): CreditTransaction[] {
    const transactions: CreditTransaction[] = [];
    const FREE_USER_CREDITS = CreditAccount.TIER_CREDITS[ProductTier.FREE];

    // 1. 废弃当前积分（如果有的话）
    if (this._monthlyBalance > 0) {
      const forfeitTransaction = CreditTransaction.forfeit({
        creditAccountId: this.id,
        spaceId: this.spaceId,
        amount: -this._monthlyBalance,
        balanceBefore: this._monthlyBalance,
        reason: `Reset to free user: forfeit old credits`,
      });
      transactions.push(forfeitTransaction);
    }

    // 2. 发放免费用户积分
    const grantTransaction = CreditTransaction.grant({
      creditAccountId: this.id,
      spaceId: this.spaceId,
      amount: FREE_USER_CREDITS,
      balanceBefore: 0,
      reason: `Reset to free user: grant free user credits`,
    });
    transactions.push(grantTransaction);

    const now = this.testClock?.frozen_time
      ? new Date(this.testClock.frozen_time * 1000)
      : new Date();
    // 3. 更新账户状态 - 使用公共方法计算周期时间
    const [periodStart, periodEnd] = this.calculatePeriod(now);

    this._monthlyBalance = FREE_USER_CREDITS;
    this._productTier = ProductTier.FREE;
    this._refreshCycleAnchor = now; // 锚点为当前时间
    this._currentPeriodStart = periodStart; // 当前周期开始
    this._currentPeriodEnd = periodEnd; // 当前周期结束
    this._updatedAt = new Date();

    return transactions;
  }

  // ========== Token 计费方法 ==========

  /**
   * 基于 Vercel AI 使用量消耗积分
   * @param params Vercel AI 使用参数
   * @returns 消费交易记录
   */
  consumeByTokens(params: LanguageModelUsageParams): CreditTransaction | undefined {
    const { model, usage, providerMetadata } = params;

    // 计算应消耗的积分
    const creditsToConsume = this.calculateCreditsByTokens(params);

    if (creditsToConsume <= 0) {
      CreditAccount.logger.warn(params, `No credits to consume`);
      return;
    }

    // 构建元数据
    const reason = `Large language model usage - ${model}`;
    const metadata = this.buildVercelAiMetadata(model, usage, providerMetadata, creditsToConsume);

    // 执行消费
    return this.consume({
      amount: creditsToConsume,
      reason,
      metadata,
    });
  }

  /**
   * 基于 Vercel AI token 使用量计算积分消耗
   */
  private calculateCreditsByTokens(params: LanguageModelUsageParams): number {
    const { model, usage, providerMetadata } = params;

    const pricing = CreditAccount.MODEL_PRICING[model];
    if (!pricing) {
      CreditAccount.logger.warn(`Unknown model: ${model}, using default OpenAI pricing`);
      return this.calculateCreditsWithDefaultPricing(usage);
    }

    let totalCredits = 0;

    // 1. 基础输入和输出 token
    totalCredits += (usage.promptTokens / 1000) * pricing.inputRate;
    totalCredits += (usage.completionTokens / 1000) * pricing.outputRate;

    // 2. 处理 OpenAI 特有的 token 类型
    if (pricing.provider === 'openai' && providerMetadata.openai) {
      const openaiMeta = providerMetadata.openai;

      // 推理 token (o1 系列)
      if (openaiMeta.reasoningTokens && 'reasoningRate' in pricing && pricing.reasoningRate) {
        totalCredits += (openaiMeta.reasoningTokens / 1000) * pricing.reasoningRate;
      }

      // 缓存 token
      if (openaiMeta.cachedPromptTokens && pricing.cacheRate) {
        totalCredits += (openaiMeta.cachedPromptTokens / 1000) * pricing.cacheRate;
      }
    }

    // 3. 处理 Anthropic 特有的 token 类型
    if (pricing.provider === 'claude' && providerMetadata.anthropic) {
      const anthropicMeta = providerMetadata.anthropic;

      // 缓存命中
      if (anthropicMeta.cacheReadInputTokens && pricing.cacheRate) {
        totalCredits += (anthropicMeta.cacheReadInputTokens / 1000) * pricing.cacheRate;
      }
    }

    // 向上取整到最小计费单位（1积分）
    return Math.ceil(totalCredits);
  }

  /**
   * 未知模型的默认定价计算
   */
  private calculateCreditsWithDefaultPricing(usage: LanguageModelUsage): number {
    const defaultPricing = CreditAccount.MODEL_PRICING[LLMs.GPT_4O_MINI]; // 使用较便宜的默认定价

    let totalCredits = 0;
    totalCredits += (usage.promptTokens / 1000) * defaultPricing.inputRate;
    totalCredits += (usage.completionTokens / 1000) * defaultPricing.outputRate;

    return Math.ceil(totalCredits);
  }

  /**
   * 构建 Vercel AI 使用的元数据
   */
  private buildVercelAiMetadata(
    model: LLMs,
    usage: LanguageModelUsage,
    providerMetadata: LanguageModelUsageParams['providerMetadata'],
    totalCredits: number,
  ): OpenAIUsageMetadata | ClaudeUsageMetadata {
    const pricing = CreditAccount.MODEL_PRICING[model];
    const traceId = `vercel-ai-${Date.now()}-${Math.random().toString(36).slice(2)}`;

    const params: BuildMetadataParams = {
      model,
      usage,
      providerMetadata,
      totalCredits,
      traceId,
    };

    if (pricing?.provider === 'openai') {
      return this.buildOpenAIMetadata(params);
    } else {
      return this.buildClaudeMetadata(params);
    }
  }

  private buildOpenAIMetadata(params: BuildMetadataParams): OpenAIUsageMetadata {
    const { model, usage, providerMetadata, totalCredits, traceId } = params;
    const pricing =
      CreditAccount.MODEL_PRICING[model] || CreditAccount.MODEL_PRICING[LLMs.GPT_4O_MINI];
    const openaiMeta = providerMetadata.openai;

    return {
      provider: 'openai',
      model,
      feature: 'chat',
      traceId,
      tokenUsage: {
        prompt_tokens: usage.promptTokens,
        completion_tokens: usage.completionTokens,
        total_tokens: usage.totalTokens,
        completion_tokens_details: openaiMeta
          ? {
              reasoning_tokens: openaiMeta.reasoningTokens,
              accepted_prediction_tokens: openaiMeta.acceptedPredictionTokens,
              rejected_prediction_tokens: openaiMeta.rejectedPredictionTokens,
            }
          : undefined,
        prompt_tokens_details: openaiMeta
          ? {
              cached_tokens: openaiMeta.cachedPromptTokens,
            }
          : undefined,
      },
      creditsCalculation: {
        provider: 'openai',
        inputCredits: (usage.promptTokens / 1000) * pricing.inputRate,
        outputCredits: (usage.completionTokens / 1000) * pricing.outputRate,
        cacheCredits:
          openaiMeta?.cachedPromptTokens && pricing.cacheRate
            ? (openaiMeta.cachedPromptTokens / 1000) * pricing.cacheRate
            : 0,
        reasoningCredits:
          openaiMeta?.reasoningTokens && 'reasoningRate' in pricing
            ? (openaiMeta.reasoningTokens / 1000) * (pricing.reasoningRate || 0)
            : 0,
        imageCredits: 0,
        totalCredits,
      },
    };
  }

  private buildClaudeMetadata(params: BuildMetadataParams): ClaudeUsageMetadata {
    const { model, usage, providerMetadata, totalCredits, traceId } = params;
    const pricing =
      CreditAccount.MODEL_PRICING[model] || CreditAccount.MODEL_PRICING[LLMs.CLAUDE_35_SONNET];
    const anthropicMeta = providerMetadata.anthropic;

    return {
      provider: 'claude',
      model,
      feature: 'chat',
      traceId,
      tokenUsage: {
        input_tokens: usage.promptTokens,
        output_tokens: usage.completionTokens,
        cache_read_input_tokens: anthropicMeta?.cacheReadInputTokens,
        cache_creation_input_tokens: anthropicMeta?.cacheCreationInputTokens,
      } as Usage,
      creditsCalculation: {
        inputCredits: (usage.promptTokens / 1000) * pricing.inputRate,
        outputCredits: (usage.completionTokens / 1000) * pricing.outputRate,
        cacheReadCredits:
          anthropicMeta?.cacheReadInputTokens && pricing.cacheRate
            ? (anthropicMeta.cacheReadInputTokens / 1000) * pricing.cacheRate
            : 0,
        cache5mCredits: 0, // Vercel AI 不提供具体的缓存创建细节
        cache1hCredits: 0,
        totalCredits,
        tokenBreakdown: {
          inputTokens: usage.promptTokens,
          outputTokens: usage.completionTokens,
          cacheReadTokens: anthropicMeta?.cacheReadInputTokens || 0,
          cache5mTokens: 0,
          cache1hTokens: 0,
        },
        rateBreakdown: {
          inputRate: pricing.inputRate,
          outputRate: pricing.outputRate,
          cacheHitRate: pricing.cacheRate || 0,
          cache5mRate: 0,
          cache1hRate: 0,
        },
      },
    };
  }
}
