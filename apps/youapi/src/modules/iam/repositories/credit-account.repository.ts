import { Injectable, NotFoundException } from '@nestjs/common';
import { and, eq, lte, sql } from 'drizzle-orm';

import { BaseRepository } from '@/common/database/base.repository';
import { creditAccounts } from '../../../shared/db/public.schema';
import { CreditAccount } from '../domain/credit/models/credit-account.entity';
import type { CreditAccountMetadata } from '../domain/credit/models/credit-account.types';
import { ProductTier } from '../domain/subscription/models/subscription.types';

// 数据库对象类型
type CreditAccountDO = typeof creditAccounts.$inferSelect;

/**
 * 积分账户 Repository
 *
 * 管理积分账户的持久化和查询操作
 * 每个 space 对应一个积分账户，是一对一关系
 */
@Injectable()
export class CreditAccountRepository extends BaseRepository {
  /**
   * 按 spaceId 查找积分账户
   */
  async findBySpaceId(spaceId: string): Promise<CreditAccount | undefined> {
    const result = await this.db
      .select()
      .from(creditAccounts)
      .where(eq(creditAccounts.spaceId, spaceId));

    if (result.length === 0) {
      return;
    }

    return this.doToEntity(result[0]);
  }

  async findBySpaceIdForUpdate(spaceId: string): Promise<CreditAccount | undefined> {
    const result = await this.db
      .select()
      .from(creditAccounts)
      .where(eq(creditAccounts.spaceId, spaceId))
      .for('update');

    if (result.length === 0) {
      return;
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 按 spaceId 获取积分账户 - 必须存在
   */
  async getBySpaceId(spaceId: string): Promise<CreditAccount> {
    const account = await this.findBySpaceId(spaceId);
    if (!account) {
      throw new NotFoundException(`Credit account for space ${spaceId} not found`);
    }
    return account;
  }

  /**
   * 按 spaceId 获取积分账户并加锁 - 用于防止并发操作
   * 使用 SELECT FOR UPDATE 锁定记录，防止并发更新
   */
  async getBySpaceIdForUpdate(spaceId: string): Promise<CreditAccount> {
    const result = await this.db
      .select()
      .from(creditAccounts)
      .where(eq(creditAccounts.spaceId, spaceId))
      .for('update');

    if (result.length === 0) {
      throw new NotFoundException(`Credit account for space ${spaceId} not found`);
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 按 ID 查找积分账户
   */
  async findById(id: string): Promise<CreditAccount | null> {
    const result = await this.db.select().from(creditAccounts).where(eq(creditAccounts.id, id));

    if (result.length === 0) {
      return null;
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 按 ID 获取积分账户 - 必须存在
   */
  async getById(id: string): Promise<CreditAccount> {
    const account = await this.findById(id);
    if (!account) {
      throw new NotFoundException(`Credit account ${id} not found`);
    }
    return account;
  }

  async getByIdForUpdate(id: string): Promise<CreditAccount> {
    const result = await this.db
      .select()
      .from(creditAccounts)
      .where(eq(creditAccounts.id, id))
      .for('update');
    if (result.length === 0) {
      throw new NotFoundException(`Credit account ${id} not found`);
    }
    return this.doToEntity(result[0]);
  }

  /**
   * 保存积分账户
   */
  async save(account: CreditAccount): Promise<void> {
    const do_ = this.entityToDO(account);

    if (account.isNew) {
      await this.db.insert(creditAccounts).values(do_);
      account.markAsExisting();
    } else {
      const { id, ...shouldUpdate } = do_;
      await this.db.update(creditAccounts).set(shouldUpdate).where(eq(creditAccounts.id, id));
    }
  }

  /**
   * 查找到期的积分账户（需要刷新的账户）
   * 基于 currentPeriodEnd 字段查询，提高定时任务效率
   *
   * @returns 到期的积分账户列表
   */
  async findExpiredAccountsBefore(date: Date): Promise<CreditAccount[]> {
    const result = await this.db
      .select()
      .from(creditAccounts)
      .where(lte(creditAccounts.currentPeriodEnd, date))
      .orderBy(creditAccounts.currentPeriodEnd); // 最早到期的排在前面

    return result.map((accountDO: CreditAccountDO) => this.doToEntity(accountDO));
  }

  async findExpiredAccountsInTestClock(date: Date): Promise<CreditAccount[]> {
    const result = await this.db
      .select()
      .from(creditAccounts)
      .where(
        and(
          lte(creditAccounts.currentPeriodEnd, date),
          sql`${creditAccounts.metadata}->>'testClockTime' IS NOT NULL`,
        ),
      )
      .orderBy(creditAccounts.currentPeriodEnd); // 最早到期的排在前面

    return result.map((accountDO: CreditAccountDO) => this.doToEntity(accountDO));
  }

  async findInTestClock(): Promise<CreditAccount[]> {
    const result = await this.db
      .select()
      .from(creditAccounts)
      .where(and(sql`${creditAccounts.metadata}->>'testClockTime' IS NOT NULL`))
      .orderBy(creditAccounts.currentPeriodEnd); // 最早到期的排在前面

    return result.map((accountDO: CreditAccountDO) => this.doToEntity(accountDO));
  }

  async findByTestClockIdForUpdate(testClockId: string): Promise<CreditAccount | undefined> {
    const result = await this.db
      .select()
      .from(creditAccounts)
      .where(eq(sql`${creditAccounts.metadata}->'testClock'->>'id'`, testClockId))
      .for('update');

    if (result.length === 0) {
      return;
    }

    return this.doToEntity(result[0]);
  }

  // ========== 实体转换方法 ==========

  /**
   * 实体转换为数据对象
   */
  private entityToDO(account: CreditAccount): CreditAccountDO {
    return {
      id: account.id,
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
      spaceId: account.spaceId,
      monthlyBalance: account.monthlyBalance,
      refreshCycleAnchor: account.refreshCycleAnchor,
      currentPeriodStart: account.currentPeriodStart,
      currentPeriodEnd: account.currentPeriodEnd,
      productTier: account.productTier,
      metadata: account.metadata,
    };
  }

  /**
   * 数据对象转换为实体
   */
  private doToEntity(accountDO: CreditAccountDO): CreditAccount {
    const account = new CreditAccount({
      id: accountDO.id,
      spaceId: accountDO.spaceId,
      monthlyBalance: accountDO.monthlyBalance,
      refreshCycleAnchor: accountDO.refreshCycleAnchor,
      currentPeriodStart: accountDO.currentPeriodStart,
      currentPeriodEnd: accountDO.currentPeriodEnd,
      productTier: accountDO.productTier as ProductTier,
      metadata: accountDO.metadata as CreditAccountMetadata,
      createdAt: accountDO.createdAt,
      updatedAt: accountDO.updatedAt,
    });

    account.markAsExisting();
    return account;
  }
}
