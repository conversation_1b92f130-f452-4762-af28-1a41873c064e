/**
 * Web Search Internal Search Domain Service - 内部搜索领域服务
 * 提供基于全局素材库的内部搜索功能
 *
 * Migrated from:
 * - youapp/src/domain/web-search/internal-search.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';

import { type InternetSearchResult, MessageAtReferenceTypeEnum } from '../../common/types';
import { withTimeout } from '../../common/utils/concurrency';

import { ContextBuilder } from '../chat/context';
import type { ChatDetail } from '../chat/types';
import { getGlobalMaterialsLibraryMeta } from '../global-materials-library/util';
import type { WebSearchParams } from './types';

// type SnipBrief = Awaited<ReturnType<typeof SnipDomainService.prototype.listBriefSnipsByIds>>[0];

@Injectable()
export class InternalSearchDomainService {
  private readonly logger = new Logger(InternalSearchDomainService.name);

  constructor(
    // private readonly snipDomain: SnipDomainService, // TODO: 移除 snipDomain 依赖，改到
    private readonly contextBuilder: ContextBuilder,
  ) {}
  async search(params: WebSearchParams, span: LangfuseSpanClient): Promise<InternetSearchResult[]> {
    // FIXME: 如果 type 为 webpage，才会走内容库。后续如果有视频，需要调整此逻辑
    if (params.type !== 'webpage') return [];
    // [FIXME] 目前缺乏新鲜度过滤，multilingual_queries 尚未使用（使用后需要去重），语言也尚未使用
    return withTimeout(
      this.performSearch(
        {
          ...params,
          // 目前 jina rerank 最多支持 1w token。理论上应该先检索回少量内容，最后rerank结束后，再召回相邻及关联内容。
          availableTokens: 10000,
        },
        span,
      ),
      10000,
    ).catch((error) => {
      this.logger.error('internal search error', error, params);
      return [];
    });
  }

  private async performSearch(
    params: {
      query: string;
      language?: string;
      chat: ChatDetail;
      availableTokens: number;
    },
    _span: LangfuseSpanClient,
  ): Promise<InternetSearchResult[]> {
    const now = Date.now();
    const { query, chat, availableTokens, language } = params;

    // FIXME: 目前只有中文走内容库。后续有其它内容库时，需要做区分，如根据
    if (language && !language.startsWith('zh')) return [];

    const { user_id, board_id } = getGlobalMaterialsLibraryMeta();

    const finalAtReferences = [
      {
        at_name: 'board',
        entity_type: MessageAtReferenceTypeEnum.BOARD,
        entity_id: board_id,
      },
    ];

    const analysis = {
      needsContext: true,
      // 不能拿全量内容，否则会很容易超出 rerank 限制
      contextType: 'relevant' as const,
      searchKeywords: query,
    };
    return [];

    // const contextResult = await this.contextBuilder.buildContextFromReferences({
    //   messages: chat.messages || [],
    //   at_references: finalAtReferences,
    //   availableTokens,
    //   userId: user_id,
    //   trace: span,
    //   analysis,
    //   quickMode: true,
    // });

    // // 使用flatMap和Map优化性能，一次遍历完成snip数据收集和建立id->chunk映射
    // const snipChunkMap = new Map<string, string>();

    // // 使用flatMap替代嵌套循环，直接提取所有snip chunks
    // const snipChunks = contextResult.atReferencesRetrieved.flatMap((reference) =>
    //   reference.chunks.filter((chunk) => chunk.type === 'snip'),
    // );

    // // 如果没有snip结果，提前返回
    // if (!snipChunks.length) {
    //   return [];
    // }

    // // 建立id到chunk的映射，优化后续查找性能
    // snipChunks.forEach((chunk) => {
    //   snipChunkMap.set(chunk.id, chunk.chunk);
    // });

    // // 提取唯一的snip IDs
    // const snipIds = Array.from(snipChunkMap.keys());

    // // 使用 snipDomain 获取完整的 snip 数据
    // const snips = await this.snipDomain.listBriefSnipsByIds(snipIds);

    // // 将 snip 数据转换为 InternetSearchResult 格式，使用Map进行O(1)查找
    // const results: InternetSearchResult[] = snips.map((snip: SnipBrief) => {
    //   // 使用Map进行O(1)查找，替代原来的O(n)查找
    //   const relatedChunk = snipChunkMap.get(snip.id) || '';

    //   // 基础信息
    //   const result: InternetSearchResult = {
    //     type: InternetSearchResultTypeEnum.WEBPAGE,
    //     url: `/snips/${snip.id}`, // 内部链接
    //     title: snip.title || '无标题',
    //     related_chunk: relatedChunk,
    //   };

    //   // 添加网页相关信息（如果有）
    //   if (snip.webpage) {
    //     result.site_name = snip.webpage.site?.name;
    //     result.favicon = snip.webpage.site?.favicon_url;

    //     // 如果内部 snip 是从外部网页保存的，使用原始网页 URL
    //     if (snip.webpage.url) {
    //       result.url = snip.webpage.url;
    //     }
    //   }

    //   result.extra = {
    //     global_material_id: snip.id,
    //   };

    //   // 添加文件信息（如果有）
    //   if (snip.file) {
    //     // 处理文件类型
    //     if ('storage_url' in snip.file) {
    //       result.extra.file = {
    //         storage_url: snip.file.storage_url,
    //         mime_type: snip.file.mime_type,
    //         name: snip.file.name,
    //       };
    //     } else if ('original_url' in snip.file) {
    //       result.extra.file = {
    //         original_url: snip.file.original_url,
    //       };
    //     }
    //   }

    //   return result;
    // });

    // span.event({
    //   name: 'internal_search_results_by_query',
    //   input: {
    //     query,
    //     results,
    //     duration: Date.now() - now,
    //   },
    // });
    // return results;
  }
}

// Export class for DI instead of instance
// const internalSearchDomain = new InternalSearchDomainService();
// export default internalSearchDomain;
