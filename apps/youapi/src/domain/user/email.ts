import { Injectable, Logger } from '@nestjs/common';
import { emailService } from '@repo/server-common';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  public async send(param: { email: string; subject: string; content: React.ReactNode }) {
    const { email, subject, content } = param;

    try {
      const { emails } = await emailService.send({
        email,
        subject,
        content,
      });

      this.logger.log(`Email sent successfully to ${email}`);
      return emails;
    } catch (error) {
      this.logger.error('call email API error', error);
      throw error;
    }
  }

  public async sendBatch(params: { emails: string[]; subject: string; content: React.ReactNode }) {
    try {
      const { emails } = await emailService.sendBatch(
        params.emails,
        params.subject,
        params.content,
      );

      this.logger.log(`Batch email sent successfully to ${params.emails.length} recipients`);
      return emails;
    } catch (error) {
      this.logger.error('call email batch API error', error);
      throw error;
    }
  }
}
