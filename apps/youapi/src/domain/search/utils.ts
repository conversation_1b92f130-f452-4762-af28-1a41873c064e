import { getServicesEnv } from '@repo/common';
import { franc } from 'franc-min';
import { marked } from 'marked';
import striptags from 'striptags';
import { FieldType } from 'typesense/lib/Typesense/Collection';
import { ChunkingStrategy, chunkText } from '@/common/utils';
import { getEnvIdentifier } from '../../common/utils';

import {
  AllFieldsNeedLocalize,
  CONTENT_SPLIT_SIZE,
  DEFAULT_LOCALE,
  type DetectedLangKeys,
  FieldsNeedSplit,
  LANGUAGE_LOCALE_MAP,
  type Locales,
  type Localized,
  type LocalizedAndSplitted,
  localize,
  localizeAndSplit,
  SEARCH_LANGUAGE_LOCALE_MAP,
  type SplitIndex,
  type TypesenseCollection,
} from './definition';

export function toLocalizedFieldSchema(fieldName: string, weight = 1) {
  const localizedFieldNames = toLocalizedFieldNames(fieldName);
  return localizedFieldNames.map((fieldName) => ({
    name: fieldName,
    type: 'string' as FieldType,
    optional: true,
    weight,
    stem: fieldName.startsWith('en'),
    locale: fieldName.split('_')[0],
  }));
}

/**
 * title -> [zh_title, en_title, ...]
 * content -> [zh_content_1, zh_content_2, ..., en_content_1, en_content_2, ...]
 * id -> [id]
 */
export function toLocalizedFieldNames(fieldName: string, detectedLang?: string): string[] {
  const isLocalized = AllFieldsNeedLocalize.includes(fieldName as AllFieldsNeedLocalize);

  if (!isLocalized) {
    return [`${fieldName}`];
  }

  if (detectedLang) {
    const locale = SEARCH_LANGUAGE_LOCALE_MAP[detectedLang as DetectedLangKeys] || DEFAULT_LOCALE;
    if (FieldsNeedSplit.includes(fieldName as FieldsNeedSplit)) {
      return Array.from({ length: CONTENT_SPLIT_SIZE }, (_, i) =>
        localizeAndSplit(fieldName, locale, i as SplitIndex),
      );
    }
    return [localize(fieldName, locale)];
  }

  // special case for content field, split into CONTENT_SPLIT_SIZE pieces for each locale
  if (FieldsNeedSplit.includes(fieldName as FieldsNeedSplit)) {
    return Object.values(SEARCH_LANGUAGE_LOCALE_MAP).flatMap((locale) => {
      return Array.from({ length: CONTENT_SPLIT_SIZE }, (_, i) =>
        localizeAndSplit(fieldName, locale, i as SplitIndex),
      );
    });
  }

  return Object.values(SEARCH_LANGUAGE_LOCALE_MAP).map((locale) => localize(fieldName, locale));
}

export function detectLang(text: string) {
  return franc(text, { minLength: 2 });
}

export function detectLanguageCode(text: string) {
  return LANGUAGE_LOCALE_MAP[detectLang(text) as DetectedLangKeys] || DEFAULT_LOCALE;
}

export function detectLocale(text: string) {
  return (
    SEARCH_LANGUAGE_LOCALE_MAP[detectLang(text) as keyof typeof SEARCH_LANGUAGE_LOCALE_MAP] ||
    DEFAULT_LOCALE
  );
}

export async function parsePlainContent({
  content,
  fieldName,
  locale,
  chunkCount = CONTENT_SPLIT_SIZE,
  chunkingStrategy = ChunkingStrategy.TOTAL_CHUNK_COUNT,
}: {
  content: string;
  fieldName: FieldsNeedSplit;
  locale?: Locales;
  chunkCount?: number;
  chunkingStrategy?: ChunkingStrategy;
}) {
  if (typeof content !== 'string' || !content) {
    return [];
  }
  if (content.trim() === '') {
    return [];
  }

  const preprocessedContent = await preprocessText(content);

  if (!locale) {
    locale =
      SEARCH_LANGUAGE_LOCALE_MAP[detectLang(preprocessedContent) as DetectedLangKeys] ||
      DEFAULT_LOCALE;
  }

  if (chunkingStrategy === ChunkingStrategy.TOTAL_CHUNK_COUNT) {
    const chunks = await chunkText({
      content: preprocessedContent,
      chunkingStrategy,
      chunkCount,
    });

    return chunks.map((chunk) => ({
      originalField: fieldName,
      name: localizeAndSplit(fieldName, locale, chunk.chunkIndex as SplitIndex),
      chunkIndex: chunk.chunkIndex,
      text: chunk.text,
    }));
  }

  if (chunkingStrategy === ChunkingStrategy.SINGLE_CHUNK_TOKEN_SIZE) {
    const chunks = await chunkText({
      content: preprocessedContent,
      chunkingStrategy,
    });

    return chunks.map((chunk) => ({
      originalField: fieldName,
      name: localizeAndSplit(fieldName, locale, chunk.chunkIndex as SplitIndex),
      chunkIndex: chunk.chunkIndex,
      text: chunk.text,
    }));
  }

  return [];
}

export type LocalizedObject = Record<
  | keyof Localized<Exclude<AllFieldsNeedLocalize, 'content'>>
  | keyof LocalizedAndSplitted<FieldsNeedSplit>,
  string
>;

export function removeLocalePrefix(fieldName: string): string {
  const localesToRemove = Object.values(SEARCH_LANGUAGE_LOCALE_MAP);
  return fieldName.replace(new RegExp(`^(${localesToRemove.join('|')})_`), '');
}

export function restoreLocalizedFields(object: Partial<LocalizedObject>) {
  return Object.entries(object)
    .map(([key, value]) => ({
      [removeLocalePrefix(key)]: value,
    }))
    .reduce((prev, curr) => ({ ...prev, ...curr }), {});
}

export function getCollectionNameWithEnv(collection: TypesenseCollection, env = getServicesEnv()) {
  return `${collection}-${getEnvIdentifier(env)}`;
}

interface PreprocessOptions {
  /** Whether to strip HTML/Markdown formatting. Defaults to true */
  stripFormatting?: boolean;
  /** Whether to normalize Unicode characters. Defaults to true */
  normalizeUnicode?: boolean;
  /** Whether to normalize spacing and punctuation. Defaults to true */
  normalizeSpacing?: boolean;
  /** Whether to preserve case. Defaults to false (will convert to lowercase) */
  preserveCase?: boolean;
  /** Maximum length of input text to process. Defaults to 1MB */
  maxInputLength?: number;
}

/**
 * Checks if text contains actual markup patterns that should be processed
 */
function isActualMarkup(text: string): boolean {
  // HTML: Look for properly formed tags with attributes
  const definiteHtmlPattern = /<([a-z][a-z0-9]*)(?:\s+[a-z0-9-]+(?:=["'][^"']*["'])?)*\s*>/i;

  // Common HTML entities that indicate actual HTML content
  const htmlEntityPattern = /&(?:quot|amp|lt|gt|nbsp|copy|reg|trade|times|divide);/i;

  // Markdown: Look for common unambiguous patterns
  const definiteMarkdownPatterns = [
    /^#{1,6}\s+\S/m, // Headers with content
    /\*\*\S[^*\n]*\S\*\*/, // Bold with content
    /^>\s+\S/m, // Blockquotes with content
    /^```\w*\n[\s\S]*?\n```/m, // Code blocks
    /^\s*[-*+]\s+\S/m, // Unordered lists with content
    /^\d+\.\s+\S/m, // Ordered lists with content
    /\[.+\]\(.+\)/, // Links with both text and URL
    /!\[.+\]\(.+\)/, // Images with both alt text and URL
  ];

  // Check for definitive HTML
  if (definiteHtmlPattern.test(text) || htmlEntityPattern.test(text)) {
    return true;
  }

  // Check for definitive Markdown
  return definiteMarkdownPatterns.some((pattern) => pattern.test(text));
}

/**
 * Preprocesses text for search indexing by stripping markup and applying various normalizations.
 * Carefully handles special patterns and only processes actual markup.
 *
 * @param text - The input text to preprocess
 * @param options - Optional configuration for preprocessing
 * @returns The preprocessed text
 * @throws Error if the input is invalid or if critical processing fails
 */
async function preprocessText(text: string, options: PreprocessOptions = {}): Promise<string> {
  const {
    stripFormatting = true,
    normalizeSpacing = true,
    maxInputLength = 1024 * 1024 * 5, // 5MB limit
  } = options;

  try {
    // Input validation
    if (typeof text !== 'string') {
      throw new Error('Input must be a string');
    }

    if (text.length > maxInputLength) {
      throw new Error(`Input text exceeds maximum length of ${maxInputLength} characters`);
    }

    // Early return for empty strings
    if (!text.trim()) {
      return '';
    }

    let processedText = text;

    // Step 1: Strip formatting (HTML and Markdown)
    if (stripFormatting && isActualMarkup(processedText)) {
      try {
        marked.setOptions({
          gfm: false,
          breaks: false,
          pedantic: true,
        });

        // First remove possible <p ym-citation="1">...</p>
        processedText = striptags(processedText);
        // Then parse as markdown
        processedText = await marked.parse(processedText);
        // Then remove all html tags and get plain visible text
        processedText = striptags(processedText);
      } catch (error) {
        console.warn('Markup processing failed:', error);
        // On error, revert to original text
        processedText = text;
      }
    }
    // Step 2: Spacing normalization
    if (normalizeSpacing) {
      processedText = processedText
        .split('\n')
        .map((line) => {
          // Normalize horizontal whitespace within each line
          return (
            line
              // Replace all horizontal whitespace with single space
              .replace(/[ \t\u00A0\u1680\u2000-\u200A\u202F\u205F\u3000]+/g, ' ')
              // Remove zero-width spaces and other invisible characters
              .replace(/[\u200B-\u200D\uFEFF]/g, '')
              .trim()
          );
        })
        .filter((line) => line.length > 0) // Remove empty lines
        .join('\n');
    }

    // Final validation
    if (processedText.length === 0) {
      console.warn('Preprocessing resulted in empty string');
      return '';
    }

    return processedText;
  } catch (error) {
    // Log the error for debugging
    console.error('Error in preprocessText:', error);

    // Throw a more user-friendly error
    throw new Error(
      `Failed to preprocess text: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

export { preprocessText, type PreprocessOptions };

export const CJK_PATTERN =
  /[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\uff66-\uff9f，。！？、：；——～“”《》]/g;
const NON_ENDING_CHINESE_CHAR_PATTERN = /[，、]/;

export function addCJKSpacing(text: string): string {
  let result = '';
  let prevIsCJK = isCJKChar(text.charAt(0));

  for (let i = 0; i < text.length; i++) {
    const prevChar = i > 0 ? text.charAt(i - 1) : '';
    const char = text.charAt(i);
    const currentIsCJK = isCJKChar(char);

    if (
      i > 0 &&
      currentIsCJK !== prevIsCJK &&
      !NON_ENDING_CHINESE_CHAR_PATTERN.test(char) &&
      !NON_ENDING_CHINESE_CHAR_PATTERN.test(prevChar)
    ) {
      result += ' ';
    }

    result += char;
    prevIsCJK = currentIsCJK;
  }

  return result;
}

function isCJKChar(char: string): boolean {
  return /[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\uff66-\uff9f，。！？、：；——～“”《》]/.test(
    char,
  );
}
