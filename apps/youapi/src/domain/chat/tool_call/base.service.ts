/**
 * Base Tool Service - 工具服务基类
 * 定义所有工具服务的通用接口和模式
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/type.ts
 */

import { Injectable } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import type { CompletionStreamChunk, TOOL_TYPES } from '../../../common/types';
import type { StreamMessage } from '../../../infra/youllm/types';
import { StreamChunkUnion } from '../ask_ai/handler';
import type { ToolCall, ToolCallResult, ToolDefinition, ToolFunctionParameters } from './types';

/**
 * Base abstract class for all tool services
 * 所有工具服务的抽象基类
 */
@Injectable()
export abstract class BaseToolService {
  abstract readonly toolName: TOOL_TYPES;
  abstract readonly toolDefinition: ToolDefinition;

  /**
   * Execute the tool function
   * 执行工具函数
   */
  abstract execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
  ):
    | AsyncGenerator<StreamMessage | CompletionStreamChunk<StreamChunkUnion>, ToolCallResult>
    | Promise<ToolCallResult>;

  /**
   * Get the complete tool call object
   * 获取完整的工具调用对象
   */
  getToolCall(): ToolCall {
    return {
      tool_name: this.toolName,
      tool_function: this.execute.bind(this),
      tool_definition: this.toolDefinition,
    };
  }
}
