import request from 'supertest';
import { TransactionType } from '@/modules/iam/domain/credit/models/credit-transaction.types';
import { CreditAccountRepository } from '@/modules/iam/repositories/credit-account.repository';
import { CreditTransactionRepository } from '@/modules/iam/repositories/credit-transaction.repository';
import { SubscriptionRepository } from '@/modules/iam/repositories/subscription.repository';
import { UserRepository } from '@/modules/iam/repositories/user.repository';
import { SupabaseService } from '@/shared/supabase/supabase.service';
import { TestIamModuleHelper } from './test-iam-module-helper';

describe('UserController (e2e)', () => {
  let helper: TestIamModuleHelper;
  let authToken: string;

  beforeAll(async () => {
    helper = new TestIamModuleHelper();
    await helper.init();
    authToken = await helper.genJwt();
  });

  describe('POST /api/v1/getCurrentUser', () => {
    describe('成功场景', () => {
      it('应该返回当前用户信息', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/getCurrentUser')
          .set('Authorization', `Bearer ${authToken}`)
          .send();

        console.log('response', {
          status: response.status,
          body: response.body,
        });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('email');
        expect(response.body).toHaveProperty('preference');
        expect(response.body).toHaveProperty('space');
      });
    });

    describe('认证检查', () => {
      it('应该拒绝未认证的请求', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/getCurrentUser')
          .send();

        expect(response.status).toBe(401);
      });

      it('应该拒绝无效的 token', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/getCurrentUser')
          .set('Authorization', 'Bearer invalid-token')
          .send();

        expect(response.status).toBe(401);
      });
    });
  });

  describe('POST /api/v1/user/initCurrentUser', () => {
    describe('成功场景', () => {
      it('对已初始化的用户应该不进行初始化', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/initCurrentUser')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            timeZone: 'Asia/Shanghai',
          });

        expect(response.status).toBe(200);
        expect(response.body.init).toBe(false);
      });

      it('对未初始化的用户应该进行初始化（对应设计文档测试1：免费用户初始化）', async () => {
        const supabaseService = helper.moduleRef.get(SupabaseService);
        const supabase = supabaseService.createAdminClient();
        const { data, error } = await supabase.auth.signUp({
          email: `test-${Date.now()}@example.com`,
          password: '123456',
        });
        if (error) {
          throw error;
        }

        const jwt = await helper.genJwt(data.user.id);

        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/initCurrentUser')
          .set('Authorization', `Bearer ${jwt}`)
          .set('Content-Type', 'application/json')
          .set('x-use-camel-case', 'true')
          .send({
            timeZone: 'Asia/Shanghai',
          });

        console.log('🔍 Init Current User Response:', {
          status: response.status,
          body: response.body,
          headers: response.headers,
        });

        expect(response.status).toBe(200);
        expect(response.body.init).toBe(true);
        expect(response.body.user.timeZone).toBe('Asia/Shanghai');
        expect(response.body.user.preference).toBeDefined();
        expect(response.body.user.space).toBeDefined();

        // 验证免费用户没有 Subscription（根据设计文档：付费用户才有订阅）
        const subscriptionRepository = helper.moduleRef.get(SubscriptionRepository);
        const subscription = await subscriptionRepository.findBySpaceId(
          response.body.user.space.id,
        );
        expect(subscription).toBeNull(); // 免费用户不应该有订阅记录

        // 验证 CreditAccount 已创建（符合设计文档测试1的要求）
        const creditAccountRepository = helper.moduleRef.get(CreditAccountRepository);
        const creditAccount = await creditAccountRepository.findBySpaceId(
          response.body.user.space.id,
        );
        expect(creditAccount).toBeDefined();
        expect(creditAccount.monthlyBalance).toBe(2000); // 设计文档：CreditAccount.monthlyBalance = 2000
        expect(creditAccount.productTier).toBe('free'); // 设计文档：CreditAccount.productTier = 'FREE'
        expect(creditAccount.spaceId).toBe(response.body.user.space.id);
        expect(creditAccount.resetAnchor).toBeDefined(); // 设计文档：resetAnchor = registrationTime
        expect(creditAccount.resetAt).toBeDefined(); // 设计文档：resetAt = registrationTime

        // 验证 CreditTransaction 已创建（符合设计文档测试1的要求）
        const creditTransactionRepository = helper.moduleRef.get(CreditTransactionRepository);
        const transactions = await creditTransactionRepository.findBySpaceId(
          response.body.user.space.id,
        );
        expect(transactions).toBeDefined();
        expect(transactions.length).toBe(1); // 设计文档：1条 CreditTransaction

        const initialTransaction = transactions[0];
        expect(initialTransaction.type).toBe(TransactionType.GRANT); // 设计文档：type = 'GRANT'
        expect(initialTransaction.amount).toBe(2000); // 设计文档：amount = 2000
        expect(initialTransaction.reason).toContain('Initial'); // 设计文档：reason = 'Initial credits for free user...'
        expect(initialTransaction.balanceAfter).toBe(2000); // 初始化后余额应该为2000
        expect(initialTransaction.spaceId).toBe(response.body.user.space.id);

        // TODO 验证 defaultBoard 已创建

        // 删除用户
        await supabase.auth.admin.deleteUser(data.user.id);
      });

      it('事务回滚测试：当保存用户失败时，整个初始化应该回滚', async () => {
        const supabaseService = helper.moduleRef.get(SupabaseService);
        const supabase = supabaseService.createAdminClient();

        // 创建新的测试用户
        const { data, error } = await supabase.auth.signUp({
          email: `test-rollback-${Date.now()}@example.com`,
          password: '123456',
        });
        if (error) {
          throw error;
        }

        const jwt = await helper.genJwt(data.user.id);

        try {
          // 获取 UserRepository 实例并 mock 其 save 方法
          const userRepository = helper.moduleRef.get(UserRepository);

          // Mock save 方法抛出异常
          jest.spyOn(userRepository, 'save').mockImplementationOnce(async () => {
            throw new Error('Mock database error: User save failed');
          });

          // 调用初始化 API，预期失败
          const response = await request(helper.app.getHttpServer())
            .post('/api/v1/user/initCurrentUser')
            .set('Authorization', `Bearer ${jwt}`)
            .set('Content-Type', 'application/json')
            .set('x-use-camel-case', 'true')
            .send({
              timeZone: 'Asia/Shanghai',
            });

          console.log('🔍 Transaction Rollback Test Response:', {
            status: response.status,
            body: response.body,
          });

          // 预期 API 返回错误
          expect(response.status).toBe(500);

          // 验证事务回滚：由于事务回滚，UserPreference 和 Space 都不应该被创建
          // 因此 getCurrentUser 应该返回 404（因为找不到 UserPreference）
          const getCurrentUserResponse = await request(helper.app.getHttpServer())
            .post('/api/v1/getCurrentUser')
            .set('Authorization', `Bearer ${jwt}`)
            .send();

          console.log('🔍 After Rollback - Current User:', {
            status: getCurrentUserResponse.status,
            body: getCurrentUserResponse.body,
          });

          // 验证事务回滚成功：由于 UserPreference 没有被创建（事务回滚），
          // getCurrentUser 应该返回 404
          expect(getCurrentUserResponse.status).toBe(404);
          expect(getCurrentUserResponse.body.message).toContain('UserPreference');

          console.log('✅ 事务回滚验证成功：UserPreference 和 Space 都没有被创建');
        } finally {
          // 清理：删除测试用户
          await supabase.auth.admin.deleteUser(data.user.id);
        }
      });

      it('应该处理空时区参数', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/initCurrentUser')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            timeZone: '',
          });

        expect(response.status).toBe(200);
      });
    });

    describe('参数验证', () => {
      it('应该拒绝缺少 timeZone 参数的请求', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/initCurrentUser')
          .set('Authorization', `Bearer ${authToken}`)
          .send({});

        console.log('🔍 Init Current User Response:', {
          status: response.status,
          body: response.body,
        });

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('timeZone');
      });

      it('应该拒绝非字符串类型的 timeZone', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/initCurrentUser')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            timeZone: 123,
          });

        expect(response.status).toBe(400);
      });
    });

    describe('认证检查', () => {
      it('应该拒绝未认证的请求', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/initCurrentUser')
          .send({
            timeZone: 'UTC',
          });

        expect(response.status).toBe(401);
      });
    });
  });

  describe('POST /api/v1/user/patchUserAvatar', () => {
    describe('成功场景', () => {
      it('应该成功更新用户头像', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserAvatar')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            avatarUrl: 'https://example.com/avatar.jpg',
          });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('picture');
        expect(response.body.picture).toBe('https://example.com/avatar.jpg');
      });

      it('应该接受空字符串清除头像', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserAvatar')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            avatarUrl: '',
          });

        expect(response.status).toBe(200);
        expect(response.body.picture).toBe('');
      });
    });

    describe('参数验证', () => {
      it('应该拒绝缺少 avatarUrl 参数的请求', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserAvatar')
          .set('Authorization', `Bearer ${authToken}`)
          .send({});

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('avatarUrl');
      });

      it('应该拒绝非字符串类型的 avatarUrl', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserAvatar')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            avatarUrl: { url: 'https://example.com/avatar.jpg' },
          });

        expect(response.status).toBe(400);
      });

      it('应该处理超长的 URL', async () => {
        const longUrl = `https://example.com/${'a'.repeat(2000)}.jpg`;
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserAvatar')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            avatarUrl: longUrl,
          });

        expect(response.status).toBe(200);
      });
    });
  });

  describe('POST /api/v1/user/patchUserName', () => {
    describe('成功场景', () => {
      it('应该成功更新用户姓名', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserName')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            name: 'John Doe',
          });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('name');
        expect(response.body.name).toBe('John Doe');
      });

      it('应该处理包含特殊字符的姓名', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserName')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            name: '张三-李四 (John)',
          });

        expect(response.status).toBe(200);
        expect(response.body.name).toBe('张三-李四 (John)');
      });

      it('应该处理空字符串姓名', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserName')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            name: '',
          });

        expect(response.status).toBe(200);
        expect(response.body.name).toBe('');
      });
    });

    describe('参数验证', () => {
      it('应该拒绝缺少 name 参数的请求', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserName')
          .set('Authorization', `Bearer ${authToken}`)
          .send({});

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('name');
      });

      it('应该拒绝非字符串类型的 name', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserName')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            name: 12345,
          });

        expect(response.status).toBe(400);
      });

      it('应该处理超长的姓名', async () => {
        const longName = 'A'.repeat(500);
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/patchUserName')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            name: longName,
          });

        expect(response.status).toBe(200);
      });
    });
  });

  describe('POST /api/v1/user/setUserTimeZoneIfNotSet', () => {
    describe('成功场景', () => {
      it('应该设置未设置的时区', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/setUserTimeZoneIfNotSet')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            timeZone: 'America/New_York',
          });

        expect(response.status).toBe(200);
      });

      it('应该不覆盖已设置的时区', async () => {
        // 第一次设置
        await request(helper.app.getHttpServer())
          .post('/api/v1/user/setUserTimeZoneIfNotSet')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            timeZone: 'Asia/Tokyo',
          });

        // 第二次尝试设置不同的时区
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/setUserTimeZoneIfNotSet')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            timeZone: 'Europe/London',
          });

        expect(response.status).toBe(200);
        // 时区应该保持第一次设置的值
      });
    });

    describe('参数验证', () => {
      it('应该拒绝缺少 timeZone 参数的请求', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/setUserTimeZoneIfNotSet')
          .set('Authorization', `Bearer ${authToken}`)
          .send({});

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('timeZone');
      });

      it('应该接受空字符串时区', async () => {
        const response = await request(helper.app.getHttpServer())
          .post('/api/v1/user/setUserTimeZoneIfNotSet')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            timeZone: '',
          });

        expect(response.status).toBe(200);
      });
    });
  });

  // TODO 需要专门注册用户来测试删除用户
  // describe('POST /api/v1/user/deleteCurrentUser', () => {
  //   describe('成功场景', () => {
  //     it('应该成功删除当前用户', async () => {
  //       // 注意：这个测试应该在最后运行，因为会删除用户
  //       const deleteUserId = 'user-to-delete';
  //       const deleteUserToken = await helper.genJwt(deleteUserId);

  //       const response = await request(helper.app.getHttpServer())
  //         .post('/api/v1/user/deleteCurrentUser')
  //         .set('Authorization', `Bearer ${deleteUserToken}`)
  //         .send();

  //       expect(response.status).toBe(200);
  //     });
  //   });

  //   describe('认证检查', () => {
  //     it('应该拒绝未认证的请求', async () => {
  //       const response = await request(helper.app.getHttpServer())
  //         .post('/api/v1/user/deleteCurrentUser')
  //         .send();

  //       expect(response.status).toBe(401);
  //     });
  //   });

  //   describe('删除后验证', () => {
  //     it('删除用户后应该无法获取用户信息', async () => {
  //       const deleteUserId = 'another-user-to-delete';
  //       const deleteUserToken = await helper.genJwt(deleteUserId);

  //       // 先删除用户
  //       await request(helper.app.getHttpServer())
  //         .post('/api/v1/user/deleteCurrentUser')
  //         .set('Authorization', `Bearer ${deleteUserToken}`)
  //         .send();

  //       // 尝试获取已删除的用户信息
  //       const response = await request(helper.app.getHttpServer())
  //         .post('/api/v1/getCurrentUser')
  //         .set('Authorization', `Bearer ${deleteUserToken}`)
  //         .send();

  //       expect(response.status).toBe(404);
  //     });
  //   });
  // });

  afterAll(async () => {
    await helper.destroy();
  });
});
