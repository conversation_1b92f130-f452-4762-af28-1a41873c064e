{
  // Cloudflare Workers 主配置
  "main": ".open-next/worker.js",
  "compatibility_date": "2025-07-01",
  "compatibility_flags": ["nodejs_compat", "global_fetch_strictly_public"],

  // 资产配置
  "assets": {
    "directory": ".open-next/assets",
    "binding": "ASSETS"
  },

  // 服务绑定
  "services": [
    {
      "binding": "WORKER_SELF_REFERENCE",
      "service": "youhome"
    }
  ],

  // 观察性配置
  "observability": {
    "logs": {
      "enabled": true
    }
  },

  // 环境配置
  "env": {
    // 预览环境配置
    "preview": {
      "name": "youhome-preview",
      "assets": {
        "directory": ".open-next/assets",
        "binding": "ASSETS"
      },
      "services": [
        {
          "binding": "WORKER_SELF_REFERENCE",
          "service": "youhome-preview"
        }
      ],
      "r2_buckets": [
        {
          "binding": "NEXT_INC_CACHE_R2_BUCKET",
          "bucket_name": "youhome-cached-pages-preview"
        }
      ],
      // 预览环境的 durable_objects 配置（使用标准绑定名称）
      "durable_objects": {
        "bindings": [
          {
            "name": "NEXT_CACHE_DO_QUEUE",
            "class_name": "DOQueueHandler"
          },
          // This is only required if you use On-demand revalidation
          {
            "name": "NEXT_TAG_CACHE_DO_SHARDED",
            "class_name": "DOShardedTagCache"
          },
          {
            "name": "NEXT_CACHE_DO_PURGE",
            "class_name": "BucketCachePurge"
          }
        ]
      }
    },

    // 生产环境配置
    "production": {
      "name": "youhome-production",
      "assets": {
        "directory": ".open-next/assets",
        "binding": "ASSETS"
      },
      "services": [
        {
          "binding": "WORKER_SELF_REFERENCE",
          "service": "youhome-production"
        }
      ],
      "r2_buckets": [
        {
          "binding": "NEXT_INC_CACHE_R2_BUCKET",
          "bucket_name": "youhome-cached-pages-production"
        }
      ],
      // 生产环境的 durable_objects 配置（使用标准绑定名称）
      "durable_objects": {
        "bindings": [
          {
            "name": "NEXT_CACHE_DO_QUEUE",
            "class_name": "DOQueueHandler"
          },
          // This is only required if you use On-demand revalidation
          {
            "name": "NEXT_TAG_CACHE_DO_SHARDED",
            "class_name": "DOShardedTagCache"
          },
          {
            "name": "NEXT_CACHE_DO_PURGE",
            "class_name": "BucketCachePurge"
          }
        ]
      }
    }
  },
  "migrations": [
    {
      "tag": "v1",
      "new_sqlite_classes": [
        "DOQueueHandler",
        // This is only required if you use On-demand revalidation
        "DOShardedTagCache",
        "BucketCachePurge"
      ]
    }
  ],
  "kv_namespaces": [
    {
      "binding": "MARKETING_APP_KV",
      // 生产环境
      "id": "92b0304094304a5e9c209b82d73c9a21",
      // 预发环境
      "preview_id": "83d56545b8d041779427b274d5317fa8",
      "experimental_remote": true
    },
    {
      "binding": "CELEBRITY_TASTE_KV",
      // 生产环境
      "id": "8c33270d00e94e78ae66821580d13aa3",
      // 预发环境
      "preview_id": "0be5a03ba4d04a08aab96f41347231cb",
      "experimental_remote": true
    }
  ],
  "keep_names": false

  // 环境变量现在通过 Doppler 远端管理
  // 其他可选配置示例：
  // "kv_namespaces": [
  //   {
  //     "binding": "MY_KV_NAMESPACE",
  //     "id": "your-kv-namespace-id"
  //   }
  // ],
  // "d1_databases": [
  //   {
  //     "binding": "DB",
  //     "database_name": "your-database-name",
  //     "database_id": "your-database-id"
  //   }
  // ]
}
