import { createCamelCaseApiClient, createSnakeCaseApiClient } from '@repo/api';
import { cookies } from 'next/headers';
import { UserWithPreferenceVO } from '@/schema/userSchema';

const isLocal = process.env.NODE_ENV === 'development';

export const createApiClientServerSide = async () => {
  const cookieStore = await cookies();

  return createCamelCaseApiClient({
    basePath: isLocal ? 'http://localhost:3001' : process.env.NEXT_PUBLIC_YOUMIND_API_BASE_PATH,
    headers: {
      'Content-Type': 'application/json',
      Cookie: cookieStore.toString(),
    },
  });
};

export const createApiClientOldServerSide = async () => {
  const cookieStore = await cookies();

  return createSnakeCaseApiClient({
    basePath: isLocal ? 'http://localhost:3001' : process.env.NEXT_PUBLIC_YOUMIND_API_BASE_PATH,
    headers: {
      'Content-Type': 'application/json',
      Cookie: cookieStore.toString(),
    },
  });
};

export async function tryGetCurrentUserFromServer() {
  const apiClient = await createApiClientServerSide();
  try {
    const user = await apiClient.userApi.getCurrentUser();
    return user as UserWithPreferenceVO;
  } catch (error: any) {
    return null;
  }
}
