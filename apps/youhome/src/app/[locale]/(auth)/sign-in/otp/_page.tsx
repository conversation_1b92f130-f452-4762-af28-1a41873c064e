'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@repo/ui/components/ui/button';
import { Input } from '@repo/ui/components/ui/input';
import { Label } from '@repo/ui/components/ui/label';
import { useAtom } from 'jotai';
import { Loader2 } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { type ClipboardEvent, useCallback, useEffect, useState } from 'react';
import { type GlobalError, useForm } from 'react-hook-form';
import { userAtom } from '@/hooks/useUser';
import { DEFAULT_ROUTE } from '@/lib/common/route';
import { apiClient, tryGetCurrentUserFromClient } from '@/lib/request/client';
import { cn } from '@/lib/utils';
import { type ValidateWithOTPParam, validateWithOTPSchema } from '@/schema/userSchema';
import { ElementClassMap } from '../components';
import LoginContainer from '../container';

export default function SignInWithOTPPage() {
  const searchParams = useSearchParams();
  const t = useTranslations('Auth.OTP');
  const router = useRouter();
  const [user, setUser] = useAtom(userAtom);
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const next = searchParams.get('next')
    ? decodeURIComponent(searchParams.get('next')!)
    : DEFAULT_ROUTE;
  const email = searchParams.get('email') ? decodeURIComponent(searchParams.get('email')!) : '';

  const form = useForm<ValidateWithOTPParam>({
    mode: 'onBlur',
    resolver: zodResolver(validateWithOTPSchema, {
      errorMap: (issue, ctx) => {
        if (issue.path[0] === 'token') {
          return { message: t('form.token.invalid') };
        }
        return { message: ctx.defaultError };
      },
    }),
    defaultValues: {
      email: email,
      token: searchParams.get('token') ? searchParams.get('token')!.split('') : [],
    },
  });
  const {
    register,
    handleSubmit,
    formState,
    setValue,
    getValues,
    setFocus,
    setError,
    clearErrors,
    trigger,
  } = form;
  const tokenErrors =
    formState.errors.token &&
    Array.from({ length: 6 })
      .map((_, i) => getValues(`token.${i}`))
      .join('')
      ? formState.errors.token
      : [];
  const tokenError = (tokenErrors as GlobalError[]).find((e) => e?.message)?.message || '';

  // 清除重发成功状态的定时器
  useEffect(() => {
    if (resendSuccess) {
      const timer = setTimeout(() => setResendSuccess(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [resendSuccess]);

  // 重发冷却倒计时
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const onSubmit = async (data: ValidateWithOTPParam) => {
    if (!data.email || isSubmitting) return;
    setIsSubmitting(true);

    try {
      const responseData = await apiClient.authApi.validateOTPToken({
        next,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        formData: {
          email: data.email,
          token: data.token.map((t) => t.toString()),
        },
      });

      const userPreference = await tryGetCurrentUserFromClient();

      if (userPreference) {
        setUser(userPreference);
      }
      // 开始导航状态
      setIsSubmitting(false);
      setIsNavigating(true);
      router.push(responseData.redirect);
    } catch (error) {
      setError('token.0', { message: t('form.token.invalid') });
      setIsSubmitting(false);
      return;
    }
  };
  const onKeyUp = (index: number, e: KeyboardEvent) => {
    // 开始输入时清除错误状态
    if (formState.errors.token) {
      clearErrors('token');
    }

    const curr = getValues(`token.${index}`) as unknown as string;
    const isEmpty = curr.trim() === '';
    if (!isEmpty && index < 5) {
      // 发生了输入
      setFocus(`token.${index + 1}`, { shouldSelect: true });
    }
    if (e.key === 'Backspace' && isEmpty && index >= 1) {
      // 发生了删除
      setFocus(`token.${index - 1}`, { shouldSelect: true });
    }

    // 如果所有输入框都填满了，则触发验证
    const allValues = Array.from({ length: 6 }).map((_, i) => getValues(`token.${i}`));
    if (allValues.every((v) => v.trim() !== '')) {
      trigger().then((isValid) => {
        if (isValid) {
          handleSubmit(onSubmit)();
        }
      });
    }
  };
  const onPaste = (e: ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    // @ts-expect-error typing
    const paste = (e.clipboardData || window.clipboardData).getData('text');
    // 改进粘贴处理：移除空格、破折号等，支持更长的代码
    const cleanPaste = paste.replace(/[\s-]/g, '');
    if (cleanPaste.length >= 6 && /^[0-9]+$/.test(cleanPaste)) {
      const code = cleanPaste.slice(0, 6); // 取前6位
      code.split('').forEach((t, i) => {
        setValue(`token.${i}`, t);
      });

      // 清除之前的错误
      if (formState.errors.token) {
        clearErrors('token');
      }

      trigger().then((isValid) => {
        if (isValid) {
          handleSubmit(onSubmit)();
        }
      });
    }
  };
  const onGoBack = (e: MouseEvent) => {
    window.history.back();
    e.preventDefault();
    e.stopPropagation();
  };
  const onResend = useCallback(async () => {
    if (!email || isResending || resendCooldown > 0) return;

    setIsResending(true);
    setResendSuccess(false);

    try {
      const res = await apiClient.authApi.signInWithOTP({
        next,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        email,
      });
      if (res.redirect) {
        router.push(res.redirect);
      }
      clearErrors();
      Array.from({ length: 6 }).forEach((_, i) => {
        setValue(`token.${i}`, '');
      });
      setResendSuccess(true);
      // 重发后聚焦第一个输入框，方便用户立即输入新验证码
      setFocus('token.0');
      // 开始60秒冷却时间
      setResendCooldown(60);
    } catch (error) {
      // 处理重发错误的情况
      console.error('Resend failed:', error);
    } finally {
      setIsResending(false);
    }
  }, [email, next, clearErrors, setValue, isResending, resendCooldown]);

  useEffect(() => {
    if (user?.id) {
      router.replace('/sign-in/onboard');
    }

    setFocus('token.0');
  }, []);

  // 当用户返回页面时自动聚焦输入框
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // 页面变为可见时，聚焦到第一个空的输入框
        const allValues = Array.from({ length: 6 }).map((_, i) => getValues(`token.${i}`));
        const firstEmptyIndex = allValues.findIndex((v) => !v || v.trim() === '');
        const focusIndex = firstEmptyIndex >= 0 ? firstEmptyIndex : 0;

        // 延迟一小段时间确保页面完全激活
        setTimeout(() => {
          setFocus(`token.${focusIndex}`);
        }, 100);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [setFocus, getValues]);

  return (
    <LoginContainer compact className="flex flex-col justify-center">
      <div className="token-page !pt-0">
        <div className="text-[40px] font-[590] leading-[48px]">{t('title')}</div>
        <div className="page-subtitle">{t('description', { email })}</div>
        <div className="form-container mt-[56px]">
          <form onSubmit={handleSubmit(onSubmit)}>
            <Input type="hidden" {...register('email')} />
            <Label htmlFor="token" className="hidden">
              {t('form.token.label')}
            </Label>
            <div className="token-container">
              {Array.from({ length: 6 }).map((_, i) => {
                return (
                  <input
                    {...register(`token.${i}`)}
                    key={i}
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={1}
                    className="token-input"
                    onPaste={onPaste}
                    // @ts-expect-error typing
                    onKeyUp={onKeyUp.bind(null, i)}
                    onKeyDown={(e) => {
                      // 如果有错误且按退格，清除所有输入
                      if (tokenError && e.key === 'Backspace') {
                        Array.from({ length: 6 }).forEach((_, idx) => {
                          setValue(`token.${idx}`, '');
                        });
                        clearErrors('token');
                        setFocus('token.0');
                        e.preventDefault();
                        return;
                      }

                      const values = getValues(`token.${i}`);
                      if (values.length === 1 && e.key !== 'Backspace') {
                        e.preventDefault();
                      }
                    }}
                    autoComplete="off"
                  />
                );
              })}
            </div>
            <div className="mt-1 text-sm">
              <span className="text-error">{tokenError}</span>
              {resendSuccess ? (
                <span className="px-1 text-secondary-fg">{t('resendSuccess')}</span>
              ) : (
                <span
                  className={`px-1 text-secondary-fg ${
                    isResending || resendCooldown > 0
                      ? 'cursor-not-allowed opacity-50'
                      : 'hover:cursor-pointer hover:text-foreground'
                  }`}
                  onClick={onResend}
                >
                  {isResending ? (
                    <>
                      <Loader2 className="mr-1 inline h-3 w-3 animate-spin" />
                      {t('resending')}
                    </>
                  ) : resendCooldown > 0 ? (
                    `${t('resend')} (${resendCooldown}s)`
                  ) : (
                    t('resend')
                  )}
                </span>
              )}
            </div>
            <div className="mt-4 flex gap-4">
              <Button
                variant="outline"
                style={{ width: '80px' }}
                className={`bg-white ${ElementClassMap.button}`}
                // @ts-expect-error typing
                onClick={onGoBack}
                disabled={isSubmitting || isNavigating}
              >
                {t('goBack')}
              </Button>
              <Button
                type="submit"
                className={cn(ElementClassMap.button, ElementClassMap.withBrand, '!w-0 flex-grow')}
                disabled={isSubmitting || isNavigating || !!tokenError || !formState.isValid}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('actionLoading')}
                  </>
                ) : isNavigating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('actionLoading')}
                  </>
                ) : (
                  t('action')
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </LoginContainer>
  );
}
