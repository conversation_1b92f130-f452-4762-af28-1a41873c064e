{
  // Cloudflare Workers 主配置
  "main": ".open-next/worker.js",
  "compatibility_date": "2025-07-01",
  "compatibility_flags": ["nodejs_compat", "global_fetch_strictly_public"],

  // 资产配置
  "assets": {
    "directory": ".open-next/assets",
    "binding": "ASSETS"
  },

  // 服务绑定
  "services": [
    {
      "binding": "WORKER_SELF_REFERENCE",
      "service": "yousite"
    }
  ],

  // 观察性配置
  "observability": {
    "logs": {
      "enabled": true
    }
  },

  // 环境配置
  "env": {
    // 预览环境配置
    "preview": {
      "name": "yousite-preview",
      "routes": [
        {
          "pattern": "preview.youmind.site",
          "custom_domain": true
        }
      ],
      "assets": {
        "directory": ".open-next/assets",
        "binding": "ASSETS"
      },
      "services": [
        {
          "binding": "WORKER_SELF_REFERENCE",
          "service": "yousite-preview"
        }
      ],
      "r2_buckets": [
        {
          "binding": "NEXT_INC_CACHE_R2_BUCKET",
          "bucket_name": "yousite-cached-pages-preview"
        }
      ],
      // 预览环境的 durable_objects 配置（使用标准绑定名称）
      "durable_objects": {
        "bindings": [
          {
            "name": "NEXT_CACHE_DO_QUEUE",
            "class_name": "DOQueueHandler"
          },
          // This is only required if you use On-demand revalidation
          {
            "name": "NEXT_TAG_CACHE_DO_SHARDED",
            "class_name": "DOShardedTagCache"
          },
          {
            "name": "NEXT_CACHE_DO_PURGE",
            "class_name": "BucketCachePurge"
          }
        ]
      }
    },

    // 生产环境配置
    "production": {
      "name": "yousite-production",
      "assets": {
        "directory": ".open-next/assets",
        "binding": "ASSETS"
      },
      "services": [
        {
          "binding": "WORKER_SELF_REFERENCE",
          "service": "yousite-production"
        }
      ],
      "r2_buckets": [
        {
          "binding": "NEXT_INC_CACHE_R2_BUCKET",
          "bucket_name": "yousite-cached-pages-production"
        }
      ],
      // 生产环境的 durable_objects 配置（使用标准绑定名称）
      "durable_objects": {
        "bindings": [
          {
            "name": "NEXT_CACHE_DO_QUEUE",
            "class_name": "DOQueueHandler"
          },
          // This is only required if you use On-demand revalidation
          {
            "name": "NEXT_TAG_CACHE_DO_SHARDED",
            "class_name": "DOShardedTagCache"
          },
          {
            "name": "NEXT_CACHE_DO_PURGE",
            "class_name": "BucketCachePurge"
          }
        ]
      }
    }
  },
  "migrations": [
    {
      "tag": "v1",
      "new_sqlite_classes": [
        "DOQueueHandler",
        // This is only required if you use On-demand revalidation
        "DOShardedTagCache",
        "BucketCachePurge"
      ]
    }
  ],
  "keep_names": false

  // 环境变量现在通过 Doppler 远端管理
  // 其他可选配置示例：
  // "kv_namespaces": [
  //   {
  //     "binding": "MY_KV_NAMESPACE",
  //     "id": "your-kv-namespace-id"
  //   }
  // ],
  // "d1_databases": [
  //   {
  //     "binding": "DB",
  //     "database_name": "your-database-name",
  //     "database_id": "your-database-id"
  //   }
  // ]
}
